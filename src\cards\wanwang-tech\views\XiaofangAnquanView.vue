<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('XiaofangAnquanView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>消防安全管理系统</h1>
    </div>

    <div class="content">
      <!-- 项目介绍 -->
      <div class="section intro-section">
        <div class="intro-card">
          <div class="intro-visual">
            <div class="intro-image">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiXiaoFang.png" alt="消防安全管理系统" />
            </div>
          </div>
          <div class="intro-content">
            <div class="intro-title">
              <h3>消防安全管理系统</h3>
              <div class="subtitle">九小场所"一户一码"精准管理</div>
              <div class="title-decoration"></div>
            </div>
            <div class="intro-description">
              <div class="description-item">
                <div class="item-icon">🏢</div>
                <p>"九小"场所是指小学校或幼儿园、小医院、小商店、小餐饮场所、小旅馆、小歌舞娱乐场所、小网吧、小美容洗浴场所、小生产加工企业的总称。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">⚠️</div>
                <p>"九小"场所因安全意识淡薄，安全措施不到位，存在严重的安全隐患问题，而且人员密集，一旦发生事故将造成重大人员伤亡。通过"一户一码"消防安全管理系统来有效管理企业消防安全信息，包括（场所基本情况，人员留宿情况、用火用电情况、消防设施情况、消防疏通情况、消防安全承诺、消防安全课堂等）。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">🛡️</div>
                <p>为了解决新形势下的消防安全问题，江西万网科技有限公司推出"消防安全管理系统"，为解决"九小"场所消防安全问题提供有力支持。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>主要功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <h3>前端数据采集</h3>
            <div class="feature-content">
              <p>通过移动端和PC端实现消防安全数据的实时采集，确保信息的准确性和时效性。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏘️</div>
            <h3>社区管理</h3>
            <div class="feature-content">
              <p>按社区进行分级管理，实现消防安全责任的层级化落实和精准化管控。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏪</div>
            <h3>场所分类管理</h3>
            <div class="feature-content">
              <p>针对九小场所的不同类型进行分类管理，制定针对性的安全管理措施。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏢</div>
            <h3>场所管理</h3>
            <div class="feature-content">
              <p>建立完整的场所档案，实现"一户一码"精准管理，全面掌握场所安全状况。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🚨</div>
            <h3>一键报警</h3>
            <div class="feature-content">
              <p>提供紧急情况下的一键报警功能，快速响应消防安全事件，保障人员安全。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <h3>用户管理</h3>
            <div class="feature-content">
              <p>多角色用户权限管理，确保不同层级用户的操作权限和数据安全。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 解决痛点 -->
      <div class="section problems-section">
        <div class="section-header">
          <h2>解决痛点</h2>
        </div>
        <div class="problems-grid">
          <div class="problem-item">
            <div class="problem-header">
              <div class="problem-icon">🏗️</div>
              <h3>建筑不规范，安全保障差</h3>
            </div>
            <div class="problem-content">
              <p>大多数"三合一"场所建筑未经消防审核、验收，甚至部分为违章搭建建筑，建筑内部装修、生产功能区、员工宿舍布置等大多由场所经营者自行安排，安全保障差。</p>
            </div>
          </div>
          <div class="problem-item">
            <div class="problem-header">
              <div class="problem-icon">🚪</div>
              <h3>消防设施不齐，逃生条件差</h3>
            </div>
            <div class="problem-content">
              <p>存在建筑耐火等级低、疏散逃生条件差、消防设施缺乏等隐患；场所人员密集，电线乱拉乱接；一旦发生火灾，很难组织起有效的灭火和逃生，容易导致人员伤亡。</p>
            </div>
          </div>
          <div class="problem-item">
            <div class="problem-header">
              <div class="problem-icon">📋</div>
              <h3>责任不落实，有效管理少</h3>
            </div>
            <div class="problem-content">
              <p>经营业主只考虑自身利益，消防安全责任意识淡薄，消防器材配备缺乏，没有制定相应的消防安全制度，没有落实相关责任，缺乏有效的监督管理。</p>
            </div>
          </div>
          <div class="problem-item">
            <div class="problem-header">
              <div class="problem-icon">🧠</div>
              <h3>消防意识不强，自救能力弱</h3>
            </div>
            <div class="problem-content">
              <p>普遍消防安全意识差，消防安全观念淡薄，缺少相应的消防培训，灭火和逃生常识缺乏，这在相当程度上制约了他们在发生火灾初期时的应急处置能力。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section scenario-section">
        <div class="section-header">
          <h2>应用场景</h2>
        </div>
        <div class="scenario-content">
          <div class="scenario-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/xiaofanganquan1.png" alt="应用场景图" />
          </div>
          <div class="scenario-description">
            <p>消防安全管理系统广泛应用于各类"九小"场所的消防安全管理，通过信息化手段实现精准管控，有效提升消防安全管理水平，保障人民群众生命财产安全。</p>
          </div>
        </div>
      </div>

      <!-- 系统优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>系统优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <h3>精准管理</h3>
            <p>"一户一码"管理模式，实现对每个场所的精准识别和个性化管理。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📱</div>
            <h3>移动便捷</h3>
            <p>支持移动端操作，随时随地进行消防安全检查和数据录入。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <h3>快速响应</h3>
            <p>一键报警功能，确保紧急情况下的快速响应和处置。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📊</div>
            <h3>数据分析</h3>
            <p>全面的数据统计分析，为消防安全决策提供科学依据。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔄</div>
            <h3>闭环管理</h3>
            <p>从隐患发现到整改完成的全流程闭环管理，确保问题得到有效解决。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🛡️</div>
            <h3>安全保障</h3>
            <p>多层次安全防护体系，全面保障消防安全管理的有效性。</p>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="value-content">
          <p>消防安全管理系统通过信息化手段，有效解决了"九小"场所消防安全管理中的痛点问题。系统实现了从传统的粗放式管理向精准化、智能化管理的转变，大大提升了消防安全管理的效率和质量。通过"一户一码"管理模式，每个场所都有了专属的数字身份，实现了消防安全信息的全面采集、实时监控和动态管理。系统不仅提高了消防安全隐患的发现和处置效率，更重要的是通过数据分析和预警机制，实现了从被动应对向主动预防的转变，为保障人民群众生命财产安全提供了强有力的技术支撑。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 项目介绍样式 */
.intro-card {
  background: linear-gradient(135deg, #fff0f0 0%, #ffe8e8 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(220, 53, 69, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #dc3545 0%, #c82333 100%);
}

.intro-visual {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
}

.intro-image {
  width: 300px;
  height: 200px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(220, 53, 69, 0.2);
  position: relative;
}

.intro-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.intro-content {
  text-align: center;
}

.intro-title {
  margin-bottom: 2rem;
}

.intro-title h3 {
  color: #dc3545;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.title-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #dc3545 0%, #c82333 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-description {
  display: grid;
  gap: 1.5rem;
  text-align: left;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #dc3545;
}

.item-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.description-item p {
  margin: 0;
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
}

/* 主要功能样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.feature-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 解决痛点样式 */
.problems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.problem-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.problem-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.problem-header {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
}

.problem-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.problem-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.problem-content {
  padding: 2rem;
}

.problem-content p {
  color: #333;
  line-height: 1.6;
  margin: 0;
  text-align: justify;
}

/* 应用场景样式 */
.scenario-content {
  text-align: center;
}

.scenario-image {
  margin-bottom: 2rem;
}

.scenario-image img {
  width: 100%;
  max-width: 800px;
  height: auto;
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.scenario-description p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 系统优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* 应用价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .intro-image {
    width: 250px;
    height: 150px;
  }

  .intro-description {
    gap: 1rem;
  }

  .description-item {
    padding: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .problems-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .problem-header {
    padding: 1rem 1.5rem;
  }

  .problem-content {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }

  .intro-title h3 {
    font-size: 1.5rem;
  }
}
</style>
