<script setup lang="ts">
import { ref } from 'vue'
import DigitalHuman from '../components/DigitalHuman.vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

const slogan = ref('您好！我是上饶市民营（个私）经济协会的AI宣传员！')
const pcBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/szr-pc.jpeg'
</script>

<template>
  <div class="home-container">
    <div class="digital-human-container">
      <div class="digital-human-wrapper">
        <DigitalHuman />
      </div>
      <div class="pc-background" :style="{ backgroundImage: `url(${pcBackgroundImage})` }"></div>
    </div>
    
    <div class="bottom-section">
      <div class="content-wrapper">
        <div class="slogan-container">
          <div class="slogan-wrapper">
            <h1 class="slogan">{{ slogan }}</h1>
            <div class="tech-line"></div>
          </div>
        </div>
        
        <div class="navigation-container">
          <NavigationButtons />
        </div>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.digital-human-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.digital-human-wrapper {
  width: 100%;
  height: 100%;
}

.pc-background {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.85) 15%, rgba(255, 255, 255, 0.95));
  z-index: 2;
  backdrop-filter: blur(5px);
  height: 53%; /* 增加手机端高度 */
}

.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-top: 1rem;
}

.slogan-container {
  padding: 1rem 1rem 0.5rem 1rem;
  display: flex;
  justify-content: center;
}

.slogan-wrapper {
  position: relative;
  max-width: 340px;
  padding-bottom: 0.5rem;
}

.tech-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #c41b21 30%, #c41b21 70%, transparent);
}

.slogan {
  font-size: 1.25rem;
  line-height: 1.5;
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

.navigation-container {
  padding: 1rem 1rem 2rem 1rem;
}

@media (min-width: 768px) {
  .bottom-section {
    height: 50%; /* 减小桌面端高度 */
  }
  
  .content-wrapper {
    justify-content: center;
    padding-bottom: 2rem; /* 底部留出空间 */
    padding-top: 1rem; /* 减少顶部空间，使内容往下移 */
  }
  
  .digital-human-container {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }
  
  .digital-human-wrapper {
    width: auto;
    height: 100%;
    min-height: 100vh;
    max-width: none;
    display: flex;
    justify-content: center;
  }
  
  .digital-human-wrapper :deep(img),
  .digital-human-wrapper :deep(video) {
    height: 100%;
    min-height: 100vh;
    width: auto;
    object-fit: cover;
    object-position: center;
  }
  
  .pc-background {
    display: block;
  }
  
  .slogan-wrapper {
    max-width: 700px; /* 增加PC端slogan的最大宽度，使其能容纳更多文字 */
    padding-bottom: 0.75rem;
  }

  .slogan {
    font-size: 1.75rem;
    background: linear-gradient(135deg, #c41b21, #f56565);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .slogan-container {
    padding: 0 1.5rem 0.5rem 1.5rem;
    margin-bottom: 1rem; /* 减少与导航按钮的间距 */
  }
  
  .navigation-container {
    padding: 0 1.5rem 0 1.5rem;
  }
}

@media (min-width: 1200px) {
  .bottom-section {
    height: 55%; /* 减小大屏幕设备高度 */
  }
  
  .content-wrapper {
    padding-bottom: 3rem; /* 大屏幕底部留出更多空间 */
    padding-top: 2rem; /* 减少大屏幕顶部空间 */
  }
  
  .slogan-container {
    margin-bottom: 1.5rem; /* 减少大屏幕的间距 */
  }
  
  .slogan {
    font-size: 2rem; /* 大屏幕增加字体大小 */
  }
  
  .slogan-wrapper {
    max-width: 800px; /* 在大屏幕上进一步增加宽度 */
  }
}
</style>
