<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { 
    model: 'FKM/N-3266', 
    outerDiameter: 'ø323', 
    innerDiameter: 'ø220', 
    height: '660',
    filterArea: '21'
  },
  { 
    model: 'FKM/N-3566', 
    outerDiameter: 'ø350', 
    innerDiameter: 'ø240', 
    height: '660',
    filterArea: '22'
  },
  { 
    model: 'FKM/N-3270', 
    outerDiameter: 'ø323', 
    innerDiameter: 'ø220', 
    height: '750',
    filterArea: '22'
  },
  { 
    model: 'FKM/N-3570', 
    outerDiameter: 'ø350', 
    innerDiameter: 'ø240', 
    height: '750',
    filterArea: '23'
  },
  { 
    model: 'FKM/N-32100', 
    outerDiameter: 'ø323', 
    innerDiameter: 'ø220', 
    height: '1000',
    filterArea: '28'
  },
  { 
    model: 'FKM/N-35100', 
    outerDiameter: 'ø350', 
    innerDiameter: 'ø240', 
    height: '1000',
    filterArea: '30'
  }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '环保木浆材料',
    description: '可降解',
    icon: '🌱'
  },
  {
    title: '阻燃处理',
    description: '安全可靠',
    icon: '🔥'
  },
  {
    title: '过滤效果好',
    description: '透气性佳',
    icon: '🌪️'
  },
  {
    title: '成本适中',
    description: '性价比高',
    icon: '💰'
  }
])

// 应用场景
const applications = reactive([
  {
    title: '除尘设备',
    description: '各类滤筒除尘器的核心过滤元件',
    icon: '🌪️'
  },
  {
    title: '空气净化',
    description: '工业空气净化系统的过滤组件',
    icon: '💨'
  },
  {
    title: '通风系统',
    description: '工厂车间通风除尘系统应用',
    icon: '🏭'
  },
  {
    title: '环保设备',
    description: '各种环保除尘设备的过滤配件',
    icon: '♻️'
  }
])

// 技术优势
const technicalAdvantages = reactive([
  {
    title: '多规格选择',
    description: '提供多种尺寸规格，满足不同设备的安装需求。',
    icon: '📏'
  },
  {
    title: '环保友好',
    description: '天然木浆纤维材料，环保可降解，符合绿色环保要求。',
    icon: '🌱'
  },
  {
    title: '成本效益',
    description: '价格合理，性能优异，具有良好的性价比。',
    icon: '💰'
  },
  {
    title: '适应性强',
    description: '适用于各种工况条件，应用范围广泛。',
    icon: '🔧'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>木浆纤维除尘滤筒</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/mujiang.jpg" alt="木浆纤维除尘滤筒" />
        </div>
        <div class="hero-content">
          <h2>木浆纤维除尘滤筒</h2>
          <p class="product-subtitle">采用阻燃木浆材料的环保滤筒，兼具过滤性能和安全性</p>
          <div class="product-intro">
            <p>阻燃木浆滤筒采用经过特殊处理的木浆纤维材料，具有良好的过滤性能和阻燃特性。</p>
            <p>环保可降解，同时保证了使用安全性，是绿色环保的过滤解决方案。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>木浆纤维滤筒通过其褶式结构和天然纤维材料对含尘气体进行过滤。</p>
          <p>当含尘气体通过滤筒时，粉尘颗粒被木浆纤维材料拦截和吸附，清洁气体从滤筒内部流出，实现高效的气固分离。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>外径<br/>(mm)</th>
                <th>内径<br/>(mm)</th>
                <th>高度<br/>(mm)</th>
                <th>过滤面积<br/>(m²)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.outerDiameter }}</td>
                <td>{{ spec.innerDiameter }}</td>
                <td>{{ spec.height }}</td>
                <td>{{ spec.filterArea }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications-grid">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <div class="app-icon">{{ app.icon }}</div>
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          技术优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in technicalAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases">
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/8-dmfc/202212091709212.jpg" alt="木浆纤维除尘滤筒工程案例" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品展示区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 产品特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.feature-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8rem;
}

.specs-table th {
  background: #f8fafc;
  padding: 0.75rem 0.5rem;
  text-align: center;
  font-weight: 600;
  color: #1e3470;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
  font-size: 0.75rem;
}

.specs-table td {
  padding: 0.75rem 0.5rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  white-space: nowrap;
  font-size: 0.8rem;
}

.specs-table tbody tr:hover {
  background: #f8fafc;
}

/* 应用场景网格 */
.applications-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 技术优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 工程案例样式 */
.project-cases {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.case-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  max-width: 600px;
  width: 100%;
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.case-item img {
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .specs-table {
    font-size: 0.85rem;
  }

  .specs-table th {
    font-size: 0.8rem;
    padding: 0.75rem;
  }

  .specs-table td {
    font-size: 0.85rem;
    padding: 0.75rem;
  }

  .applications-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .specs-table {
    font-size: 0.9rem;
  }

  .specs-table th {
    font-size: 0.85rem;
  }

  .specs-table td {
    font-size: 0.9rem;
  }
}
</style>
