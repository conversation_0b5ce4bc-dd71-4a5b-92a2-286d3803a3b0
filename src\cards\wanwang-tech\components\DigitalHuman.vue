<script setup lang="ts">

const mobileBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/SZR.jpeg'
</script>

<template>
  <div class="digital-human">
    <div class="mobile-background" :style="{ backgroundImage: `url(${mobileBackgroundImage})` }"></div>
  </div>
</template>

<style scoped>
.digital-human {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.mobile-background {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

@media (min-width: 768px) {
  .mobile-background {
    display: none;
  }
}
</style>
