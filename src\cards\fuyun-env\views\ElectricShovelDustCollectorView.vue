<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { model: 'FYDC-32', airflow: '40000', filterArea: '32', power: '2×22kW', application: 'PH-2300XPC' },
  { model: 'FYDC-33', airflow: '40000', filterArea: '33', power: '2×22kW', application: 'PH-2300XPC' },
  { model: 'FYDC-60', airflow: '60000', filterArea: '60', power: '2×30kW', application: 'PH-2800XPC' },
  { model: 'FYDC-48', airflow: '50000', filterArea: '48', power: '3×22kW', application: 'WK-20' },
  { model: 'FYDC-60', airflow: '60000', filterArea: '60', power: '3×22kW', application: 'WK-35' },
  { model: 'FYDC-72', airflow: '72000', filterArea: '72', power: '4×22kW', application: 'WK-55' }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '超低排放浓度',
    description: '电铲棚粉尘排放浓度≤10mg/m³，高于国家规定的≤30mg/m³的标准'
  },
  {
    title: '低噪音运行',
    description: '除尘器整体噪音≤75db(A)，瞬间噪音≤80db(A)，确保作业环境安静'
  },
  {
    title: '强劲风机压力',
    description: '风机压力≤1500pa，提供强大的吸尘动力，确保除尘效果'
  },
  {
    title: '低阻力节能',
    description: '设备运行阻力为500-800pa，初阻力<500pa，最高阻力为1200pa，运行阻力低，节能效果好'
  },
  {
    title: '高效过滤精度',
    description: '对于3μm以上粒径过滤效率可达99%，有效捕集超细微粉尘颗粒'
  },
  {
    title: '二级过滤设计',
    description: '进风口安装旋风式二级过滤器，提升了二级精密滤筒的使用寿命'
  }
])

// 产品优势
const productAdvantages = reactive([
  {
    title: '大风量处理',
    description: '专为大型电铲设备设计，处理风量可达72000m³/h，满足重型设备除尘需求。',
    icon: '💨'
  },
  {
    title: '高效过滤',
    description: '采用大面积滤筒设计，过滤面积最大可达72m²，确保高效除尘效果。',
    icon: '🌪️'
  },
  {
    title: '适应性强',
    description: '适用于PH-2300XPC、PH-2800XPC、WK系列等多种电铲型号。',
    icon: '🔧'
  },
  {
    title: '稳定可靠',
    description: '结构坚固，适应恶劣的矿山环境，设备运行稳定可靠。',
    icon: '🛡️'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
       ‹ 返回
      </button>
      <h1>电铲除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/9b6362ff-938d-4fb7-b7dc-df69f88666cd.jpg" alt="电铲除尘器" />
        </div>
        <div class="hero-content">
          <h2>电铲除尘器</h2>
          <p class="product-subtitle">专为大型电铲设备设计的高效除尘系统</p>
          <div class="product-intro">
            <p>电铲是露天矿山开采的重要设备，在作业过程中会产生大量粉尘，严重影响操作环境和设备性能。</p>
            <p>我们的电铲除尘器专为大型电铲设备设计，采用大风量、高效过滤技术，能够有效控制粉尘排放，保护操作人员健康，延长设备使用寿命。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>电铲除尘器通过强力风机产生负压，将电铲作业过程中产生的粉尘吸入除尘器内部。</p>
          <p>粉尘经过旋风式预分离器初步分离后，进入滤筒进行精细过滤，清洁空气经风机排出，收集的粉尘定期清理。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>处理风量<br/>(m³/h)</th>
                <th>过滤面积<br/>(m²)</th>
                <th>风机功率</th>
                <th>适用机型</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.filterArea }}</td>
                <td>{{ spec.power }}</td>
                <td>{{ spec.application }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in productAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications">
          <div class="application-item">
            <div class="app-icon">⛏️</div>
            <div class="content">
              <h4>露天矿山</h4>
              <p>大型露天矿山电铲作业粉尘治理</p>
            </div>
          </div>
          <div class="application-item">
            <div class="app-icon">🏭</div>
            <div class="content">
              <h4>采石场</h4>
              <p>采石场电铲设备粉尘控制</p>
            </div>
          </div>
          <div class="application-item">
            <div class="app-icon">🚧</div>
            <div class="content">
              <h4>土方工程</h4>
              <p>大型土方工程电铲除尘</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases">
          <div class="case-grid">
             <!-- 工程案例图片展示 -->
             <div class="case-item">
               <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/1-dcjpfc/img_202005071436291.jpg" alt="电铲除尘器工程案例3 - 除尘效果展示" />
             </div>
             <div class="case-item">
               <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/1-dcjpfc/IMG_20151230_092045.jpg" alt="电铲除尘器工程案例1 - 露天矿山现场安装" />
             </div>
            
             <div class="case-item">
               <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/1-dcjpfc/IMG_20151230_094126.jpg" alt="电铲除尘器工程案例2 - 设备运行状态" />
             </div>
             
             <div class="case-item">
               <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/1-dcjpfc/img_202107021433233.jpg" alt="电铲除尘器工程案例4 - 整体工程完成" />
             </div> 
             
           </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem; /* 减少顶部留白 */
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem; /* 减少底部留白 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品展示区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1rem; /* 减少底部间距 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 产品特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.85rem;
}

.specs-table th {
  background: #f8fafc;
  padding: 0.75rem;
  text-align: center;
  font-weight: 600;
  color: #1e3470;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
}

.specs-table td {
  padding: 0.75rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  white-space: nowrap;
}

.specs-table tbody tr:hover {
  background: #f8fafc;
}

/* 产品优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 应用场景 */
.applications {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem; /* 减少应用场景项目间距 */
}

.application-item {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem; /* 减少内边距 */
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-item .content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.application-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.application-item h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-item p {
  margin: 0;
  color: #4b5563;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .specs-table {
    font-size: 0.9rem;
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .applications {
    grid-template-columns: repeat(3, 1fr);
  }

  .application-item {
    flex-direction: column;
    text-align: center;
  }

  .application-item .content {
    align-items: center;
    text-align: center;
  }

  .app-icon {
    margin-right: 0;
    margin-bottom: 0.75rem;
  }

  /* 工程案例样式 - 桌面端 */
  .case-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 工程案例样式 */
.project-cases {
  margin-top: 1rem;
}

.case-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 1.5rem;
}

.case-item {
  background: #f8fafc;
  border-radius: 0.75rem;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 2px solid transparent;
  display: flex; /* 使用flex布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  min-height: 250px; /* 增加高度适应两行两列布局 */
}

.case-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.case-item img {
  max-width: 100%; /* 最大宽度不超过容器 */
  max-height: 220px; /* 增加最大高度适应更大的容器 */
  object-fit: contain; /* 完整显示图片，不裁剪 */
  display: block;
  border-radius: 0.5rem; /* 给图片添加圆角 */
}

/* 移动端工程案例样式 */
@media (max-width: 767px) {
  .case-grid {
    grid-template-columns: 1fr; /* 移动端保持单列布局 */
    grid-template-rows: auto; /* 移动端自动行高 */
    gap: 1rem; /* 移动端使用较小间距 */
  }
  
  .case-item img {
    height: 180px;
  }
}
</style>
