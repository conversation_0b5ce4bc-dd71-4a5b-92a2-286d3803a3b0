<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

// 系统截图数据
const systemImages = [
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanjiayiyuan11.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanjiayiyuan12.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanjiayiyuan13.jpg'
]

const currentImageIndex = ref(0)

const nextImage = () => {
  currentImageIndex.value = (currentImageIndex.value + 1) % systemImages.length
}

const prevImage = () => {
  currentImageIndex.value = currentImageIndex.value === 0 ? systemImages.length - 1 : currentImageIndex.value - 1
}

const goToImage = (index: number) => {
  currentImageIndex.value = index
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('SanjiaYiyuanView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>三甲医院门户网站管理系统</h1>
    </div>

    <div class="content">
      <!-- 产品概述 -->
      <div class="section intro-section">
        <div class="intro-card">
          <div class="intro-content">
            <div class="intro-title">
              <h3>三甲医院门户网站管理系统</h3>
              <div class="title-decoration"></div>
            </div>
            <div class="intro-description">
              <div class="description-item">
                <div class="item-icon">🏥</div>
                <p>江西万网科技有限公司针对三甲医院需求，经过深入研究与技术开发，推出了完善的三甲医院门户网站管理系统。该系统旨在满足医院全方位的信息展示与互动需求，提供从医院概况、科室介绍到就医指南等一站式信息服务，提升医院的形象和服务品质。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统截图展示 -->
      <div class="section gallery-section">
        <div class="section-header">
          <h2>系统功能展示</h2>
        </div>
        <div class="carousel-container">
          <div class="carousel-wrapper">
            <div class="carousel-main">
              <img 
                :src="systemImages[currentImageIndex]" 
                :alt="`三甲医院门户网站管理系统截图 ${currentImageIndex + 1}`"
                class="carousel-image"
              />
              <button @click="prevImage" class="carousel-btn prev-btn">‹</button>
              <button @click="nextImage" class="carousel-btn next-btn">›</button>
            </div>
            <div class="carousel-thumbnails">
              <div 
                v-for="(image, index) in systemImages" 
                :key="index"
                @click="goToImage(index)"
                :class="['thumbnail', { active: index === currentImageIndex }]"
              >
                <img :src="image" :alt="`缩略图 ${index + 1}`" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能特点 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>功能特点</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🏥</div>
            <h3>医院概况系统</h3>
            <div class="feature-content">
              <p>全面展示医院的基本信息，包括医院简介、文化、设备、环境等，帮助用户了解医院的整体情况。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏢</div>
            <h3>科室介绍系统</h3>
            <div class="feature-content">
              <p>详细介绍医院的门诊、临床科室和医技科室，方便用户了解医院的科室设置和医疗项目。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">👨‍⚕️</div>
            <h3>专家介绍系统</h3>
            <div class="feature-content">
              <p>展示医院门诊部和住院部的专家信息，便于患者预约挂号和选择合适的医生。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📰</div>
            <h3>医院动态系统</h3>
            <div class="feature-content">
              <p>实时发布医院的公告、动态和媒体新闻，提供最新医疗资讯，增强用户对医院的关注度。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔬</div>
            <h3>教学科研系统</h3>
            <div class="feature-content">
              <p>展示医院的科研管理和教学管理信息，体现医院的学术水平和科研实力。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📋</div>
            <h3>院政务公开系统</h3>
            <div class="feature-content">
              <p>向社会、患者和职工公开相关信息，包括政策法规和医疗价格公示，增强医院的透明度和信任度。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏛️</div>
            <h3>党群工作系统</h3>
            <div class="feature-content">
              <p>提供党建园地、统战工作和纪检监察信息，体现医院的党群建设成果。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🗺️</div>
            <h3>就医指南系统</h3>
            <div class="feature-content">
              <p>提供便民指南、地理交通图、就医须知等信息，帮助患者更好地了解就医流程和注意事项。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📝</div>
            <h3>医院专题系统</h3>
            <div class="feature-content">
              <p>围绕特定主题如党的基层组织、红包问题整治等，发布相关专题信息。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎭</div>
            <h3>医院文化系统</h3>
            <div class="feature-content">
              <p>宣传医院理念、文化学习等内容，展示医院的文化底蕴和精神风貌。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">⚙️</div>
            <h3>系统模块</h3>
            <div class="feature-content">
              <p>提供门户设置、系统设置等功能，方便管理员进行系统的个性化配置和日常维护。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔐</div>
            <h3>权限管理</h3>
            <div class="feature-content">
              <p>支持管理员和角色管理，确保不同角色的用户只能访问其对应权限的内容。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>产品优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">🌟</div>
            <h3>功能全面</h3>
            <p>覆盖医院各领域的信息展示与互动需求，提升医院形象和服务品质。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <h3>个性化定制</h3>
            <p>根据客户需求进行个性化定制，满足不同医院的特定需求。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">👆</div>
            <h3>操作简便</h3>
            <p>界面友好，操作简单，方便管理员和用户快速上手。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔒</div>
            <h3>安全可靠</h3>
            <p>采用先进的安全技术和防护措施，确保系统的稳定性和数据的安全性。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔄</div>
            <h3>实时更新</h3>
            <p>支持实时发布和更新各类信息，确保信息的及时性和准确性。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📊</div>
            <h3>数据分析</h3>
            <p>提供数据分析功能，帮助医院更好地了解用户需求和市场动态。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🤝</div>
            <h3>客户支持</h3>
            <p>提供全方位的客户支持和服务，包括技术咨询、培训和售后服务等。</p>
          </div>
        </div>
      </div>

      <!-- 服务支持 -->
      <div class="section service-section">
        <div class="section-header">
          <h2>服务支持</h2>
        </div>
        <div class="service-grid">
          <div class="service-item">
            <div class="service-header">
              <div class="service-icon">🔧</div>
              <h3>技术支持</h3>
            </div>
            <div class="service-content">
              <p>为客户提供系统的安装、调试及日常维护等服务，确保系统的稳定运行。</p>
            </div>
          </div>
          <div class="service-item">
            <div class="service-header">
              <div class="service-icon">🎓</div>
              <h3>培训服务</h3>
            </div>
            <div class="service-content">
              <p>根据客户需求，提供系统使用的培训服务，帮助客户更快地掌握系统的使用方法。</p>
            </div>
          </div>
          <div class="service-item">
            <div class="service-header">
              <div class="service-icon">🛠️</div>
              <h3>定制开发</h3>
            </div>
            <div class="service-content">
              <p>根据客户的实际需求，提供系统的定制化开发服务，满足客户的特定需求。</p>
            </div>
          </div>
          <div class="service-item">
            <div class="service-header">
              <div class="service-icon">🔄</div>
              <h3>售后服务</h3>
            </div>
            <div class="service-content">
              <p>为客户提供长期的售后服务，包括系统升级、故障排除等，确保客户能够享受到持续的服务支持。</p>
            </div>
          </div>
          <div class="service-item">
            <div class="service-header">
              <div class="service-icon">📈</div>
              <h3>数据分析服务</h3>
            </div>
            <div class="service-content">
              <p>提供专业的数据分析服务，帮助客户更好地了解用户需求和市场动态，为医院的决策提供有力支持。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 网站案例展示 -->
      <div class="section cases-section">
        <div class="section-header">
          <h2>网站案例展示</h2>
        </div>
        <div class="cases-content">
          <p>我们服务的医院网站客户案例包括：</p>
          <div class="cases-list">
            <div class="case-item">上饶市人民医院</div>
            <div class="case-item">上饶市第三人民医院</div>
            <div class="case-item">玉山县中医院</div>
            <div class="case-item">广丰区中医院</div>
            <div class="case-item">上饶口腔医院</div>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="value-content">
          <p>三甲医院门户网站管理系统，作为一款专为三甲医院设计的门户网站管理系统，具有功能全面、操作简便、安全可靠等优势，它能满足三甲医院全方位的信息展示与互动需求，提升医院形象和服务品质。通过该系统，医院可以更加便捷地进行信息发布和管理工作，向患者和社会展示医院的综合实力和服务水平。我们为客户提供全方位的服务支持，确保客户能够享受到优质的产品和服务体验。选择三甲医院门户网站管理系统是提升医院信息化水平和服务质量的重要保障。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 产品介绍样式 */
.intro-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(76, 175, 80, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%);
}

.intro-content {
  text-align: center;
}

.intro-title {
  margin-bottom: 2rem;
}

.intro-title h3 {
  color: #388e3c;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.title-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-description {
  display: grid;
  gap: 1.5rem;
  text-align: left;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #4caf50;
}

.item-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.description-item p {
  margin: 0;
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
}

/* 轮播图样式 */
.carousel-container {
  max-width: 100%;
  margin: 0 auto;
}

.carousel-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.carousel-main {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.prev-btn {
  left: 10px;
}

.next-btn {
  right: 10px;
}

.carousel-thumbnails {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.thumbnail {
  width: 120px;
  height: 80px;
  border-radius: 0.5rem;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.thumbnail:hover {
  border-color: #1693d2;
  transform: scale(1.05);
}

.thumbnail.active {
  border-color: #1693d2;
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 功能特点样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.8rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.feature-content p {
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 产品优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* 服务支持样式 */
.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.service-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.service-header {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  padding: 1.2rem 1.5rem;
  display: flex;
  align-items: center;
}

.service-icon {
  font-size: 1.3rem;
  margin-right: 0.8rem;
}

.service-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.service-content {
  padding: 1.5rem;
}

.service-content p {
  color: #333;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
  text-align: justify;
}

/* 网站案例展示样式 */
.cases-content {
  text-align: center;
}

.cases-content > p {
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.cases-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.case-item {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  padding: 1rem 2rem;
  border-radius: 2rem;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

/* 应用价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .intro-description {
    gap: 1rem;
  }

  .description-item {
    padding: 1rem;
  }

  .carousel-main {
    height: 250px;
  }

  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .thumbnail {
    width: 80px;
    height: 60px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .service-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .service-header {
    padding: 1rem 1.2rem;
  }

  .service-content {
    padding: 1.2rem;
  }

  .cases-list {
    flex-direction: column;
    align-items: center;
  }

  .case-item {
    padding: 0.8rem 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }

  .intro-title h3 {
    font-size: 1.5rem;
  }
}
</style>
