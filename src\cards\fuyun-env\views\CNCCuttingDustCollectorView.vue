<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { model: 'FQG1-4000', airflow: '4000', power: '5.5', diameter: 'Ø250', dimensions: '900×900×2850' },
  { model: 'FQG1-6000', airflow: '6000', power: '7.5', diameter: 'Ø300', dimensions: '1450×900×2850' },
  { model: 'FQG1-8000', airflow: '8000', power: '11', diameter: 'Ø350', dimensions: '1450×1200×2850' },
  { model: 'FQG2-4000', airflow: '4000', power: '5.5', diameter: 'Ø250', dimensions: '1400×1300×2450' },
  { model: 'FQG2-6000', airflow: '6000', power: '7.5', diameter: 'Ø300', dimensions: '1400×1300×2850' },
  { model: 'FQG2-8000', airflow: '8000', power: '11', diameter: 'Ø350', dimensions: '1400×1460×2690' }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '高效过滤',
    description: '采用高效过滤器，过滤效率达99.9%以上'
  },
  {
    title: '智能控制',
    description: '配备智能控制系统，可根据工况自动调节风量和清灰频率'
  },
  {
    title: '节能环保',
    description: '低能耗设计，符合环保要求'
  },
  {
    title: '过滤精度',
    description: '可有效过滤0.3微米以上粉尘，过滤效率达99.9%以上'
  },
  {
    title: '结构紧凑',
    description: '占地面积小，安装维护方便，适合各种工作环境'
  },
  {
    title: '运行稳定',
    description: '设备运行稳定可靠，故障率低'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>数控切削除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/bb887e2d-4853-4baa-aa28-537a1fd5f903.jpg" alt="数控切削除尘器" />
        </div>
        <div class="hero-content">
          <h2>数控切削除尘器</h2>
          <p class="product-subtitle">高效净化切削粉尘</p>
          <div class="product-intro">
            <p>专为数控机床切削加工过程中产生的金属粉尘、油雾等污染物设计的高效除尘设备。</p>
            <p>采用先进的过滤技术和智能控制系统，能够有效收集和过滤各种金属切削粉尘，保护操作人员健康，改善车间环境。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>设备通过强力风机产生负压，将切削过程中产生的金属粉尘、油雾等污染物吸入除尘器内部。</p>
          <p>污染物经过预过滤器初步分离后，进入高效过滤器进行精细过滤，清洁空气经风机排出，收集的粉尘定期清理。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>处理风量<br/>(m³/h)</th>
                <th>功率<br/>(kW)</th>
                <th>管径<br/>(mm)</th>
                <th>设备体积<br/>(L×W×H mm)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.power }}</td>
                <td>{{ spec.diameter }}</td>
                <td class="dimensions-cell">{{ spec.dimensions }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications">
          <div class="application-item">
            <div class="app-icon">🏭</div>
            <h4>数控机床</h4>
            <p>各类数控车床、铣床切削粉尘处理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">⚙️</div>
            <h4>机械加工</h4>
            <p>金属切削、打磨等加工粉尘处理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🔧</div>
            <h4>精密制造</h4>
            <p>精密零件加工环境净化</p>
          </div>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🌪️</div>
            <div class="indicator-content">
              <h4>除尘效率</h4>
              <div class="indicator-value">99.9%+</div>
              <p>进口覆膜滤料</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔧</div>
            <div class="indicator-content">
              <h4>结构设计</h4>
              <div class="indicator-value">紧凑型</div>
              <p>一体式和分体式</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🛡️</div>
            <div class="indicator-content">
              <h4>安全防护</h4>
              <div class="indicator-value">火花预处理</div>
              <p>内置安全装置</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">💨</div>
            <div class="indicator-content">
              <h4>处理风量</h4>
              <div class="indicator-value">4000-8000</div>
              <p>m³/h大范围覆盖</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔄</div>
            <div class="indicator-content">
              <h4>过滤方式</h4>
              <div class="indicator-value">二级过滤</div>
              <p>初级+精密过滤</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📏</div>
            <div class="indicator-content">
              <h4>安装方式</h4>
              <div class="indicator-value">即插即用</div>
              <p>水平/竖直安装</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 案例工程 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          工程案例
        </h3>
        <div class="project-cases-grid">
          <div class="case-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/6-jgcc/1716954306882.jpg" alt="数控切削除尘器工程案例1" />
          </div>
          <div class="case-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/6-jgcc/202109261144443.jpg" alt="数控切削除尘器工程案例2" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.3;
  font-size: 0.7rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.7rem;
  min-width: 800px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.75rem 0.5rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.65rem;
  line-height: 1.2;
}

.specs-table td {
  padding: 0.75rem 0.5rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.7rem;
}

.dimensions-cell {
  font-size: 0.6rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

.specs-table tbody tr:last-child td {
  border-bottom: none;
}

/* 应用场景 */
.applications {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.application-item {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
}

.application-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .feature-card p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .specs-table {
    font-size: 0.8rem;
  }

  .specs-table th {
    padding: 1rem 0.75rem;
    font-size: 0.75rem;
  }

  .specs-table td {
    padding: 1rem 0.75rem;
    font-size: 0.8rem;
  }

  .dimensions-cell {
    font-size: 0.7rem;
  }

  .applications {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .application-item {
    padding: 1.5rem;
  }

  .application-item h4 {
    font-size: 1.1rem;
  }

  .application-item p {
    font-size: 0.9rem;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.85rem;
  }

  .specs-table th {
    font-size: 0.8rem;
  }

  .specs-table td {
    font-size: 0.85rem;
  }

  .dimensions-cell {
    font-size: 0.75rem;
  }

  .applications {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  /* 案例工程网格样式 */
  .project-cases-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .case-image {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
  }

  .case-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }

  .case-image img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain;
  }
}

/* 移动端案例工程样式 */
@media (max-width: 767px) {
  .project-cases-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .case-image {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
  }

  .case-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }

  .case-image img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain;
  }
}
</style>