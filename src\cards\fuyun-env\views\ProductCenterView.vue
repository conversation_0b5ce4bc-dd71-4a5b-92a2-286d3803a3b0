<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 类型定义
interface ProductData {
  id: number
  title: string
  image: string
  summary: string
  description?: string
  features?: string[]
  applications?: string[]
}

const goBack = () => {
  // 返回到主页
  window.location.href = '/card/fuyun-env'
}

// 产品详情页面导航
const goToProductDetail = (productId: number) => {
  if (productId === 1) {
    // 电铲除尘器详情页
    window.location.href = '/card/fuyun-env/product/electric-shovel-dust-collector'
  } else if (productId === 2) {
    // 自洁式滤筒除尘器详情页
    window.location.href = '/card/fuyun-env/product/self-cleaning-filter-dust-collector'
  } else if (productId === 4) {
    // 集成式焊接烟尘除尘器详情页
    window.location.href = '/card/fuyun-env/product/integrated-welding-fume-dust-collector'
  } else if (productId === 3) {
    // 数控(激光、等离子)切割除尘器详情页
    window.location.href = '/card/fuyun-env/product/cnc-cutting-dust-collector'
  } else if (productId === 6) {
    // 钻机除尘器详情页
    window.location.href = '/card/fuyun-env/product/drilling-rig-dust-collector'
  } else if (productId === 7) {
    // 木工除尘器详情页
    window.location.href = '/card/fuyun-env/product/woodworking-dust-collector'
  } else if (productId === 5) {
    // 焊接烟尘净化器详情页
    window.location.href = '/card/fuyun-env/product/welding-fume-purifier'
  } else if (productId === 8) {
    // 脉冲单机布袋除尘器详情页
    window.location.href = '/card/fuyun-env/product/pulse-bag-dust-collector'
  } else if (productId === 9) {
    // 长袋离线脉冲袋式除尘器详情页
    window.location.href = '/card/fuyun-env/product/long-bag-offline-dust-collector'
  } else if (productId === 10) {
    // 旋风除尘器详情页
    window.location.href = '/card/fuyun-env/product/cyclone-dust-collector'
  } else if (productId === 11) {
    // 组合式空调机组净化器详情页
    window.location.href = '/card/fuyun-env/product/air-conditioning-purifier'
  } else if (productId === 12) {
    // 活性炭-UV光氧废气净化器详情页
    window.location.href = '/card/fuyun-env/product/activated-carbon-uv-purifier'
  } else if (productId === 13) {
    // VOCs催化燃烧系统详情页
    window.location.href = '/card/fuyun-env/product/vocs-catalytic-combustion'
  } else if (productId === 16) {
    // 木浆纤维除尘滤筒详情页
    window.location.href = '/card/fuyun-env/product/wood-pulp-fiber-filter-cartridge'
  } else if (productId === 20) {
    // 初中效(袋式)过滤器详情页
    window.location.href = '/card/fuyun-env/product/primary-medium-efficiency-bag-filter'
  } else if (productId === 21) {
    // 聚酯纤维除尘滤筒详情页
    window.location.href = '/card/fuyun-env/product/polyester-fiber-filter-cartridge'
  } else if (productId === 26) {
    // 打磨除尘器详情页
    window.location.href = '/card/fuyun-env/product/grinding-dust-collector'
  } else if (productId === 27) {
    // 高效过滤器详情页
    window.location.href = '/card/fuyun-env/product/high-efficiency-filter'
  } else if (productId === 28) {
    // 板式初效过滤器详情页
    window.location.href = '/card/fuyun-env/product/plate-primary-filter'
  } else if (productId === 29) {
    // 除尘布袋详情页
    window.location.href = '/card/fuyun-env/product/dust-filter-bag'
  } else if (productId === 30) {
    // 除尘布袋骨架详情页
    window.location.href = '/card/fuyun-env/product/dust-bag-cage'
  } else if (productId === 23) {
    // 环保项目运维详情页
    window.location.href = '/card/fuyun-env/service/environmental-operation-maintenance'
  } else if (productId === 24) {
    // 环保工程安装详情页
    window.location.href = '/card/fuyun-env/service/environmental-engineering-installation'
  } else if (productId === 25) {
    // 粉尘与废气综合治理设计方案详情页
    window.location.href = '/card/fuyun-env/service/dust-waste-gas-treatment-design'
  } else {
    // 其他产品暂时显示提示
    alert('该产品详情页面正在开发中...')
  }
}

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 产品数据
const products = reactive<ProductData[]>([
  {
    id: 1,
    title: '电铲除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/dianchuchan.jpg',
    summary: '专为电铲作业设计的高效除尘设备，有效控制粉尘污染，保护作业环境。',
    description: '电铲在工作过程中将产生大量的热量，需要从外部环境中引入冷空气进行冷却。由于现场工况环境恶劣，粉尘量大，现有的惯性过滤器过滤效率低，过滤器体积小，纳污量小，导致滤芯过早堵塞，降低了输入机体的风量，致使机械室温度较高，由于滤芯是粗过滤器，较多的粉尘被带入机体，进而损坏电气元件，增加了电路故障。福运生产的自动除尘装置包括风机、过滤器总成、反吹装置、排尘装置，安装于电铲尾部平台，具有过滤净化空气、自动反吹滤芯及自动排尘功能。过滤系统运行时将室外含有粉尘的污浊空气过滤净化后送入电铲机械室内，保证机械室内洁净的同时使机械室内温度下降为正常温度，有利于电气设备的正常工作，延长电气设备的使用寿命，降低运营成本，增加运营利润等有益效果。',
    features: [
      '电铲机棚粉尘排放浓度≤10mg/m³，高于国家规定的≤20mg/m³的标准值',
      '除尘器整体噪音<75db(A)，瞬间噪音<80db(A)',
      '风机压力≥2500pa',
      '设备运行阻力为500-800pa，初阻力<500pa，最高阻力为1200pa，运行阻力低，节能效果好',
      '对于3μm以上粒径过滤效率可达99%',
      '入风口安装旋风式初级过滤器，提升了二级精密滤芯的使用寿命'
    ],
    applications: [
      '矿山开采电铲作业',
      '建筑工地土方作业',
      '港口码头装卸作业',
      '大型工程机械配套'
    ]
  },
  {
    id: 2,
    title: '自洁式滤筒除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/zijieshi.jpg',
    summary: 'FKZJ脉冲反吹自洁式空气过滤器，专为空气动力设备设计的高效过滤系统。',
    description: '空气动力设备吸入含灰尘的空气之后将造成设备的非正常磨损，吸入的灰尘将在转子表面结垢使设备转动平衡精度下降，同时下游管道设备受蚀受损。福运公司生产的FKZJ脉冲反吹自洁式空气过滤器解决了上述实际问题。因此FKZJ型系列电脑自洁式空气过滤器是各种压缩机、风机空气入口净化过滤器必不可少的配套产品。',
    features: [
      '抗恶劣环境能力强，能满足酸碱环境、近海地区多雾及梅雨季节的低区使用',
      '工作阻力小，一般在250-600pa之间，能满足进口及国产空压机入口空气过滤要求',
      '可与中央控制室连锁控制，三种自洁类型：时序自洁、压差自洁、手动自洁',
      '环保节能、脉冲自洁耗气量低'
    ],
    applications: [
      '制氧厂空分设备过滤进气',
      '燃气轮机的进气过滤',
      '钢铁冶炼高炉鼓风',
      '空调新风过滤',
      '化工、制药、食品等行业'
    ]
  },
  {
    id: 3,
    title: '数控(激光、等离子)切割除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/shukong.jpg',
    summary: '专为数控切割和激光加工设计的精密除尘系统，确保加工精度和环境清洁。',
    description: '专门为数控切割和激光加工设备配套的高精度除尘系统。采用先进的过滤技术和精确的风量控制，能够有效收集切割过程中产生的金属粉尘和烟雾，保证加工精度和操作环境。',
    features: [
      '精确风量控制，不影响切割精度',
      '高效过滤，处理金属粉尘和烟雾',
      '自洁式设计，减少维护频率',
      '智能联动，与切割设备同步启停'
    ],
    applications: [
      '数控切割车间',
      '激光加工中心',
      '金属制品加工',
      '精密机械制造'
    ]
  },
  {
    id: 4,
    title: '集成式焊接烟尘除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/jichengshi.jpg',
    summary: '采用沉流装置和滤芯过滤的集成式焊接烟尘处理系统，适用于工厂局部或整体烟尘治理。',
    description: '通过风机产生的负压气流经除尘器进口进入沉流装置，粉尘颗粒与导板碰撞后、大的颗粒首先沉降，含细小颗粒的混合粉尘在除尘室内通过滤芯的过滤分离作用完成。粉尘在穿过滤芯时，则被滤芯阻拦在其表面上，当被阻拦的粉尘在滤芯表面不断沉积时，滤芯里外的压差也同时不断加大，当压差达到预先设定值时，控制压缩空气的电磁阀被打开，使气包内的压缩空气由喷吹管孔眼喷出，通过文氏管诱导数倍于一次风的周围空气，瞬间喷向滤芯内表面，使得沉积在滤芯上的粉尘颗粒在高压气流的作用下脱离滤芯表面落入灰桶中，使得整个滤芯表面都得到清扫。',
    features: [
      '设计灵活通过，各种组合布置，适用于工厂局部或整个工厂烟尘及粉尘治理',
      '除尘器结构设计紧凑，在处理相同风量的情况下，节省占地空间只相当于滤袋除尘器的1/3',
      '吸尘罩结构新颖，灵活多变，可设置全移动式、手动式、固定式等，捕捉烟尘能力强',
      '褶式滤筒的过滤面积可比传统滤袋高300%，安装简便。进口纳米覆膜滤料表面过滤性能好，使除尘效率达到99.99%以上',
      '斜插式滤筒设计，维修及更换滤筒更简便、更快捷、更安全，大大缩短了停机时间',
      '运行费用低、运行阻力低、能耗与运行成本极低。通常初阻力为25mm水柱，正常运行阻力为50~75mm水柱'
    ],
    applications: [
      '大型焊接车间',
      '造船厂焊接作业',
      '钢结构制造',
      '重工业焊接生产线'
    ]
  },
  {
    id: 5,
    title: '焊接烟尘净化器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/hanjieyan.jpg',
    summary: '专业焊接烟尘净化设备，高效过滤焊接产生的有害烟雾和颗粒物。',
    description: '焊接烟尘净化器专门用于处理焊接作业过程中产生的烟尘和有害气体。采用多级过滤技术，能够有效去除焊接烟雾中的金属颗粒和有害物质，保护操作人员健康，改善工作环境。',
    features: [
      '多级过滤系统，高效净化烟尘',
      '移动式设计，灵活便捷',
      '低噪音运行，不影响作业',
      '智能控制，自动启停'
    ],
    applications: [
      '焊接车间',
      '维修作业',
      '小型加工厂',
      '实验室焊接'
    ]
  },
  {
    id: 6,
    title: '钻机除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/zuanji.jpg',
    summary: '专为钻机作业设计的除尘设备，有效控制钻孔过程中的粉尘扩散。',
    description: '矿山采矿场使用的YZ-35型牙轮钻机，其室内通风采用轴流通风机加惯性过滤器组合形式，由于采区粉尘大，惯性过滤器过滤效率低，给钻机室内的电器设备及机械设备带来极大的使用隐患，且惯性过滤器容易堵塞，不易清洁，造成室内风量减小，室内正压不足，外部粉尘进入室内，污染室内环境，同时由于风量的减小，室内散热不畅，导致设备温度升高。为改善这一状况，福运设计的过滤除尘系统采用更大流量的风机，滤筒式除尘器，且具有反吹功能，满足了室内风量补充的同时，保证了室内空气的清洁度，提高了钻机运行的可靠性，降低了综合运营成本，且滤芯更换保养方便，现场适用性较强。',
    features: [
      '钻机棚粉尘排放浓度≤10mg/m³，高于国家规定的≤20mg/m³的标准值',
      '除尘器整体噪音<75db(A)，瞬间噪音<80db(A)',
      '风机压力≥500pa，提供充足动力',
      '设备运行阻力为200-500pa，初阻力<200pa，最高阻力为600pa，运行阻力低，节能效果好',
      '对于3μm以上粒径过滤效率可达99%',
      '入风口安装旋风式初级过滤器或百叶窗，提升了二级精密滤芯的使用寿命'
    ],
    applications: [
      '建筑工地钻孔',
      '地质勘探',
      '矿山开采',
      '基础设施建设'
    ]
  },
  {
    id: 7,
    title: '木工除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/mugong.jpg',
    summary: '专业木工粉尘收集设备，保护木工作业环境，提高工作效率。',
    description: '木工除尘器专门为木材加工行业设计，能够有效收集和处理木工作业过程中产生的木屑、锯末和粉尘。采用专业的过滤技术，确保工作环境清洁，保护操作人员健康。',
    features: [
      '专业木工粉尘处理',
      '大容量收集仓',
      '防火安全设计',
      '低噪音运行'
    ],
    applications: [
      '木工车间',
      '家具制造',
      '装修施工',
      '木材加工厂'
    ]
  },
  {
    id: 8,
    title: '脉冲单机布袋除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/maichong.jpg',
    summary: '高效脉冲清灰布袋除尘器，适用于各种工业粉尘处理场合。',
    description: '脉冲单机布袋除尘器采用先进的脉冲喷吹清灰技术，具有清灰效果好、滤袋使用寿命长、维护简便等特点。适用于各种工业生产过程中的粉尘收集和处理。',
    features: [
      '脉冲喷吹清灰，效果显著',
      '布袋使用寿命长',
      '结构紧凑，占地面积小',
      '维护简便，运行可靠'
    ],
    applications: [
      '水泥厂',
      '钢铁厂',
      '化工厂',
      '粉料加工'
    ]
  },
  {
    id: 9,
    title: '长袋离线脉冲袋式除尘器（离线式）',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/changdailixian.jpg',
    summary: '大型离线式脉冲袋式除尘器，适用于大风量、高浓度粉尘处理。',
    description: '长袋离线脉冲袋式除尘器采用离线清灰技术，能够在不停机的情况下进行清灰维护。适用于处理大风量、高浓度的工业粉尘，具有除尘效率高、运行稳定的特点。',
    features: [
      '离线清灰，不停机维护',
      '处理风量大，适应性强',
      '除尘效率高达99.9%',
      '自动化程度高'
    ],
    applications: [
      '大型工业企业',
      '电力行业',
      '冶金行业',
      '建材行业'
    ]
  },
  {
    id: 10,
    title: '旋风除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/xuanfeng.jpg',
    summary: '利用离心力分离粉尘的高效除尘设备，结构简单，维护方便。',
    description: '旋风除尘器利用气流的旋转运动产生的离心力来分离粉尘颗粒。结构简单、制造成本低、维护方便，特别适用于处理较粗颗粒的粉尘，是工业除尘的经典设备。',
    features: [
      '结构简单，制造成本低',
      '无运动部件，维护方便',
      '适用于高温高压环境',
      '处理粗颗粒粉尘效果好'
    ],
    applications: [
      '粮食加工',
      '木材加工',
      '矿物加工',
      '化工生产'
    ]
  },
  {
    id: 11,
    title: '组合式空调机组净化器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/zuheshi.jpg',
    summary: '集成式空气净化系统，提供洁净舒适的室内环境。',
    description: '组合式空调机组净化器将空调功能与空气净化功能完美结合，采用多级过滤技术，能够有效去除空气中的粉尘、细菌、病毒等污染物，为室内提供洁净舒适的空气环境。',
    features: [
      '空调与净化一体化设计',
      '多级过滤，净化效果好',
      '智能控制，节能环保',
      '模块化设计，便于维护'
    ],
    applications: [
      '医院手术室',
      '实验室',
      '电子厂房',
      '食品加工车间'
    ]
  },
  {
    id: 12,
    title: '活性炭-UV光氧废气净化器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/huoxingtan.jpg',
    summary: '结合活性炭吸附和UV光氧技术的复合式废气净化设备。',
    description: '活性炭-UV光氧废气净化器采用活性炭吸附和UV光氧催化相结合的技术，能够高效处理各种有机废气。活性炭负责吸附，UV光氧负责分解，双重净化确保废气达标排放。',
    features: [
      '双重净化技术，效果显著',
      '处理多种有机废气',
      '运行成本低，维护简单',
      '环保达标，安全可靠'
    ],
    applications: [
      '化工企业',
      '印刷行业',
      '涂装车间',
      '制药企业'
    ]
  },
  {
    id: 13,
    title: 'VOCs催化燃烧系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/cuihua.jpg',
    summary: '高效VOCs有机废气催化燃烧处理系统，实现废气无害化处理。',
    description: 'VOCs催化燃烧系统采用催化燃烧技术，在较低温度下将有机废气完全氧化分解为无害的CO2和H2O。具有处理效率高、能耗低、无二次污染等优点，是VOCs治理的理想选择。',
    features: [
      '催化燃烧，处理效率高',
      '低温操作，节能环保',
      '无二次污染',
      '自动化控制，运行稳定'
    ],
    applications: [
      '石化行业',
      '涂装行业',
      '印刷包装',
      '制药化工'
    ]
  },
  
 
  {
    id: 16,
    title: '木浆纤维除尘滤筒',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/mujiang.jpg',
    summary: '采用阻燃木浆材料的环保滤筒，兼具过滤性能和安全性。',
    description: '阻燃木浆滤筒采用经过特殊处理的木浆纤维材料，具有良好的过滤性能和阻燃特性。环保可降解，同时保证了使用安全性，是绿色环保的过滤解决方案。',
    features: [
      '环保木浆材料，可降解',
      '阻燃处理，安全可靠',
      '过滤效果好，透气性佳',
      '成本适中，性价比高'
    ],
    applications: [
      '食品加工',
      '制药行业',
      '精细化工',
      '环保要求高的场所'
    ]
  },
  

  {
    id: 20,
    title: '初中效(袋式)过滤器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/chuzhongxiao.jpg',
    summary: '多级过滤器系列，满足不同精度的空气净化需求。',
    description: '初/中/高效过滤器系列产品涵盖了从粗效到高效的全系列过滤等级。初效过滤器用于预过滤，中效过滤器用于中级净化，高效过滤器用于精密过滤，可根据不同需求进行组合使用。',
    features: [
      '多级过滤，满足不同需求',
      '过滤效率逐级提升',
      '标准化设计，通用性强',
      '维护更换方便'
    ],
    applications: [
      '空调系统',
      '洁净室',
      '医疗设施',
      '精密制造'
    ]
  },
  {
    id: 27,
    title: '高效过滤器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gaoxiao.jpg',
    summary: 'KG/VC系列高效过滤器，提供F6-H13等级的精密过滤。',
    description: '高效过滤器适用于需要高洁净度的空气净化场合，采用优质滤料和精密制造工艺，提供F6到H13等级的高效过滤。广泛应用于电子、医药、食品、精密仪器等对空气质量要求极高的行业。',
    features: [
      '高效过滤，F6-H13等级',
      '精密制造，质量可靠',
      '低阻力设计，节能高效',
      '标准化尺寸，通用性强'
    ],
    applications: [
      '洁净室工程',
      '电子制造',
      '医药生产',
      '食品加工',
      '精密仪器',
      '生物实验室'
    ]
  },
  {
    id: 28,
    title: '板式初效过滤器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/banshiguolvqi.png',
    summary: '板式初效过滤器，适用于空调通风系统的预过滤。',
    description: '板式初效过滤器适用于滤除当量直径在10μm以上的尘粒，能有效过滤空气中的灰尘和杂物。滤料采用进口粗效滤棉，可广泛用于各种空调通风的进口处，作为预过滤器使用。',
    features: [
      '滤除10μm以上尘粒',
      '进口粗效滤棉材料',
      '金属框架可重复利用',
      '废弃纸框可焚烧处理'
    ],
    applications: [
      '空调通风系统',
      '预过滤应用',
      '工业通风',
      '商业建筑',
      '住宅空调',
      '净化设备'
    ]
  },
  {
    id: 29,
    title: '除尘布袋',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/chuchenbudai.jpg',
    summary: '多种材质除尘布袋，满足不同工况的除尘需求。',
    description: '除尘布袋是袋式除尘器的核心元件，决定除尘效率和工作温度。提供涤纶、美塔斯、PPS、氟美斯、P84等多种材质，适用于常温到高温的各种工况条件。',
    features: [
      '多种材质可选',
      '适用温度范围广',
      '优异的过滤性能',
      '长使用寿命'
    ],
    applications: [
      '钢铁冶炼',
      '电力行业',
      '水泥建材',
      '化工行业',
      '垃圾焚烧',
      '沥青搅拌'
    ]
  },
  {
    id: 30,
    title: '除尘布袋骨架',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/chuchengujia.jpg',
    summary: '除尘布袋的"肋骨"，全自动电焊一次成型。',
    description: '除尘骨架是滤袋的"肋骨"，采用全自动电焊机一次焊接成型。具有焊接牢固、外表光洁挺直、使滤袋不受损伤的特点。应用轻巧，便于安装和维护，表面处理采用镀锌、喷塑或有机硅工艺。',
    features: [
      '全自动电焊一次成型',
      '焊接牢固外表光洁',
      '轻巧便于安装维护',
      '多种表面处理工艺'
    ],
    applications: [
      '袋式除尘器',
      '脉冲除尘器',
      '反吹除尘器',
      '机械振打除尘器',
      '工业除尘系统',
      '环保设备'
    ]
  },
  {
    id: 21,
    title: '聚酯滤筒',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/juzhitong.jpg',
    summary: '通用型聚酯纤维滤筒，适用范围广，性价比高。',
    description: '聚酯滤筒采用优质聚酯纤维材料制造，具有良好的过滤性能和机械强度。适用于一般工业粉尘的过滤处理，具有成本低、效果好、适用范围广等特点，是工业除尘的经济选择。',
    features: [
      '聚酯纤维材料，性能稳定',
      '适用范围广，通用性强',
      '成本低，性价比高',
      '维护简单，更换方便'
    ],
    applications: [
      '一般工业除尘',
      '机械加工',
      '建材生产',
      '轻工业生产'
    ]
  },
  
  {
    id: 23,
    title: '环保项目运维',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/huanbaoyunwei.jpeg',
    summary: '确保环保设施正常运行、实现预期环保效果的关键环节。',
    description: '环保项目运维是确保环保设施正常运行、实现预期环保效果的关键环节。项目运维包括设备巡检、数据采集与分析、故障排查与修复、优化运行方案等。通过专业的运维团队和完善的服务体系，确保环保设施稳定运行，为改善环境质量和可持续发展贡献力量。',
    features: [
      '设备巡检与维护',
      '数据采集与分析',
      '故障排查与修复',
      '优化运行方案',
      '实时监控预警',
      '技术创新与培养'
    ],
    applications: [
      '环保设施运营管理',
      '除尘系统运维',
      '废气处理设备维护',
      '污水处理设施运营',
      '环保达标监测',
      '设备改造升级'
    ]
  },
  {
    id: 24,
    title: '环保工程安装',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/huanbaogongcheng.jpg',
    summary: '环保工程项目中一项至关重要的任务，确保环保工程正常运行及环境得到有效保护。',
    description: '环保工程安装是环保工程项目中一项至关重要的任务，旨在确保环保工程正常运行及环境得到有效保护。该工程安装涉及多个环节，包括设备选择、布局设计、施工安装及调试等。我们严格遵守安全规范，注重细节，为客户提供高质量的环保工程安装服务。',
    features: [
      '设备选择与配置',
      '布局设计优化',
      '施工安装专业',
      '设备调试完善',
      '安全规范施工',
      '性能检测验收'
    ],
    applications: [
      '除尘系统安装工程',
      '废气处理设备安装',
      '污水处理设施建设',
      '洁净厂房工程',
      '环保设备改造',
      '工业通风系统'
    ]
  },
  {
    id: 25,
    title: '粉尘与废气综合治理设计方案',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/feiqi.jpg',
    summary: '致力于粉尘和废气的综合治理，减少排放，提高空气质量，达到国家环保标准。',
    description: '随着工业化的快速发展，粉尘和废气的排放问题日益严重。福运环保设计方案致力于粉尘和废气的综合治理，通过源头控制、收集处理、预处理、吸收处理、生物处理和排放控制等多个环节，减少粉尘和废气的排放，提高空气质量。',
    features: [
      '源头控制减排',
      '高效收集处理',
      '多元化治理技术',
      '实时监测控制',
      '达标排放保证',
      '综合效益提升'
    ],
    applications: [
      '工业粉尘治理方案',
      '有机废气处理设计',
      '复合污染综合治理',
      '工艺改进优化',
      '环保设施升级改造',
      '清洁生产技术咨询'
    ]
  },
  {
    id: 26,
    title: '打磨除尘器',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/damochuchen.png',
    summary: 'FDM系列打磨除尘器，专为打磨工艺设计的高效除尘设备。',
    description: 'FDM系列打磨除尘器专门为打磨工艺设计，采用先进的过滤技术，能够有效收集打磨过程中产生的粉尘和颗粒物。设备具有风量大、过滤效率高、运行稳定等特点，广泛应用于各种打磨作业场所。',
    features: [
      '打磨台自带风机，一体化设计',
      '可移动防护罩(LED灯条)，电机、照明防护',
      '可调整过滤器，过滤面积大，过滤精度高',
      '可调整工作台高度，适应不同工件',
      '上下工作台，可根据需要灵活使用',
      '抽屉式集尘箱，倾倒灰尘方便',
      '上下工作台，可根据需要灵活使用',
      '静音风机，噪音低，运行稳定',
      '模块化设计，维护方便',
      '抽屉式集尘箱，倾倒灰尘方便'
    ],
    applications: [
      '各种钢件、铸件、打磨',
      '焊缝、焊渣、清理、打磨',
      '各种、建材、陶瓷、化工、机械',
      '冶金等行业的打磨除尘'
    ]
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>产品中心</h1>
    </div>

    <div class="content">
      <!-- 水平布局卡片 -->
      <div class="cards-grid">
        <div
          v-for="product in products"
          :key="product.id"
          class="card-item"
          @click="goToProductDetail(product.id)"
        >
          <div class="card-image">
            <img :src="product.image" :alt="product.title" />
          </div>
          <div class="card-content">
            <h3>{{ product.title }}</h3>
            <p>{{ product.summary }}</p>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 卡片网格布局 */
.cards-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}

/* 统一的卡片样式 - 水平布局 */
.card-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  min-height: 140px;
}

.card-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #1e3470;
}

.card-image {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-item:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  flex: 1;
  padding: 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.card-content p {
  margin: 0;
  color: #666;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .cards-grid {
    gap: 1.2rem;
    padding: 0 2rem;
  }

  .card-item {
    min-height: 160px;
  }

  .card-image {
    width: 140px;
    height: 140px;
  }

  .card-content {
    padding: 1.5rem 2rem;
  }

  .card-content h3 {
    font-size: 1.2rem;
    margin-bottom: 0.6rem;
  }

  .card-content p {
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

@media (min-width: 1024px) {
  .cards-grid {
    gap: 1.5rem;
  }

  .card-item {
    min-height: 180px;
  }

  .card-image {
    width: 160px;
    height: 160px;
  }

  .card-content {
    padding: 2rem 2.5rem;
  }

  .card-content h3 {
    font-size: 1.3rem;
    margin-bottom: 0.75rem;
  }

  .card-content p {
    font-size: 0.95rem;
    line-height: 1.6;
  }
}
</style>
