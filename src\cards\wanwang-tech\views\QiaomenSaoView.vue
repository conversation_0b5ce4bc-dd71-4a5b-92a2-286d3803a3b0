<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('QiaomenSaoView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>"敲门嫂"钢城红信息化平台</h1>
    </div>

    <div class="content">
      <!-- 产品概述 -->
      <div class="section overview-section">
        <div class="section-header">
          <h2>产品概述</h2>
        </div>
        <div class="overview-content">
          <div class="overview-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/qiaomensao.jpg" alt="敲门嫂钢城红信息化平台" />
          </div>
          <div class="overview-text">
            <p>"敲门嫂"钢城红信息化平台是基于"移动互联网+"技术，打通宣传群众、教育群众、关心群众、服务群众"最后一公里"，依托"敲门嫂"志愿服务，开创服务群众新模式，让新时代文明实践与群众需求无缝对接，实现党的宣传思想文化工作在基层实起来、强起来。</p>
            <p>致力将其打造成"三手"，即老百姓的生活帮手、社区干部的工具助手、党建宣传的工作抓手。</p>
          </div>
        </div>
      </div>

      <!-- 三大功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>三大核心功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🏠</div>
            <h3>老百姓的生活帮手</h3>
            <div class="feature-content">
              <p>"敲门嫂"微信小程序是集宣传展示、互动互通、管理调度于一体的新型信息化新时代文明实践云平台，集成"志愿服务"、"网格服务"、"意见建议"、"敲门嫂之星"等内容。</p>
              <p>旨在打通社区线上线下一体服务，实现"群众点单—社区派单—敲门嫂接单—群众评单"志愿服务新模式。社区有需求的群众可通过手机或电脑登录"敲门嫂"微信小程序，经过注册登录后进行各项服务点单。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🛠️</div>
            <h3>社区干部的工具助手</h3>
            <div class="feature-content">
              <p>社区干部通过"敲门嫂"微信小程序中的"活动风采"、"社区公告"等栏目，第一时间向社区群众发布重要信息公告，成为群众获取信息的有效窗口。</p>
              <p>通过"意见建议"版块可以有效链接职能部门和社区资源力量，由街道安排专人专职跟进，及时为群众答疑解惑，全力打造指尖上的政民互动"连心桥"。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎯</div>
            <h3>党建宣传的工作抓手</h3>
            <div class="feature-content">
              <p>按照市委提出的"党建做强了就是凝聚力、做细了就是战斗力、做实了就是生产力"要求，突出党建引领，抓好党建宣传。</p>
              <p>通过小程序中的"活动风采"，讲好敲门嫂爱心善事、社区干部工作实事、社区群众日常故事，分享干群工作生活动态，增强各类信息资源的共享互动。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台展示 -->
      <div class="section platform-section">
        <div class="section-header">
          <h2>平台功能展示</h2>
        </div>
        <div class="platform-images">
          <div class="platform-item">
            <div class="platform-header">
              <div class="platform-icon">📱</div>
              <h3>小程序端</h3>
            </div>
            <div class="platform-content">
              <div class="platform-text">
                <p>"敲门嫂"微信小程序提供便民服务入口，群众可以通过小程序进行服务点单，享受便民服务。界面简洁友好，操作便捷，让群众能够轻松获取所需服务。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/qiaomensao1.png" alt="小程序端界面" />
              </div>
            </div>
          </div>

          <div class="platform-item">
            <div class="platform-header">
              <div class="platform-icon">🏘️</div>
              <h3>社区选择功能</h3>
            </div>
            <div class="platform-content">
              <div class="platform-text">
                <p>需要选择自己所属社区才能下单，确保服务的精准性和针对性。系统会根据用户所在社区匹配相应的"敲门嫂"志愿者，提供就近服务。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/qiaomensao2.png" alt="社区选择界面" />
              </div>
            </div>
          </div>

          <div class="platform-item">
            <div class="platform-header">
              <div class="platform-icon">⭐</div>
              <h3>服务评价系统</h3>
            </div>
            <div class="platform-content">
              <div class="platform-text">
                <p>获取服务的居民可以对"敲门嫂"服务进行评价，形成服务质量监督机制。通过评价反馈，不断提升服务质量，确保群众满意度。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/qiaomensao3.png" alt="服务评价界面" />
              </div>
            </div>
          </div>

          <div class="platform-item">
            <div class="platform-header">
              <div class="platform-icon">📋</div>
              <h3>订单管理系统</h3>
            </div>
            <div class="platform-content">
              <div class="platform-text">
                <p>社区工作者的主要职责是分派订单，把群众服务订单分派给有时间的"敲门嫂"。系统支持智能分派和手动分派，确保服务及时响应。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/qiaomensao4.png" alt="订单管理界面" />
              </div>
            </div>
          </div>

          <div class="platform-item">
            <div class="platform-header">
              <div class="platform-icon">🔄</div>
              <h3>订单退回机制</h3>
            </div>
            <div class="platform-content">
              <div class="platform-text">
                <p>"敲门嫂"如果临时有事情的情况下，可以退回订单，说明缘由，避免耽搁到订单的完成时间。灵活的退回机制确保服务质量和时效性。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/qiaomensao5.png" alt="订单退回界面" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>平台价值</h2>
        </div>
        <div class="value-content">
          <p>"敲门嫂"微信小程序是党建引领基层治理的生动实践，是数字赋能新时代文明工作的创新举措。接下来，新钢街道将持续增强党建引领，围绕"党建+社区"工作，不断为小程序赋能，加大对小程序的推广宣传，做精做细小程序内容，推动打造成为基层治理改革的一张亮丽名片，为加快全面建设社会主义现代化幸福渝水，奋力开创"上善渝水、抱石故园"工作新局面贡献"钢城红"力量。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 产品概述样式 */
.overview-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.overview-image {
  width: 200px;
  height: 200px;
  flex-shrink: 0;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.overview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overview-text {
  flex: 1;
  color: #333;
  line-height: 1.8;
}

.overview-text p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  text-align: justify;
}

/* 功能特性样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
}

.feature-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  text-align: left;
}

/* 平台展示样式 */
.platform-images {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.platform-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
}

.platform-header {
  background: linear-gradient(135deg, #1693d2 0%, #0d7ab8 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
}

.platform-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.platform-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.platform-content {
  padding: 2rem;
}

.platform-text p {
  color: #333;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.platform-text img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  display: block;
}

/* 平台价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .overview-content {
    flex-direction: column;
    text-align: center;
  }

  .overview-image {
    width: 150px;
    height: 150px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .platform-header {
    padding: 1rem 1.5rem;
  }

  .platform-content {
    padding: 1.5rem;
  }

  .platform-text img {
    max-width: 100%;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }
}
</style>
