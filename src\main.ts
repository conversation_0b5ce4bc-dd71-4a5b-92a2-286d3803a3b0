import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './style.css'
import App from './App.vue'
import router from './router'

const app = createApp(App)

// 全局错误处理器
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error handler:', err, info)

  // 特别处理异步消息通道错误
  if (err instanceof Error && err.message.includes('message channel closed')) {
    console.warn('Message channel error detected, this is usually caused by browser extensions or popup blockers')
    return
  }
}

// 处理未捕获的 Promise 错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)

  // 特别处理异步消息通道错误
  if (event.reason && event.reason.message && event.reason.message.includes('message channel closed')) {
    console.warn('Message channel promise rejection detected, preventing default handling')
    event.preventDefault()
    return
  }
})

app.use(ElementPlus)
app.use(router)
app.mount('#app')