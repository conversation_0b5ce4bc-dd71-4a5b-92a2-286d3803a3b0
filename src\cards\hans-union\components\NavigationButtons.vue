<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  OfficeBuilding, 
  User
} from '@element-plus/icons-vue'

const router = useRouter()

// 汉氏联合导航按钮配置
const navigationItems = ref([
  {
    id: 'company-intro',
    name: '企业介绍',
    description: '公司详情',
    icon: OfficeBuilding,
    route: '/card/hans-union/company-intro',
    color: 'linear-gradient(135deg, #0f9da8, #1fb5c4)'
  },
  {
    id: 'ai-promoter',
    name: 'AI宣传员',
    description: '智能助手',
    icon: User,
    route: '/card/hans-union/ai-promoter',
    color: 'linear-gradient(135deg, #0f9da8, #2bc5d2)'
  }
])

// 导航到指定路由
const navigateTo = (route: string) => {
  router.push(route)
}
</script>

<template>
  <div class="navigation-grid">
    <div 
      v-for="item in navigationItems" 
      :key="item.id" 
      class="nav-button"
      @click="navigateTo(item.route)"
    >
      <div class="icon-container" :style="{ background: item.color }">
        <component :is="item.icon" class="icon" />
      </div>
      <div class="button-text-container">
        <span class="button-text">{{ item.name }}</span>
        <span class="button-description">{{ item.description }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 导航按钮网格布局 */
.navigation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

/* 导航按钮样式 */
.nav-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1rem 0.8rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.6);
}

/* 按钮光泽效果 */
.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  z-index: 0;
}

/* 按钮点击效果 */
.nav-button:active {
  transform: scale(0.98);
}

/* 按钮悬停效果 */
.nav-button:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 图标容器样式 */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* 图标样式 */
.icon {
  font-size: 1.1rem;
  color: white;
  transform: scale(0.7);
}

/* 按钮文本容器 */
.button-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 按钮主文本样式 */
.button-text {
  font-size: 0.9rem;
  font-weight: 500;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

/* 按钮描述文本样式 */
.button-description {
  font-size: 0.7rem;
  color: #0f9da8;
  margin-top: 0.2rem;
}

/* 悬停时图标放大效果 */
.nav-button:hover .icon-container {
  transform: scale(1.05);
}

/* 桌面端响应式设计 */
@media (min-width: 768px) {
  .navigation-grid {
    max-width: 600px;
    gap: 3.5rem;
  }
  
  .nav-button {
    padding: 1.5rem 1rem;
  }
  
  .icon-container {
    width: 3rem;
    height: 3rem;
  }
  
  .icon {
    font-size: 1.3rem;
  }
  
  .button-text {
    font-size: 1rem;
  }
  
  .button-description {
    font-size: 0.8rem;
  }
}
</style>