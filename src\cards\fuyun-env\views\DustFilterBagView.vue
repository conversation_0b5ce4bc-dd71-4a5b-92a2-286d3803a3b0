<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术参数
const technicalSpecs = reactive([
  { 
    type: '圆袋(外滤式)',
    diameter: '120',
    length: '2000-6000',
    application: '脉冲喷吹袋式除尘器'
  },
  { 
    type: '圆袋(外滤式)',
    diameter: '130',
    length: '2000-6000',
    application: '脉冲喷吹袋式除尘器'
  },
  { 
    type: '圆袋(外滤式)',
    diameter: '152',
    length: '2000-6000',
    application: '脉冲喷吹袋式除尘器'
  },
  { 
    type: '圆袋(外滤式)',
    diameter: '200',
    length: '2000-6000',
    application: '脉冲喷吹袋式除尘器'
  },
  { 
    type: '圆袋(内滤式)',
    diameter: '180',
    length: '6000-12000',
    application: '脉冲喷吹袋式除尘器'
  },
  { 
    type: '圆袋(内滤式)',
    diameter: '250',
    length: '6000-12000',
    application: '脉冲喷吹袋式除尘器'
  },
  { 
    type: '圆袋(内滤式)',
    diameter: '300',
    length: '6000-12000',
    application: '脉冲喷吹袋式除尘器'
  },
  { 
    type: '扁袋',
    diameter: '800',
    length: '2000-6000',
    application: '回转反吹袋式除尘器'
  },
  { 
    type: '扁袋',
    diameter: '900',
    length: '2000-6000',
    application: '回转反吹袋式除尘器'
  },
  { 
    type: '信封型袋',
    diameter: '1500×750×25',
    length: '-',
    application: '旁插式袋式除尘器'
  }
])

// 滤布种类
const filterTypes = reactive([
  { name: '涤纶针刺过滤毡', description: '常温工况，性价比高' },
  { name: 'PTFE耐腐蚀针刺过滤毡', description: '耐腐蚀，化学稳定性好' },
  { name: '涤纶防水防油针刺过滤毡', description: '防水型，适用于高湿环境' },
  { name: '玻璃纤维针刺过滤毡', description: '高温工况，耐温性能优异' },
  { name: '涤纶耐温针刺过滤毡', description: '中温工况，性能稳定' },
  { name: 'PPS针刺过滤毡', description: '高温耐腐蚀，化学稳定性好' },
  { name: '涤纶耐温覆膜针刺过滤毡', description: '覆膜处理，过滤精度高' },
  { name: 'P84针刺过滤毡', description: '超高温，优异的耐化学性' },
  { name: '防水防油防静电针刺过滤毡', description: '三防处理，适用于特殊工况' },
  { name: '氟美斯针刺过滤毡', description: '高温耐磨，抗折性好' },
  { name: '涤纶纤维防静电针刺过滤毡', description: '防静电，安全可靠' },
  { name: '中温迷特针刺过滤毡', description: '中温工况，性能优异' },
  { name: '亚克力抗水解针刺过滤毡', description: '抗水解，耐湿性好' },
  { name: '复合防静电针刺过滤毡', description: '复合材料，综合性能好' },
  { name: '诺梅克斯针刺过滤毡', description: '高温工况，机械强度高' },
  { name: '华特高温针刺过滤毡', description: '超高温，特殊工况专用' }
])

// 滤袋长寿四要素
const longevityFactors = reactive([
  {
    title: '选用合适的滤料',
    description: '根据工况条件选择最适合的滤料材质，确保滤袋在恶劣环境中保持良好性能。'
  },
  {
    title: '设计合理的结构',
    description: '优化滤袋结构设计，提高过滤效率和使用寿命，降低运行阻力。'
  },
  {
    title: '精湛的缝制技术',
    description: '采用先进的缝制工艺，确保滤袋密封性和机械强度，防止破损。'
  },
  {
    title: '正确的使用方法',
    description: '规范操作和维护，定期检查和清灰，延长滤袋使用寿命。'
  }
])

// 应用范围
const applications = reactive([
  {
    title: '钢铁冶炼',
    description: '高炉、转炉、电炉等钢铁冶炼过程的烟气除尘。'
  },
  {
    title: '电力行业',
    description: '燃煤电厂、垃圾发电厂的烟气净化和粉煤灰收集。'
  },
  {
    title: '水泥建材',
    description: '水泥生产线、石灰窑等建材行业的粉尘控制。'
  },
  {
    title: '化工行业',
    description: '化工生产过程中的粉尘和有害气体净化。'
  },
  {
    title: '垃圾焚烧',
    description: '垃圾焚烧炉、危废处理等环保设施的烟气净化。'
  },
  {
    title: '沥青搅拌',
    description: '沥青搅拌站、路面施工等的粉尘控制。'
  },
  {
    title: '有色冶炼',
    description: '铝、铜、锌等有色金属冶炼的烟气除尘。'
  },
  {
    title: '炭黑行业',
    description: '炭黑生产过程中的粉尘收集和净化。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>除尘布袋</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/chuchenbudai.jpg" alt="除尘布袋" />
        </div>
        <div class="hero-content">
          <h2>除尘布袋系列</h2>
          <p class="product-subtitle">袋式除尘器的核心元件，决定除尘效率和工作温度</p>
          <div class="product-intro">
            <p>除尘布袋是袋式除尘器的关键元件，直接影响除尘效率和工作温度。我们提供多种材质的滤袋产品，包括涤纶、美塔斯、PPS、氟美斯、P84等，适用于常温到超高温的各种工况条件。</p>
            <p>产品广泛应用于钢铁、电力、水泥、化工、垃圾焚烧等行业，具有过滤效率高、使用寿命长、耐温性能好的特点，是工业除尘的理想选择。</p>
          </div>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术参数
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>袋型</th>
                <th>直径/规格(mm)</th>
                <th>长度(mm)</th>
                <th>适用除尘器类型</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.type + spec.diameter">
                <td>{{ spec.type }}</td>
                <td>{{ spec.diameter }}</td>
                <td>{{ spec.length }}</td>
                <td>{{ spec.application }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 滤布种类说明 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🧵</span>
          滤布种类说明
        </h3>
        <div class="filter-types">
          <div
            v-for="(type, index) in filterTypes"
            :key="index"
            class="filter-type-card"
          >
            <h4>{{ type.name }}</h4>
            <p>{{ type.description }}</p>
          </div>
        </div>
      </div>

      <!-- 滤袋长寿四要素 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          滤袋长寿的四个要素
        </h3>
        <div class="longevity-factors">
          <div
            v-for="(factor, index) in longevityFactors"
            :key="index"
            class="factor-card"
          >
            <div class="factor-number">{{ index + 1 }}</div>
            <div class="factor-content">
              <h4>{{ factor.title }}</h4>
              <p>{{ factor.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用范围 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用范围
        </h3>
        <div class="applications">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 主要材质详解 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🧪</span>
          主要材质详解
        </h3>
        <div class="material-details">
          <div class="material-card">
            <div class="material-header">
              <h4>亚克力中温针刺过滤毡布袋</h4>
              <div class="temp-badge">≤140℃</div>
            </div>
            <div class="material-specs">
              <div class="spec-item">
                <span class="spec-label">克重:</span>
                <span class="spec-value">500g/m²</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">厚度:</span>
                <span class="spec-value">1.9mm</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">透气量:</span>
                <span class="spec-value">14m³/m².min</span>
              </div>
            </div>
            <p class="material-desc">采用非织造针刺工艺，利用较强纤维交错排列，空隙分布均匀的细纤维布，表面经热轧、烧毛处理，使其表面平整光滑，不易被粉尘堵塞。</p>
          </div>

          <div class="material-card">
            <div class="material-header">
              <h4>高温美塔斯针刺过滤毡布袋(覆膜)</h4>
              <div class="temp-badge high-temp">≤204℃</div>
            </div>
            <div class="material-specs">
              <div class="spec-item">
                <span class="spec-label">克重:</span>
                <span class="spec-value">500g/m²</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">厚度:</span>
                <span class="spec-value">2.2mm</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">透气量:</span>
                <span class="spec-value">17m³/m².min</span>
              </div>
            </div>
            <p class="material-desc">美塔斯是美国杜邦公司生产的诺梅克斯和日本帝人公司生产的康耐克斯的统称。广泛用于各种高温烟气过滤烟气场合，性能优良可靠。</p>
          </div>

          <div class="material-card">
            <div class="material-header">
              <h4>高温PPS针刺过滤毡布袋</h4>
              <div class="temp-badge high-temp">≤190℃</div>
            </div>
            <div class="material-specs">
              <div class="spec-item">
                <span class="spec-label">克重:</span>
                <span class="spec-value">500g/m²</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">厚度:</span>
                <span class="spec-value">1.8mm</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">透气量:</span>
                <span class="spec-value">15m³/m².min</span>
              </div>
            </div>
            <p class="material-desc">PPS纤维具有强度的完整保持性和内在的耐化学性，可以在恶劣的环境中保持良好的过滤性能，并达到理想的寿命。</p>
          </div>

          <div class="material-card">
            <div class="material-header">
              <h4>氟美斯(FMS)耐高温针刺毡布袋</h4>
              <div class="temp-badge ultra-high-temp">≤260℃</div>
            </div>
            <div class="material-specs">
              <div class="spec-item">
                <span class="spec-label">克重:</span>
                <span class="spec-value">800g/m²</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">厚度:</span>
                <span class="spec-value">2.5mm</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">透气量:</span>
                <span class="spec-value">10m³/m².min</span>
              </div>
            </div>
            <p class="material-desc">与玻璃纤维布袋相比，耐磨性、抗折性及耐剥离强度明显的提高。过滤速度可达1.0m/min以上，运行阻力低。</p>
          </div>

          <div class="material-card">
            <div class="material-header">
              <h4>P84耐高温针刺毡布袋</h4>
              <div class="temp-badge ultra-high-temp">≤260℃</div>
            </div>
            <div class="material-specs">
              <div class="spec-item">
                <span class="spec-label">克重:</span>
                <span class="spec-value">500g/m²</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">厚度:</span>
                <span class="spec-value">2.3mm</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">透气量:</span>
                <span class="spec-value">15m³/m².min</span>
              </div>
            </div>
            <p class="material-desc">具有显著的耐温性和良好的抗化学性，对酸性废气及碱性粉尘的过滤有一定的优势，低逆洗压力并高弹泥饼效率。</p>
          </div>

          <div class="material-card">
            <div class="material-header">
              <h4>三防涤纶针刺毡布袋</h4>
              <div class="temp-badge">≤130℃</div>
            </div>
            <div class="material-specs">
              <div class="spec-item">
                <span class="spec-label">克重:</span>
                <span class="spec-value">500g/m²</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">厚度:</span>
                <span class="spec-value">1.8mm</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">透气量:</span>
                <span class="spec-value">11m³/m².min</span>
              </div>
            </div>
            <p class="material-desc">防水、防静电、防油三防处理。在生产针刺毡的过程中混入导电纤维，滤布经轧光，浸渍PTFE防水剂，用在含湿量较大的场合。</p>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <div class="advantage-content">
              <h4>多种材质可选</h4>
              <p>提供涤纶、美塔斯、PPS、氟美斯、P84等多种材质，满足从常温到超高温的各种工况需求，确保在恶劣环境中保持优异性能。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🌡️</div>
            <div class="advantage-content">
              <h4>适用温度范围广</h4>
              <p>从130℃到260℃的宽温度范围，满足不同行业的温度要求，确保滤袋在高温环境下稳定工作，延长使用寿命。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔬</div>
            <div class="advantage-content">
              <h4>优异的过滤性能</h4>
              <p>采用先进的针刺工艺和优质滤料，具有过滤效率高、阻力低、容尘量大的特点，确保除尘系统高效稳定运行。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⏰</div>
            <div class="advantage-content">
              <h4>长使用寿命</h4>
              <p>精湛的缝制技术和合理的结构设计，确保滤袋密封性和机械强度，在正确使用和维护下，具有较长的使用寿命。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🛡️</div>
            <div class="advantage-content">
              <h4>耐腐蚀性能好</h4>
              <p>特殊材质具有优异的耐化学腐蚀性能，能够抵抗酸性、碱性等腐蚀性气体的侵蚀，适用于化工等特殊工况。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <div class="advantage-content">
              <h4>防静电安全</h4>
              <p>提供防静电处理的滤袋，有效防止静电积累，确保在易燃易爆环境中的安全使用，符合安全生产要求。</p>
            </div>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.5rem;
  min-width: 600px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.5rem 0.25rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.45rem;
  line-height: 1.1;
  border: 1px solid #2563eb;
}

.specs-table td {
  padding: 0.5rem 0.25rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.5rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

/* 滤布种类 */
.filter-types {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.filter-type-card {
  background: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.filter-type-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-type-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.3;
}

.filter-type-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.8rem;
}

/* 长寿要素 */
.longevity-factors {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.factor-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.factor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.factor-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.factor-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.factor-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 应用范围 */
.applications {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.application-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 材质详解 */
.material-details {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.material-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.material-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.material-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.material-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.3;
}

.temp-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.temp-badge.high-temp {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.temp-badge.ultra-high-temp {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.material-specs {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
}

.spec-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.spec-label {
  font-size: 0.7rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.spec-value {
  font-size: 0.8rem;
  font-weight: 600;
  color: #1e3470;
}

.material-desc {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 产品优势 */
.advantages {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .specs-table {
    font-size: 0.6rem;
  }

  .specs-table th {
    padding: 0.75rem 0.5rem;
    font-size: 0.55rem;
  }

  .specs-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.6rem;
  }

  .filter-types {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .filter-type-card {
    padding: 1.25rem;
  }

  .filter-type-card h4 {
    font-size: 1rem;
  }

  .filter-type-card p {
    font-size: 0.85rem;
  }

  .longevity-factors {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .factor-card {
    padding: 1.5rem;
  }

  .factor-content h4 {
    font-size: 1.1rem;
  }

  .factor-content p {
    font-size: 0.9rem;
  }

  .material-details {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .material-card {
    padding: 2rem;
  }

  .material-header h4 {
    font-size: 1.1rem;
  }

  .material-specs {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding: 1.25rem;
  }

  .spec-label {
    font-size: 0.75rem;
  }

  .spec-value {
    font-size: 0.85rem;
  }

  .material-desc {
    font-size: 0.9rem;
  }

  .advantages {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .application-card {
    padding: 1.5rem;
  }

  .application-card h4 {
    font-size: 1.1rem;
  }

  .application-card p {
    font-size: 0.9rem;
  }
}

@media (min-width: 1024px) {
  .filter-types {
    grid-template-columns: repeat(3, 1fr);
  }

  .longevity-factors {
    grid-template-columns: repeat(2, 1fr);
  }

  .material-details {
    grid-template-columns: repeat(2, 1fr);
  }

  .advantages {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.65rem;
  }

  .specs-table th {
    font-size: 0.6rem;
  }

  .specs-table td {
    font-size: 0.65rem;
  }

  .material-specs {
    grid-template-columns: repeat(3, 1fr);
  }

  .spec-item {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .material-specs {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .spec-item {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }

  .spec-label {
    margin-bottom: 0;
  }

  .applications {
    grid-template-columns: 1fr;
  }

  .filter-types {
    grid-template-columns: 1fr;
  }
}
</style>
