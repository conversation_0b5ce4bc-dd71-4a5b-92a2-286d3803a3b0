<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS 语音识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        button {
            padding: 15px 30px;
            font-size: 18px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        #startBtn {
            background-color: #4CAF50;
            color: white;
        }
        #stopBtn {
            background-color: #f44336;
            color: white;
        }
        #result {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            min-height: 100px;
            background-color: #f9f9f9;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>iOS Safari 语音识别测试</h1>
    
    <div id="status" class="status info">
        准备测试...
    </div>
    
    <button id="startBtn" onclick="startRecognition()">🎤 开始语音识别</button>
    <button id="stopBtn" onclick="stopRecognition()" disabled>⏹️ 停止识别</button>
    
    <div id="result">
        <p>识别结果将显示在这里...</p>
    </div>

    <script>
        let recognition = null;
        let isRecognizing = false;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateResult(text) {
            document.getElementById('result').innerHTML = `<p><strong>识别结果：</strong>${text}</p>`;
        }

        function checkSpeechSupport() {
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                updateStatus('❌ 此浏览器不支持语音识别', 'error');
                return false;
            }
            
            updateStatus('✅ 浏览器支持语音识别', 'success');
            return true;
        }

        function startRecognition() {
            if (!checkSpeechSupport()) return;

            try {
                // 创建语音识别对象
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                // 配置识别参数
                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'zh-CN';

                // 事件监听
                recognition.onstart = function() {
                    isRecognizing = true;
                    updateStatus('🎤 正在监听...', 'info');
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                };

                recognition.onresult = function(event) {
                    let finalTranscript = '';
                    let interimTranscript = '';

                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        const transcript = event.results[i][0].transcript;
                        if (event.results[i].isFinal) {
                            finalTranscript += transcript;
                        } else {
                            interimTranscript += transcript;
                        }
                    }

                    updateResult(finalTranscript + '<span style="color: #999;">' + interimTranscript + '</span>');
                };

                recognition.onerror = function(event) {
                    updateStatus(`❌ 识别错误: ${event.error}`, 'error');
                    console.error('Speech recognition error:', event);
                    resetButtons();
                };

                recognition.onend = function() {
                    updateStatus('🔇 识别已停止', 'info');
                    resetButtons();
                };

                // 开始识别
                recognition.start();

            } catch (error) {
                updateStatus(`❌ 启动失败: ${error.message}`, 'error');
                console.error('Recognition start error:', error);
            }
        }

        function stopRecognition() {
            if (recognition && isRecognizing) {
                recognition.stop();
            }
        }

        function resetButtons() {
            isRecognizing = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }

        // 页面加载时检查支持情况
        window.onload = function() {
            checkSpeechSupport();
            
            // 显示设备信息
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
            
            console.log('User Agent:', userAgent);
            console.log('Is iOS:', isIOS);
            console.log('Is Safari:', isSafari);
            console.log('HTTPS:', location.protocol === 'https:');
        };
    </script>
</body>
</html>
