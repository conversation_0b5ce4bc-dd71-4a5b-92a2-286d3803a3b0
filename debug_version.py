# 调试版本 - 带详细日志
Args = dict
Output = dict

async def main(args: Args) -> Output:
    # 🔍 调试：打印输入数据
    print(f"Input args: {args}")
    print(f"Args type: {type(args)}")
    
    if not args or not isinstance(args, dict):
        error_msg = f"Invalid input: args is {args} (type: {type(args)})"
        print(error_msg)
        return {"error": error_msg}
    
    params = args.get('params')
    print(f"Params: {params}")
    print(f"Params type: {type(params)}")
    
    if not params or not isinstance(params, dict):
        error_msg = f"Invalid input: params is {params} (type: {type(params)})"
        print(error_msg)
        return {"error": error_msg}
    
    # 安全地提取参数
    title = params.get('title') or ''
    desc = params.get('desc') or ''
    url = params.get('url') or ''
    nickname = params.get('nickname') or ''
    videoUrl = params.get('videoUrl') or ''
    
    # 处理数字字段
    likedCount = params.get('likedCount', 0)
    collectedCount = params.get('collectedCount', 0)
    
    # 处理列表字段
    imageList = params.get('imageList', [])
    
    # 🔍 调试：打印提取的数据
    print(f"Extracted data: title={title}, desc={desc}, url={url}")
    print(f"Numbers: liked={likedCount}, collected={collectedCount}")
    print(f"ImageList: {imageList}")
    
    records = [{"fields": {
        "笔记链接": url,
        "标题": title,
        "内容": desc,
        "作者": nickname,
        "点赞数": likedCount,
        "收藏数": collectedCount,
        "图片地址": imageList,
        "视频地址": videoUrl,
    }}]
    
    ret = {"records": records}
    print(f"Final result: {ret}")
    return ret
