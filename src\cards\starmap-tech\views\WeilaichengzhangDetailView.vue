<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { onMounted } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/starmap-tech/case-center')
}

onMounted(() => {
  document.title = '星图AI名片 - 未来成长线下中心'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>星图AI未来成长线下中心</h1>
    </div>

    <div class="content">
      <!-- 主标题区域 -->
      <div class="hero-section">
        <div class="hero-content">
          <h1 class="main-title">星图AI未来成长线下中心</h1>
          <p class="subtitle">不止是场地，更是未来能力的孵化场</p>
        </div>
      </div>

      <!-- 项目介绍卡片 -->
      <div class="section-card">
        <p class="hero-desc" style="text-indent: 2em;">
          在这里，我们相信最好的学习发生在真实的互动与玩乐之中。星图AI未来成长线下中心不是一个传统的教室，而是一个充满探索、创意和欢笑的成长空间。我们把前沿的AI工具变成孩子手中最好玩的玩具、最有灵感的画笔和最聪明的伙伴，让他们在亲身体验中，自然而然地拥抱未来。
        </p>
      </div>

      <!-- 三大核心理念 -->
      <div class="concepts-section">
        <div class="section-card">
          <h2 class="section-title">我们的三大核心理念</h2>
          <div class="concepts-grid">
            <div class="concept-item">
              <div class="concept-number">01</div>
              <h3>AI是创造的伙伴，不是竞争的对手</h3>
              <p>培养孩子将AI作为提升自己的超级工具，激发创意、解决问题，而不是陷入与机器比拼的误区。</p>
            </div>
            <div class="concept-item">
              <div class="concept-number">02</div>
              <h3>玩是最高级的学习，兴趣是最好的老师</h3>
              <p>摒弃枯燥的灌输式教学，所有活动都以"项目制"和"游戏化"的形式展开，让孩子在乐趣中收获成长。</p>
            </div>
            <div class="concept-item">
              <div class="concept-number">03</div>
              <h3>真实世界的挑战，塑造未来的能力</h3>
              <p>设计的每一个活动都与生活紧密相连，旨在培养孩子能够带得走、用得上的未来核心素养。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 孩子将收获 -->
      <div class="gains-section">
        <div class="section-card">
          <h2 class="section-title">在这里，您的孩子将收获</h2>
          <div class="gains-grid">
            <div class="gain-item">
              <div class="gain-icon">🎨</div>
              <h3>创造力与想象力</h3>
              <p>将天马行空的想法，通过AI变为现实。</p>
            </div>
            <div class="gain-item">
              <div class="gain-icon">🤝</div>
              <h3>协作力与表达力</h3>
              <p>在团队项目中学会沟通，自信地展示自己。</p>
            </div>
            <div class="gain-item">
              <div class="gain-icon">🧠</div>
              <h3>逻辑力与思辨力</h3>
              <p>在解谜和策划中，锻炼精准提问和解决问题的能力。</p>
            </div>
            <div class="gain-item">
              <div class="gain-icon">🚀</div>
              <h3>数字素养与未来观</h3>
              <p>建立对AI的正确认知，成为真正的"AI时代原住民"。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- AI主题活动 -->
      <div class="activities-section">
        <div class="section-card">
          <h2 class="section-title">丰富多彩的AI主题活动</h2>
          <p class="activities-note">（注：此处活动列表会持续更新，每次参与都可能有新惊喜！）</p>
          
          <div class="activities-grid">
            <div class="activity-item">
              <div class="activity-number">1</div>
              <h3>感恩主题创作营</h3>
              <p class="activity-intro">在AI的引导下，回忆与家人的温暖瞬间，共同创作一份独一无二的感恩视频。</p>
              <div class="activity-skills">
                <span class="skill-tag">感恩教育</span>
                <span class="skill-tag">情感表达能力</span>
                <span class="skill-tag">多媒体创作能力</span>
              </div>
            </div>

            <div class="activity-item">
              <div class="activity-number">2</div>
              <h3>"我的超级英雄人设"工作坊</h3>
              <p class="activity-intro">用AI作为"魔镜"，帮孩子发现自身优点，塑造专属英雄形象。</p>
              <div class="activity-skills">
                <span class="skill-tag">自我认同感</span>
                <span class="skill-tag">积极心理</span>
                <span class="skill-tag">创意表达能力</span>
              </div>
            </div>

            <div class="activity-item">
              <div class="activity-number">3</div>
              <h3>AI幻想生物设计师</h3>
              <p class="activity-intro">将脑海中的奇思妙想，通过AI指令变为独一无二的艺术品。</p>
              <div class="activity-skills">
                <span class="skill-tag">想象力</span>
                <span class="skill-tag">指令表达能力（Prompt）</span>
                <span class="skill-tag">故事构建能力</span>
              </div>
            </div>

            <div class="activity-item">
              <div class="activity-number">4</div>
              <h3>"AI戏剧梦工厂"</h3>
              <p class="activity-intro">零物料即兴创作，让AI担当"金牌编剧"，共同完成一场精彩演出。</p>
              <div class="activity-skills">
                <span class="skill-tag">团队协作力</span>
                <span class="skill-tag">语言表达能力</span>
                <span class="skill-tag">临场应变能力</span>
              </div>
            </div>

            <div class="activity-item">
              <div class="activity-number">5</div>
              <h3>"密室解谜"头脑风暴</h3>
              <p class="activity-intro">将教室变为信息密室，孩子们需协作使用AI这把"万能钥匙"解开谜题。</p>
              <div class="activity-skills">
                <span class="skill-tag">逻辑推理能力</span>
                <span class="skill-tag">信息检索与筛选能力</span>
              </div>
            </div>

            <div class="activity-item">
              <div class="activity-number">6</div>
              <h3>AI创意工坊 (故事/诗歌)</h3>
              <p class="activity-intro">与AI伙伴一起进行故事接龙或诗歌创作，体验人机协作的乐趣。</p>
              <div class="activity-skills">
                <span class="skill-tag">文学感受力</span>
                <span class="skill-tag">创意写作能力</span>
                <span class="skill-tag">发散性思维</span>
              </div>
            </div>

            <div class="activity-item">
              <div class="activity-number">7</div>
              <h3>小小营养师与AI美食策划</h3>
              <p class="activity-intro">模拟为家人策划一顿健康午餐，学习利用AI进行信息搜集和规划。</p>
              <div class="activity-skills">
                <span class="skill-tag">生活规划能力</span>
                <span class="skill-tag">信息整合能力</span>
                <span class="skill-tag">健康生活意识</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 立即体验 -->
      <div class="cta-section">
        <div class="section-card">
          <h2 class="section-title">百闻不如一见，立即开启体验！</h2>
          <p class="cta-description" style="text-indent: 2em;">
            我们的线下主题活动每周都在更新，想让您的孩子亲身体验AI带来的创造乐趣吗？扫码添加我们的官方微信，获取最新的活动排期，并预约一场精彩的体验之旅吧！
          </p>
          
          <div class="cta-content">
            <div class="qr-code">
              <img
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/erweima.jpg"
                alt="官方微信二维码"
                class="qr-image"
              >
            </div>
            <p class="cta-text">
              立即扫码，添加官方微信。第一时间获取我们的最新活动信息，为孩子开启与AI的伙伴关系！
            </p>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 主标题区域 */
.hero-section {
  position: relative;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 2rem;
  padding: 0;
  box-shadow: 0 10px 30px rgba(49, 106, 188, 0.2);
}

.hero-content {
  padding: 3rem 2rem;
  text-align: center;
  position: relative;
  z-index: 2;
}

.main-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.hero-desc {
  font-size: 1rem;
  color: #666;
  line-height: 1.8;
}

/* 通用卡片样式 */
.section-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 2.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #316abc;
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}


/* 核心理念样式 */
.concepts-grid {
  display: grid;
  gap: 1.5rem;
}

.concept-item {
  padding: 1.5rem;
  border-radius: 0.75rem;
  background: #f8fafc;
  border-left: 4px solid #316abc;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.concept-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(49, 106, 188, 0.1);
  border-left-width: 6px;
}

.concept-number {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-weight: 600;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(49, 106, 188, 0.2);
}

.concept-item h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #316abc;
  margin-bottom: 0.75rem;
}

.concept-item p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 收获能力样式 */
.gains-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
}

.gain-item {
  text-align: center;
  padding: 1.5rem;
  border-radius: 0.75rem;
  background: #f8fafc;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);

}

.gain-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(49, 106, 188, 0.1);
  border-bottom-width: 5px;
}

.gain-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: inline-block;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(49, 106, 188, 0.2);
}

.gain-item h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #316abc;
  margin-bottom: 0.75rem;
}

.gain-item p {
  color: #666;
  line-height: 1.5;
  margin: 0;
}

/* 活动列表样式 */
.activities-note {
  text-align: center;
  color: #666;
  font-style: italic;
  margin-bottom: 2rem;
}

.activities-grid {
  display: grid;
  gap: 1.5rem;
}

.activity-item {
  padding: 1.5rem;
  border-radius: 0.75rem;
  background: #f8fafc;

  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.activity-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(49, 106, 188, 0.1);
  border-left-width: 6px;
}

.activity-number {
  display: inline-block;
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 0.75rem;
  margin-bottom: 0;
  box-shadow: 0 4px 10px rgba(49, 106, 188, 0.2);
  vertical-align: middle;
}

.activity-item h3 {
  display: inline-block;
  font-size: 1.1rem;
  font-weight: 600;
  color: #316abc;
  margin-bottom: 0.75rem;
  vertical-align: middle;
}

.activity-intro {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.activity-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skill-tag {
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(49, 106, 188, 0.2);
}

/* CTA区域样式 */
.cta-description {
  color: #666;
  line-height: 1.7;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.cta-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.qr-code {
  margin-bottom: 1rem;
}

.qr-image {
  width: 180px;
  height: 180px;
  border-radius: 0.5rem;
  box-shadow: 0 10px 30px rgba(49, 106, 188, 0.15);
  border: 4px solid white;
  transition: transform 0.3s ease;
}

.qr-image:hover {
  transform: scale(1.05);
}

.cta-text {
  color: #666;
  line-height: 1.6;
  font-size: 1rem;
  margin: 0;
  max-width: 400px;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content {
    padding-top: 4rem;
    padding-bottom: 5rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .section-card {
    padding: 1.5rem;
  }
  
  .hero-content {
    padding: 2rem 1rem;
  }
  
  .main-title {
    font-size: 1.6rem;
  }
  
  .gains-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .cta-text {
    text-align: center;
  }
  
  .qr-image {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0.75rem 0.5rem;
  }
  
  .header h1 {
    font-size: 1rem;
  }
  
  .main-title {
    font-size: 1.4rem;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .concept-item, .gain-item, .activity-item {
    padding: 1.25rem;
  }
}
</style>