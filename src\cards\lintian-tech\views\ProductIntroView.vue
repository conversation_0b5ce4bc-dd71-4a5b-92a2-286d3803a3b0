<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  VideoPlay,
  Star,
  TrendCharts,
  User,
  School,
  Trophy,
  Check,
  CopyDocument,
  Message,
  Phone
} from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/lintian-tech')
}

const copyPhone = () => {
  navigator.clipboard.writeText('18007937376')
  alert('电话号码已复制到剪贴板')
}

const callPhone = () => {
  window.location.href = 'tel:18007937376'
}
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>产品介绍</h1>
    </div>

    <div class="content">
      <!-- 视频播放区域 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><VideoPlay /></el-icon>
            产品演示视频
          </h2>
          <div class="video-container">
            <video controls style="width: 100%; border-radius: 8px;">
              <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/LinTianKeJi/xcsp.mp4" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </div>
        </div>
      </div>

      <!-- 产品概述 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Star /></el-icon>
            产品概述
          </h2>
          <div class="intro-content">
            <h3>新时代学生伴随性综合素质评价管理系统——深度介绍与核心价值</h3>
            <p>我们推出的"新时代学生伴随性综合素质评价管理系统"是一款基于国家教育改革政策开发的智能化教育管理平台。该系统深度融合教育评价改革要求，通过信息化手段实现对学生成长全过程、多维度的科学评价。</p>
          </div>
        </div>
      </div>

      <!-- 系统功能 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><TrendCharts /></el-icon>
            这个系统是干什么用的？
          </h2>

          <!-- 解决传统评价痛点 -->
          <div class="feature-block">
            <h3>1. 解决传统评价的痛点</h3>
            <p>传统教育评价中，学生成长被简化为"分数"，而<strong>德智体美劳全面发展</strong>难以量化。本系统通过<strong>多维度、伴随性、动态化</strong>的记录与评价，实现：</p>
            <div class="feature-list">
              <div class="feature-item">
                <el-icon><Trophy /></el-icon>
                <div>
                  <strong>打破"唯分数论"</strong>：将品德、实践、艺术、健康等纳入评价体系。
                </div>
              </div>
              <div class="feature-item">
                <el-icon><TrendCharts /></el-icon>
                <div>
                  <strong>过程性记录</strong>：实时记录学生课堂表现、课外活动、家校行为，而非仅凭考试结果。
                </div>
              </div>
              <div class="feature-item">
                <el-icon><Check /></el-icon>
                <div>
                  <strong>客观真实</strong>：所有数据由教师、家长、学生共同参与生成，避免主观片面。
                </div>
              </div>
            </div>
          </div>

          <!-- 覆盖三大核心场景 -->
          <div class="feature-block">
            <h3>2. 覆盖三大核心场景</h3>
            <div class="scenario-grid">
              <div class="scenario-item">
                <el-icon><School /></el-icon>
                <h4>学校管理</h4>
                <p>落实政策要求，实现"三全育人"（全员、全程、全方位）。</p>
              </div>
              <div class="scenario-item">
                <el-icon><User /></el-icon>
                <h4>教师减负增效</h4>
                <p>一键完成评价、通知、考勤等繁琐工作，聚焦教学。</p>
              </div>
              <div class="scenario-item">
                <el-icon><Trophy /></el-icon>
                <h4>家长参与共育</h4>
                <p>随时了解孩子在校表现，配合学校科学培养。</p>
              </div>
            </div>
          </div>

          <!-- 关键功能举例 -->
          <div class="feature-block">
            <h3>3. 关键功能举例</h3>
            <div class="function-list">
              <div class="function-item">
                <strong>学生成长档案</strong>：自动生成包含学业、德育、实践等维度的电子档案，为升学提供依据。
              </div>
              <div class="function-item">
                <strong>家校协同平台</strong>：家长上传孩子居家表现（如睡眠、运动），教师同步在校数据，形成教育闭环。
              </div>
              <div class="function-item">
                <strong>智能数据分析</strong>：识别学生特长与短板，为个性化培养提供支持。
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择理由 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Star /></el-icon>
            为什么选择这个管理系统？
          </h2>

          <div class="reason-grid">
            <div class="reason-item">
              <h4>1. 政策刚性需求</h4>
              <p>国家政策明确要求"<strong>破除唯分数、唯升学</strong>"（《深化新时代教育评价改革总体方案》），该系统是<strong>合规落地的必备工具</strong>。</p>
              <p>已在<strong>全国38个综合素质评价试点区域</strong>（如江西上饶）成功应用，测评完成率全国前五，<strong>权威验证</strong>。</p>
            </div>

            <div class="reason-item">
              <h4>2. 真正减轻教师负担</h4>
              <p><strong>10分钟完成全班评价</strong>：通过扫码、人脸识别等技术，快速录入学生表现。</p>
              <p><strong>自动化统计</strong>：班级排名、成长趋势一键生成，告别手工报表。</p>
            </div>

            <div class="reason-item">
              <h4>3. 家长满意度提升</h4>
              <p><strong>透明化沟通</strong>：家长随时查看孩子动态，减少误解，增强信任。</p>
              <p><strong>科学育儿指导</strong>：通过系统数据，家长能针对性调整家庭教育策略。</p>
            </div>

            <div class="reason-item">
              <h4>4. 学校管理升级</h4>
              <p><strong>数据驱动决策</strong>：校长可实时查看全校评价数据，优化资源配置。</p>
              <p><strong>风险预警</strong>：如考勤异常、心理评价偏低等，系统自动提醒干预。</p>
            </div>

            <div class="reason-item">
              <h4>5. 技术优势</h4>
              <p><strong>微信端操作</strong>：无需安装APP，家长教师轻松上手。</p>
              <p><strong>隐私保护</strong>：符合《个人信息保护法》，数据加密存储。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 获得价值 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Trophy /></el-icon>
            选择我们，您将获得什么？
          </h2>
          <div class="value-list">
            <div class="value-item">
              <el-icon><Check /></el-icon>
              <div>
                <strong>政策合规保障</strong>：帮助学校通过教育督导检查，展现改革成效。
              </div>
            </div>
            <div class="value-item">
              <el-icon><Star /></el-icon>
              <div>
                <strong>典型案例背书</strong>：提供弋阳县等区域的成功落地经验。
              </div>
            </div>
            <div class="value-item">
              <el-icon><User /></el-icon>
              <div>
                <strong>全程服务支持</strong>：从部署到培训，专属团队保驾护航。
              </div>
            </div>
          </div>

          <div class="cta-section">
            <p class="cta-text">教育评价的指挥棒，已经转向"全面发展"——您准备好了吗？</p>
            <p class="cta-highlight">让我们携手，用科学评价点亮每个学生的未来！</p>
          </div>
        </div>
      </div>

      <!-- 联系方式 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Message /></el-icon>
            立即咨询
          </h2>
          <div class="contact-section">
            <!-- <p class="contact-cta">立即扫码咨询，获取专属解决方案！</p> -->
            <div class="contact-item">
                <span class="contact-label">官方微信公众号：</span>
                <span class="contact-value">智慧微家校</span>
              </div>
            <div class="qr-container">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/ErWeiMa.png" alt="咨询二维码" class="qr-code" />
            </div>

            <div class="contact-info">
              <div class="contact-item">
                <span class="contact-label">联系电话：</span>
                <span class="contact-value">18007937376</span>
                <div class="button-group">
                  <button @click="callPhone" class="call-btn">
                    <el-icon><Phone /></el-icon>
                    拨打
                  </button>
                  <button @click="copyPhone" class="copy-btn">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </button>
                </div>
              </div>
             
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  width: 100vw;
  background-color: #f8fafc;
  position: relative;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #dcfdfa, #a7f3d0);
  color: #059669;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: #059669;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(5, 150, 105, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 1.5rem;
}

.card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.card h2 {
  color: #059669;
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card h3 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.card p {
  color: #555;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

/* 视频容器 */
.video-container {
  margin-top: 1rem;
}

/* 产品介绍内容 */
.intro-content h3 {
  color: #059669;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  text-align: center;
}

/* 功能块样式 */
.feature-block {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.feature-block:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8fffe;
  border-radius: 0.5rem;
  border-left: 4px solid #059669;
}

.feature-item .el-icon {
  color: #059669;
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

/* 场景网格 */
.scenario-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.scenario-item {
  background: #f8fffe;
  padding: 1.5rem;
  border-radius: 0.75rem;
  text-align: center;
  border: 1px solid #dcfdfa;
}

.scenario-item .el-icon {
  color: #059669;
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.scenario-item h4 {
  color: #333;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.scenario-item p {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

/* 功能列表 */
.function-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.function-item {
  padding: 1rem;
  background: #f8fffe;
  border-radius: 0.5rem;
  border-left: 4px solid #10b981;
}

.function-item strong {
  color: #059669;
}

/* 选择理由网格 */
.reason-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 1rem;
}

.reason-item {
  background: #f8fffe;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid #dcfdfa;
}

.reason-item h4 {
  color: #059669;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.reason-item p {
  margin: 0 0 0.75rem 0;
}

.reason-item p:last-child {
  margin-bottom: 0;
}

/* 价值列表 */
.value-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.value-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fffe;
  border-radius: 0.5rem;
  border-left: 4px solid #059669;
}

.value-item .el-icon {
  color: #059669;
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

/* CTA区域 */
.cta-section {
  background: linear-gradient(135deg, #dcfdfa, #a7f3d0);
  padding: 1.5rem;
  border-radius: 0.75rem;
  text-align: center;
  margin-top: 1.5rem;
}

.cta-text {
  color: #333;
  font-size: 1rem;
  margin: 0 0 0.5rem 0;
}

.cta-highlight {
  color: #059669;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

/* 联系方式 */
.contact-section {
  text-align: center;
}

.contact-cta {
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.qr-container {
  margin: 1.5rem 0;
}

.qr-code {
  width: 200px;
  height: 200px;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8fffe;
  border-radius: 0.5rem;
  flex-wrap: wrap;
}

.contact-label {
  color: #666;
  font-weight: 500;
}

.contact-value {
  color: #333;
  font-weight: 600;
}

.button-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.call-btn {
  background: #059669;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.call-btn:hover {
  background: #047857;
}

.copy-btn {
  background: #059669;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.copy-btn:hover {
  background: #047857;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .scenario-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .reason-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-item {
    justify-content: flex-start;
  }
}

@media (min-width: 1024px) {
  .reason-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .qr-code {
    width: 250px;
    height: 250px;
  }
}
</style>
