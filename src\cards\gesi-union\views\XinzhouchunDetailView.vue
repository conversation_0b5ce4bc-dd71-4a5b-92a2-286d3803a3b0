<template>
  <div class="page">
    <!-- 顶部导航 -->
    <div class="header">
      <button @click="goBack" class="back-btn">‹ 返回</button>
      <h1>信州春大舞台</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 舞台头部展示 -->
      <div class="hero-section">
        <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xinzhouchun.png" alt="信州春大舞台" class="hero-image" />
      </div>

      <!-- 项目简介 -->
      <div class="section">
        <div class="intro-card">
          <div class="intro-header">
            <h2>项目简介</h2>
          </div>
          <div class="intro-content">
            <p class="intro-text">"信州春大舞台"是协会发起的大型现象级群众文化活动，一个真正属于上饶人民自己的开放式舞台。我们致力于丰富市民夜生活，为普通人提供展示才华、实现梦想的机会，同时有效激活市场消费活力，向全国推介上饶的城市魅力。</p>

            <div class="stage-vision">
              <div class="vision-grid">
                <div class="vision-item">
                  <h4>丰富夜生活</h4>
                  <p>为市民提供精彩的夜间文化娱乐</p>
                </div>
                <div class="vision-item">
                  <h4>实现梦想</h4>
                  <p>给普通人展示才华的舞台机会</p>
                </div>
                <div class="vision-item">
                  <h4>激活消费</h4>
                  <p>有效带动市场消费活力提升</p>
                </div>
                <div class="vision-item">
                  <h4>城市推介</h4>
                  <p>向全国展示上饶的城市魅力</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 活动亮点 -->
      <div class="section">
        <div class="highlights-section">
          <div class="highlights-header">
            <h2>活动亮点</h2>
            <p class="highlights-subtitle">三大核心亮点，打造现象级文化盛宴</p>
          </div>

          <div class="highlights-grid">
            <div class="highlight-card economic">
              <div class="highlight-card-header">
                <h3>经济激活器</h3>
              </div>
              <div class="highlight-card-content">
                <p>活动期间，单日现场人流突破1万人次，全网曝光超50万人次，强力拉动了水南历史文化街区的夜间消费。</p>
                <div class="impact-stats">
                  <div class="impact-stat">
                    <div class="impact-number">1万+</div>
                    <div class="impact-label">单日人流</div>
                  </div>
                  <div class="impact-stat">
                    <div class="impact-number">50万+</div>
                    <div class="impact-label">全网曝光</div>
                  </div>

                </div>
              </div>
            </div>

            <div class="highlight-card stage">
              <div class="highlight-card-header">
                <h3>全民大舞台</h3>
              </div>
              <div class="highlight-card-content">
                <p>真正实现"零门槛"，无论是工友、职员还是企业家，人人都可以是舞台的主角，展示才艺、推荐企业、分享快乐。</p>
                <div class="stage-features">
                  <div class="stage-feature">
                    <div class="stage-feature-text">零门槛参与</div>
                  </div>
                  <div class="stage-feature">
                    <div class="stage-feature-text">全民参与</div>
                  </div>
                  <div class="stage-feature">
                    <div class="stage-feature-text">才艺展示</div>
                  </div>
                  <div class="stage-feature">
                    <div class="stage-feature-text">企业推荐</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="highlight-card promotion">
              <div class="highlight-card-header">
                <h3>城市宣传窗</h3>
              </div>
              <div class="highlight-card-content">
                <p>成功吸引湖南、安徽、衢州等周边省市的游客与选手前来参赛打卡，成为推介上饶好山好水好物的一张亮丽名片。</p>
                <div class="attraction-regions">
                  <div class="region-badge">湖南</div>
                  <div class="region-badge">安徽</div>
                  <div class="region-badge">衢州</div>
                  <div class="region-badge">更多地区</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 参与方式 -->
      <div class="section">
        <div class="participation-section">
          <div class="participation-header">
            <h2>参与方式</h2>
            <p class="participation-subtitle">信州春大舞台，有梦你就来！</p>
          </div>

          <div class="participation-content">
            <div class="participation-intro">
              <p>我们持续欢迎社会各界人士、企业团体报名参与，共同将这份快乐与活力传递下去！</p>
            </div>

            <div class="participant-categories">
              <div class="participant-category">
                <h3>社会各界人士</h3>
                <div class="category-features">
                  <div class="category-feature">✓ 个人才艺展示</div>
                  <div class="category-feature">✓ 自由表演时间</div>
                  <div class="category-feature">✓ 专业指导支持</div>
                </div>
              </div>

              <div class="participant-category">
                <h3>企业团体</h3>
                <div class="category-features">
                  <div class="category-feature">✓ 团队节目展示</div>
                  <div class="category-feature">✓ 企业文化宣传</div>
                  <div class="category-feature">✓ 品牌推广机会</div>
                </div>
              </div>

              <div class="participant-category">
                <h3>才艺达人</h3>
                <div class="category-features">
                  <div class="category-feature">✓ 专业舞台展示</div>
                  <div class="category-feature">✓ 媒体曝光机会</div>
                  <div class="category-feature">✓ 观众互动体验</div>
                </div>
              </div>

              <div class="participant-category">
                <h3>追梦者</h3>
                <div class="category-features">
                  <div class="category-feature">✓ 梦想实现平台</div>
                  <div class="category-feature">✓ 励志故事分享</div>
                  <div class="category-feature">✓ 正能量传递</div>
                </div>
              </div>
            </div>
          </div>

          <div class="join-cta">
            <div class="cta-background">
              <h3>有梦你就来！</h3>
              <p>让我们一起在信州春大舞台上闪闪发光</p>
              <div class="cta-buttons">
                <button @click="goToContact" class="primary-btn">立即咨询</button>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/gesi-union/product-center')
}

const goToContact = () => {
  router.push('/card/gesi-union/ai-promoter')
}
</script>

<style scoped>
.page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 70px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.2s;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  flex: 1;
  text-align: center;
  padding-right: 3rem;
}

.content {
  padding-top: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 移动端内容间距优化 */
@media (max-width: 480px) {
  .content {
    padding-top: 3.75rem;
  }
}

.section {
  margin-bottom: 2rem;
}

/* Hero Section */
.hero-section {
  margin-bottom: 2rem;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.hero-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 1rem;
}

/* Intro Card */
.intro-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.intro-header {
  margin-bottom: 1.5rem;
}

.intro-header h2 {
  color: #c41b21;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.intro-text {
  color: #555;
  font-size: 1rem;
  line-height: 1.7;
  margin: 0 0 2rem 0;
  text-align: justify;
}

.stage-vision {
  margin-top: 2rem;
}

.vision-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.vision-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.vision-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.vision-item h4 {
  color: #c41b21;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.vision-item p {
  color: #666;
  font-size: 0.85rem;
  margin: 0;
  line-height: 1.4;
}

/* Highlights Section */
.highlights-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.highlights-header {
  text-align: center;
  margin-bottom: 2rem;
}

.highlights-header h2 {
  color: #c41b21;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.highlights-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.highlights-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.highlight-card {
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.highlight-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.highlight-card.economic {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 2px solid #fca5a5;
}

.highlight-card.economic::before {
  background: linear-gradient(135deg, #c41b21, #dc2626);
}

.highlight-card.stage {
  background: linear-gradient(135deg, #fef2f2, #fde8e8);
  border: 2px solid #fca5a5;
}

.highlight-card.stage::before {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
}

.highlight-card.promotion {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 2px solid #fca5a5;
}

.highlight-card.promotion::before {
  background: linear-gradient(135deg, #c41b21, #dc2626);
}

.highlight-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.highlight-card-header {
  margin-bottom: 1rem;
}

.highlight-card h3 {
  color: #c41b21;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.highlight-card-content p {
  color: #555;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.impact-stats {
  display: flex;
  gap: 1rem;
  justify-content: space-around;
}

.impact-stat {
  text-align: center;
  background: white;
  padding: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.impact-number {
  color: #c41b21;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
}

.impact-label {
  color: #666;
  font-size: 0.8rem;
  margin: 0;
}

.stage-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.stage-feature {
  background: white;
  padding: 0.75rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stage-feature-text {
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
}

.attraction-regions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
}

.region-badge {
  background: white;
  color: #c41b21;
  padding: 0.5rem 1rem;
  border-radius: 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #fca5a5;
}

/* Participation Section */
.participation-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.participation-header {
  text-align: center;
  margin-bottom: 2rem;
}

.participation-header h2 {
  color: #c41b21;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.participation-subtitle {
  color: #666;
  font-size: 1.2rem;
  margin: 0;
  font-weight: 500;
}

.participation-intro {
  text-align: center;
  margin-bottom: 2rem;
}

.participation-intro p {
  color: #555;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.participant-categories {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.participant-category {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.participant-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #c41b21, #e53e3e);
}

.participant-category:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.15);
  border-color: #c41b21;
}

.participant-category h3 {
  color: #c41b21;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.category-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-feature {
  color: #333;
  font-size: 0.9rem;
  padding: 0.5rem;
  background: white;
  border-radius: 0.5rem;
  border-left: 3px solid #c41b21;
}

.join-cta {
  margin-top: 2rem;
}

.cta-background {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  color: white;
  box-shadow: 0 8px 30px rgba(196, 27, 33, 0.3);
  position: relative;
  overflow: hidden;
}

.cta-background::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}



.cta-background h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  position: relative;
  z-index: 2;
}

.cta-background p {
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  position: relative;
  z-index: 2;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.primary-btn, .secondary-btn {
  padding: 0.75rem 2rem;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid white;
}

.primary-btn {
  background: white;
  color: #c41b21;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.secondary-btn {
  background: transparent;
  color: white;
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* 移动端专用优化 - 完全重新设计 */
@media (max-width: 480px) {
  .page {
    background: #f5f7fa;
    font-size: 14px;
    line-height: 1.5;
    padding-bottom: 80px;
  }

  .header {
    height: 3.5rem;
    padding: 0.75rem 1rem;
    box-shadow: 0 2px 8px rgba(196, 27, 33, 0.15);
  }

  .header h1 {
    font-size: 1.1rem;
    font-weight: 600;
    padding-right: 2.5rem;
  }

  .back-btn {
    padding: 0.5rem;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    border-radius: 8px;
  }

  .content {
    padding-top: 4rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .section {
    margin-bottom: 1rem;
  }

  /* 移动端卡片重新设计 - 更现代化 */
  .intro-card, .highlights-section, .participation-section {
    background: white;
    border-radius: 12px;
    padding: 1.25rem;
    margin: 0 0 1rem 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(196, 27, 33, 0.1);
  }

  /* 移动端触摸优化 */
  .primary-btn, .secondary-btn {
    min-height: 48px;
    touch-action: manipulation;
    font-size: 1rem;
    padding: 0.875rem 1.5rem;
    border-radius: 24px;
    font-weight: 600;
  }

  /* 禁用移动端hover效果 */
  .highlight-card:hover,
  .vision-item:hover,
  .participant-category:hover {
    transform: none;
    box-shadow: inherit;
  }

  /* Hero Section 移动端优化 */
  .hero-section {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
  }

  .hero-image {
    border-radius: 0.75rem;
  }

  /* Intro Card 移动端优化 */
  .intro-card {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .intro-header {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .intro-icon {
    font-size: 1.5rem;
  }

  .intro-header h2 {
    font-size: 1.25rem;
  }

  .intro-text {
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1.25rem;
  }

  .stage-vision {
    margin-top: 1.25rem;
  }

  .vision-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .vision-item {
    padding: 0.75rem;
    border-radius: 0.5rem;
  }

  .vision-icon {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
  }

  .vision-item h4 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }

  .vision-item p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  /* Highlights Section 移动端优化 */
  .highlights-section {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .highlights-header {
    margin-bottom: 1.25rem;
  }

  .highlights-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .highlights-header h2 {
    font-size: 1.4rem;
    margin-bottom: 0.25rem;
  }

  .highlights-subtitle {
    font-size: 0.9rem;
  }

  .highlights-grid {
    gap: 1rem;
  }

  .highlight-card {
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .highlight-card-header {
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .highlight-card-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.5rem;
  }

  .highlight-card h3 {
    font-size: 1.1rem;
  }

  .highlight-card-content p {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
  }

  .impact-stats {
    flex-direction: row;
    gap: 0.5rem;
  }

  .impact-stat {
    padding: 0.75rem 0.5rem;
    border-radius: 0.5rem;
  }

  .impact-number {
    font-size: 1.1rem;
    margin-bottom: 0.125rem;
  }

  .impact-label {
    font-size: 0.7rem;
  }

  .stage-features {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .stage-feature {
    padding: 0.5rem;
    border-radius: 0.375rem;
  }

  .stage-feature-icon {
    font-size: 1rem;
  }

  .stage-feature-text {
    font-size: 0.8rem;
  }

  .attraction-regions {
    gap: 0.5rem;
  }

  .region-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
  }

  /* Participation Section 移动端优化 */
  .participation-section {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .participation-header {
    margin-bottom: 1.25rem;
  }

  .participation-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .participation-header h2 {
    font-size: 1.4rem;
    margin-bottom: 0.25rem;
  }

  .participation-subtitle {
    font-size: 1rem;
  }

  .participation-intro {
    margin-bottom: 1.25rem;
  }

  .participation-intro p {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .participant-categories {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.25rem;
  }

  .participant-category {
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .category-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .participant-category h3 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .category-features {
    gap: 0.375rem;
  }

  .category-feature {
    font-size: 0.8rem;
    padding: 0.375rem;
    border-radius: 0.375rem;
    border-left-width: 2px;
  }

  .join-cta {
    margin-top: 1.25rem;
  }

  .cta-background {
    padding: 1.5rem;
    border-radius: 0.75rem;
  }

  .cta-stars {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .cta-background h3 {
    font-size: 1.4rem;
    margin-bottom: 0.375rem;
  }

  .cta-background p {
    font-size: 1rem;
    margin-bottom: 1.25rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .primary-btn, .secondary-btn {
    width: 100%;
    max-width: 240px;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    border-radius: 1.5rem;
  }
}

/* 平板端优化 */
@media (min-width: 481px) and (max-width: 767px) {
  .content {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .section {
    margin-bottom: 1.5rem;
  }

  .vision-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .participant-categories {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .impact-stats {
    gap: 1rem;
  }

  .stage-features {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

@media (min-width: 768px) {
  .vision-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .highlights-grid {
    gap: 2rem;
  }

  .highlight-card {
    padding: 2rem;
  }

  .impact-stats {
    gap: 1.5rem;
  }

  .stage-features {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .participant-categories {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .intro-card, .highlights-section, .participation-section {
    padding: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .hero-section {
    height: 420px;
  }

  .hero-title {
    font-size: 3.2rem;
  }

  .hero-features {
    gap: 1.5rem;
  }

  .highlights-grid {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .highlight-card {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2.5rem;
  }

  .highlight-card-header {
    flex-shrink: 0;
    margin-bottom: 0;
  }

  .highlight-card-content {
    flex: 1;
  }

  .participant-categories {
    grid-template-columns: repeat(4, 1fr);
  }

  .cta-buttons {
    gap: 2rem;
  }
}


@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

</style>
