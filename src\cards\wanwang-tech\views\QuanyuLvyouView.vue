<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

// 系统截图数据
const systemImages = [
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/quangyuditu1.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/quangyuditu2.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/quangyuditu3.jpg'
]

const currentImageIndex = ref(0)

const nextImage = () => {
  currentImageIndex.value = (currentImageIndex.value + 1) % systemImages.length
}

const prevImage = () => {
  currentImageIndex.value = currentImageIndex.value === 0 ? systemImages.length - 1 : currentImageIndex.value - 1
}

const goToImage = (index: number) => {
  currentImageIndex.value = index
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('QuanyuLvyouView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>全域旅游智慧地图管理系统</h1>
    </div>

    <div class="content">
      <!-- 项目概述 -->
      <div class="section intro-section">
        <div class="intro-card">
          <div class="intro-content">
            <div class="intro-title">
              <h3>全域旅游智慧地图管理系统</h3>
              <div class="title-decoration"></div>
            </div>
            <div class="intro-description">
              <div class="description-item">
                <div class="item-icon">🌍</div>
                <p>全域旅游智慧地图管理系统是由江西万网科技有限公司倾力打造的一款全方位、智能化的旅游管理系统。本系统主要分为小程序端和PC管理端，通过先进的软件开发技术和丰富的开发经验，旨在提升旅游目的地的管理和服务水平，为游客提供更加便捷、智能的旅游体验。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统截图展示 -->
      <div class="section gallery-section">
        <div class="section-header">
          <h2>系统功能展示</h2>
        </div>
        <div class="carousel-container">
          <div class="carousel-wrapper">
            <div class="carousel-main">
              <img 
                :src="systemImages[currentImageIndex]" 
                :alt="`全域旅游智慧地图管理系统截图 ${currentImageIndex + 1}`"
                class="carousel-image"
              />
              <button @click="prevImage" class="carousel-btn prev-btn">‹</button>
              <button @click="nextImage" class="carousel-btn next-btn">›</button>
            </div>
            <div class="carousel-thumbnails">
              <div 
                v-for="(image, index) in systemImages" 
                :key="index"
                @click="goToImage(index)"
                :class="['thumbnail', { active: index === currentImageIndex }]"
              >
                <img :src="image" :alt="`缩略图 ${index + 1}`" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 小程序端功能 -->
      <div class="section miniprogram-section">
        <div class="section-header">
          <h2>小程序端功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🏞️</div>
            <h3>景区景点</h3>
            <div class="feature-content">
              <p>提供各景区的详细信息、实时导航和实景图，便于游客规划行程。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🍽️</div>
            <h3>餐饮服务</h3>
            <div class="feature-content">
              <p>搜索附近餐厅和美食，支持在线预订和支付。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏛️</div>
            <h3>文化馆、博物馆</h3>
            <div class="feature-content">
              <p>展示各类文化活动信息和门票预订服务。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏨</div>
            <h3>宾馆住宿</h3>
            <div class="feature-content">
              <p>提供酒店预订、在线支付及用户评价功能。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎯</div>
            <h3>休闲场所</h3>
            <div class="feature-content">
              <p>提供各类娱乐和健身场所的信息及预订服务。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🛍️</div>
            <h3>购物推荐</h3>
            <div class="feature-content">
              <p>推荐当地特色商品及购物场所，方便游客购物。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🚻</div>
            <h3>旅游厕所、停车</h3>
            <div class="feature-content">
              <p>实时查询厕所位置和停车位信息，解决游客的燃眉之急。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🚌</div>
            <h3>精品路线、公交路线</h3>
            <div class="feature-content">
              <p>提供旅游线路规划和公交查询服务，便于游客出行。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎧</div>
            <h3>语音导览</h3>
            <div class="feature-content">
              <p>提供景点语音讲解服务，让游客深入了解景区文化。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🗺️</div>
            <h3>地图导览</h3>
            <div class="feature-content">
              <p>基于地图的导航服务，指引游客到达目的地。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- PC管理端功能 -->
      <div class="section pc-management-section">
        <div class="section-header">
          <h2>PC管理端功能</h2>
        </div>
        <div class="management-grid">
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">📊</div>
              <h3>旅游控制台</h3>
            </div>
            <div class="management-content">
              <p>提供全面的旅游数据监控和管理功能。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">🏞️</div>
              <h3>景区管理模块</h3>
            </div>
            <div class="management-content">
              <p>实现景区分组、景区信息管理等功能。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">📍</div>
              <h3>景点管理模块</h3>
            </div>
            <div class="management-content">
              <p>进行景点信息管理、分类及游览路线设置。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">🚌</div>
              <h3>公交线路管理</h3>
            </div>
            <div class="management-content">
              <p>进行路线分类、路线信息管理。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">🎨</div>
              <h3>手绘景点图制作</h3>
            </div>
            <div class="management-content">
              <p>为景区提供专业手绘地图制作服务。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">🗺️</div>
              <h3>地图嵌入功能</h3>
            </div>
            <div class="management-content">
              <p>实现地图与旅游信息的完美融合。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">⚙️</div>
              <h3>功能模块管理</h3>
            </div>
            <div class="management-content">
              <p>对景点、酒店、商户、停车场、厕所、公共设施等进行统一管理，实现语音导览、一键导览等功能。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">📱</div>
              <h3>多媒体展示</h3>
            </div>
            <div class="management-content">
              <p>景区线路、概况、图文、视频展示，多维度展示景区魅力，提升游客兴趣。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">📞</div>
              <h3>投诉维权</h3>
            </div>
            <div class="management-content">
              <p>增设投诉、维权联系方式，为游客提供便捷的投诉和维权渠道。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">🔧</div>
              <h3>系统管理</h3>
            </div>
            <div class="management-content">
              <p>进行系统配置、数据备份及安全管理。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">🔐</div>
              <h3>权限管理</h3>
            </div>
            <div class="management-content">
              <p>进行用户角色、权限设置及登录验证管理。</p>
            </div>
          </div>
          <div class="management-item">
            <div class="management-header">
              <div class="management-icon">🔍</div>
              <h3>运维管理</h3>
            </div>
            <div class="management-content">
              <p>进行系统日志管理、性能监控及升级维护等操作。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>系统优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">🌐</div>
            <h3>全方位覆盖</h3>
            <p>涵盖旅游全要素，提供一站式智慧旅游解决方案。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🤖</div>
            <h3>智能化管理</h3>
            <p>运用大数据和人工智能技术，实现智能化旅游管理。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📱</div>
            <h3>双端协同</h3>
            <p>小程序端和PC管理端完美配合，提升管理效率。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <h3>定制化服务</h3>
            <p>深入了解客户需求，提供个性化定制解决方案。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔄</div>
            <h3>实时更新</h3>
            <p>支持实时数据更新，确保信息的准确性和时效性。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">👥</div>
            <h3>用户体验优化</h3>
            <p>注重用户体验设计，提供便捷、直观的操作界面。</p>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="value-content">
          <p>全域旅游智慧地图管理系统通过整合旅游资源，构建了完整的智慧旅游生态体系。系统不仅为游客提供了便捷的旅游服务，更为旅游管理部门提供了高效的管理工具。通过小程序端的便民服务和PC管理端的专业管理功能，实现了旅游服务的数字化转型。江西万网科技有限公司深入了解目标客户的业务需求和痛点，为其提供定制化的解决方案，有效提高了客户满意度和忠诚度，为全域旅游发展注入了新的活力，推动了旅游产业的高质量发展。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 项目介绍样式 */
.intro-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(33, 150, 243, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%);
}

.intro-content {
  text-align: center;
}

.intro-title {
  margin-bottom: 2rem;
}

.intro-title h3 {
  color: #1976d2;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.title-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-description {
  display: grid;
  gap: 1.5rem;
  text-align: left;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #2196f3;
}

.item-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.description-item p {
  margin: 0;
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
}

/* 轮播图样式 */
.carousel-container {
  max-width: 100%;
  margin: 0 auto;
}

.carousel-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.carousel-main {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.prev-btn {
  left: 10px;
}

.next-btn {
  right: 10px;
}

.carousel-thumbnails {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.thumbnail {
  width: 120px;
  height: 80px;
  border-radius: 0.5rem;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.thumbnail:hover {
  border-color: #1693d2;
  transform: scale(1.05);
}

.thumbnail.active {
  border-color: #1693d2;
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 小程序端功能样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.8rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.feature-content p {
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* PC管理端功能样式 */
.management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.management-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.management-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.management-header {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  padding: 1.2rem 1.5rem;
  display: flex;
  align-items: center;
}

.management-icon {
  font-size: 1.3rem;
  margin-right: 0.8rem;
}

.management-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.management-content {
  padding: 1.5rem;
}

.management-content p {
  color: #333;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
  text-align: justify;
}

/* 系统优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* 应用价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .intro-description {
    gap: 1rem;
  }

  .description-item {
    padding: 1rem;
  }

  .carousel-main {
    height: 250px;
  }

  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .thumbnail {
    width: 80px;
    height: 60px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .management-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .management-header {
    padding: 1rem 1.2rem;
  }

  .management-content {
    padding: 1.2rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }

  .intro-title h3 {
    font-size: 1.5rem;
  }
}
</style>
