import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'aiSzGuideHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiTuiJieGuan/LOGO.jpeg',
      title: 'AI数智推介官'
    }
  },
  {
    path: '/project-intro',
    name: 'aiSzGuideProjectIntro',
    component: () => import('./views/ProjectIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiTuiJieGuan/LOGO.jpeg',
      title: 'AI数智推介官 - 项目介绍'
    }
  },
  {
    path: '/case-center',
    name: 'aiSzGuideCaseCenter',
    component: () => import('./views/CaseCenterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiTuiJieGuan/LOGO.jpeg',
      title: 'AI数智推介官 - 案例中心'
    }
  }
]

export default routes 