# 直接使用内置的 dict 类型
Args = dict
Output = dict

async def main(args: Args) -> Output:
    # ✅ 验证输入参数
    if not args or not isinstance(args, dict):
        return {"error": "Invalid input: args is null or not a dictionary"}
    
    params = args.get('params')
    if not params or not isinstance(params, dict):
        return {"error": "Invalid input: params is null or not a dictionary"}
    
    # ✅ 安全地提取参数，处理 None 值
    title = params.get('title') or ''
    desc = params.get('desc') or ''
    url = params.get('url') or ''
    nickname = params.get('nickname') or ''
    
    # ✅ 数字类型的安全处理
    likedCount = params.get('likedCount')
    if likedCount is None or likedCount == '':
        likedCount = 0
    else:
        try:
            likedCount = int(likedCount) if likedCount else 0
        except (ValueError, TypeError):
            likedCount = 0
    
    collectedCount = params.get('collectedCount')
    if collectedCount is None or collectedCount == '':
        collectedCount = 0
    else:
        try:
            collectedCount = int(collectedCount) if collectedCount else 0
        except (ValueError, TypeError):
            collectedCount = 0
    
    videoUrl = params.get('videoUrl') or ''
    imageList = params.get('imageList')
    if not imageList or not isinstance(imageList, list):
        imageList = []

    # ✅ 安全处理图片地址
    image_urls = []
    for item in imageList:
        if isinstance(item, dict) and item.get('urlDefault'):
            image_urls.append(item['urlDefault'])

    # ✅ 构建记录，确保所有字段都有值
    records = [{"fields": {
        "笔记链接": url,
        "标题": title,
        "内容": desc,
        "作者": nickname,
        "点赞数": likedCount,
        "收藏数": collectedCount,
        "图片地址": imageList,
        "视频地址": videoUrl,
    }}]

    # ✅ 验证记录是否有效
    if not records or not records[0].get("fields"):
        return {"error": "Failed to create valid records"}

    # 构建输出对象
    ret = {
        "records": records
    }
    return ret
