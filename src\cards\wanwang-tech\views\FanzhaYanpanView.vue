<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('FanzhaYanpanView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>反诈研判系统</h1>
    </div>

    <div class="content">
      <!-- 产品概述 -->
      <div class="section overview-section">
        <div class="section-header">
          <h2>产品概述</h2>
        </div>
        <div class="overview-content">
          <div class="overview-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/fanzhayanpan11.jpg" alt="反诈研判系统" />
          </div>
          <div class="overview-text">
            <p>为达到全面、实时、准确的分析研判辖区内案事件的特点和规律，达到"预防犯罪、发现犯罪、打击犯罪"为目的，进一步突出信息在侦查办案中的作用，更好的发挥信息化作战的效能，江西万网科技有限公司研发反诈研判系统。</p>
            <p>反诈研判系统，基于先进的云服务体系，建立统一的大数据平台，整合各类相关联的内外部数据，实现信息最大共享的基础上，构建警务智慧三大核心智慧应用：内部公文共享、智慧求助管理、智慧情报研判。</p>
          </div>
        </div>
      </div>

      <!-- 系统特色 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>系统特色</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🛡️</div>
            <h3>预防犯罪</h3>
            <div class="feature-content">
              <p>通过大数据分析和智能研判，提前识别潜在风险，实现犯罪预防，从源头上减少电信网络诈骗案件的发生。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔍</div>
            <h3>发现犯罪</h3>
            <div class="feature-content">
              <p>利用先进的数据挖掘技术，快速发现犯罪线索和异常行为模式，提高案件发现效率。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">⚖️</div>
            <h3>打击犯罪</h3>
            <div class="feature-content">
              <p>为执法部门提供精准的情报支撑，协助快速锁定犯罪嫌疑人，提高打击犯罪的成功率。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心功能 -->
      <div class="section core-functions-section">
        <div class="section-header">
          <h2>三大核心智慧应用</h2>
        </div>
        <div class="core-functions-grid">
          <div class="function-item">
            <div class="function-icon">📄</div>
            <h3>内部公文共享</h3>
            <div class="function-content">
              <p>建立统一的公文管理平台，实现各部门间公文的快速流转和共享，提高工作效率，确保信息传达的及时性和准确性。</p>
            </div>
          </div>
          <div class="function-item">
            <div class="function-icon">🆘</div>
            <h3>智慧求助管理</h3>
            <div class="function-content">
              <p>构建智能化的求助处理系统，快速响应群众求助，合理分配警力资源，提升服务群众的质量和效率。</p>
            </div>
          </div>
          <div class="function-item">
            <div class="function-icon">🧠</div>
            <h3>智慧情报研判</h3>
            <div class="function-content">
              <p>运用人工智能和大数据技术，对海量情报信息进行深度分析和研判，为决策提供科学依据。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>系统优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">☁️</div>
            <h3>先进云服务体系</h3>
            <p>基于先进的云服务体系，确保系统的稳定性、可扩展性和安全性，支持大规模数据处理和高并发访问。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🗄️</div>
            <h3>统一大数据平台</h3>
            <p>建立统一的大数据平台，整合各类相关联的内外部数据，实现数据的统一管理和高效利用。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔗</div>
            <h3>信息最大共享</h3>
            <p>打破信息孤岛，实现各部门间信息的最大化共享，提高协作效率和决策质量。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <h3>高效数据流转</h3>
            <p>实现信息共享和数据高效流转，是实现反诈工作智能化研判的一大利器。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔧</div>
            <h3>全面数据服务</h3>
            <p>实现数据的管控、存储、分析挖掘和基础数据服务，为各类应用提供强有力的数据支撑。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <h3>智能化研判</h3>
            <p>运用先进算法和模型，实现反诈工作的智能化研判，提高工作精准度和效率。</p>
          </div>
        </div>
      </div>

      <!-- 系统展示 -->
      <div class="section platform-section">
        <div class="section-header">
          <h2>系统界面展示</h2>
        </div>
        <div class="platform-images">
          <div class="platform-item">
            <div class="platform-header">
              <div class="platform-icon">💻</div>
              <h3>系统主界面</h3>
            </div>
            <div class="platform-content">
              <div class="platform-text">
                <p>反诈研判系统主界面，集成了各项核心功能模块，提供直观的操作界面和数据展示，方便用户快速访问各项功能。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/fanzhayanpan1.jpg" alt="系统主界面" />
              </div>
            </div>
          </div>

          <div class="platform-item">
            <div class="platform-header">
              <div class="platform-icon">📊</div>
              <h3>数据分析界面</h3>
            </div>
            <div class="platform-content">
              <div class="platform-text">
                <p>数据分析和研判界面，通过可视化图表展示各类统计数据和分析结果，为决策提供科学依据和直观参考。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/fanzhayanban2.jpg" alt="数据分析界面" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="value-content">
          <p>反诈研判系统通过整合各类数据资源，运用先进的分析技术，为反诈工作提供了强有力的技术支撑。系统不仅提高了案件研判的准确性和效率，还增强了各部门间的协作能力，为构建全方位、立体化的反诈防护体系奠定了坚实基础。通过智能化的研判分析，有效提升了预防、发现和打击电信网络诈骗犯罪的能力，为维护社会安全稳定和保护人民群众财产安全发挥了重要作用。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 产品概述样式 */
.overview-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.overview-image {
  width: 250px;
  height: 200px;
  flex-shrink: 0;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.overview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overview-text {
  flex: 1;
  color: #333;
  line-height: 1.8;
}

.overview-text p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  text-align: justify;
}

/* 系统特色样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.feature-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 核心功能样式 */
.core-functions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.function-item {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.function-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.function-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.function-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.function-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 系统优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.advantage-item:hover {
  border-color: #1693d2;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1693d2;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #666;
  margin: 0;
}

/* 平台展示样式 */
.platform-images {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.platform-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
}

.platform-header {
  background: linear-gradient(135deg, #1693d2 0%, #0d7ab8 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
}

.platform-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.platform-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.platform-content {
  padding: 2rem;
}

.platform-text p {
  color: #333;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.platform-text img {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  display: block;
}

/* 应用价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .overview-content {
    flex-direction: column;
    text-align: center;
  }

  .overview-image {
    width: 150px;
    height: 150px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .core-functions-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .function-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .platform-header {
    padding: 1rem 1.5rem;
  }

  .platform-content {
    padding: 1.5rem;
  }

  .platform-text img {
    max-width: 100%;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }
}
</style>
