<template>
  <div class="third-hospital-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>汉氏联合三级医院(筹)</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/sanjiyiyuan.png"
          alt="汉氏联合三级医院"
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">汉氏联合三级医院(筹)</h2>
          <p class="banner-subtitle">擘画未来，一座以细胞治疗为核心的智慧型医院</p>
        </div>
      </div>

      <!-- 蓝图数据化模块 -->
      <div class="section blueprint-section">
        <h2 class="section-title"><el-icon><DataAnalysis /></el-icon> 蓝图数据化 · 未来可期</h2>
        <div class="blueprint-data">
          <div class="data-item">
            <div class="data-number">
              <span class="data-main">107</span>
              <span class="data-unit">亩</span>
            </div>
            <div class="data-label">规划占地面积</div>
          </div>
          <div class="data-item">
            <div class="data-number">
              <span class="data-main">500</span>
              <span class="data-unit">张</span>
            </div>
            <div class="data-label">设计床位数量</div>
          </div>
          <div class="data-item">
            <div class="data-number">
              <span class="data-main">8.3万</span>
              <span class="data-unit">㎡</span>
            </div>
            <div class="data-label">总建筑面积</div>
          </div>
          <div class="data-item">
            <div class="data-number">
              <span class="data-main">2024</span>
              <span class="data-unit">年</span>
            </div>
            <div class="data-label">预计投入运营</div>
          </div>
        </div>
      </div>

      <!-- 核心学科规划模块 -->
      <div class="section discipline-section">
        <h2 class="section-title"><el-icon><Medal /></el-icon> 核心学科规划 · 精准定位</h2>
        <p class="discipline-subtitle">打造特色鲜明的区域医疗新高地</p>

        <!-- 中心核心 -->
        <div class="discipline-center-card">
          <div class="center-badge">核心</div>
          <div class="center-icon">🧬</div>
          <h3 class="center-title">细胞治疗中心</h3>
          <p class="center-desc">以细胞治疗为核心，整合多学科资源，构建区域医疗新高地</p>
          <div class="center-features">
            <span class="feature-tag">干细胞治疗</span>
            <span class="feature-tag">免疫细胞治疗</span>
            <span class="feature-tag">再生医学</span>
          </div>
        </div>

        <!-- 辐射科室 -->
        <div class="discipline-cards">
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><FirstAidKit /></el-icon>
            </div>
            <h4 class="card-title">血液病科</h4>
            <p class="card-desc">专业血液疾病诊疗</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <h4 class="card-title">肿瘤科</h4>
            <p class="card-desc">肿瘤综合治疗中心</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <h4 class="card-title">心脑血管科</h4>
            <p class="card-desc">心脑血管疾病防治</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><User /></el-icon>
            </div>
            <h4 class="card-title">老年病科</h4>
            <p class="card-desc">老年综合医疗服务</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><Cpu /></el-icon>
            </div>
            <h4 class="card-title">风湿免疫科</h4>
            <p class="card-desc">免疫系统疾病诊疗</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><Female /></el-icon>
            </div>
            <h4 class="card-title">高端妇产科</h4>
            <p class="card-desc">高端妇产医疗服务</p>
          </div>
        </div>
      </div>

      <!-- 产医融合模块 -->
      <div class="section integration-section">
        <h2 class="section-title"><el-icon><Connection /></el-icon> 产医融合 · 独特优势</h2>
        <p class="integration-subtitle">从实验室到病床，转化一步到位</p>

        <!-- 产医融合流程图 -->
        <div class="integration-flow">
          <div class="flow-step">
            <div class="step-icon">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="step-content">
              <h3>细胞谷</h3>
              <p>国家级细胞库与研发中心</p>
            </div>
            <div class="flow-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="flow-step">
            <div class="step-icon">
              <el-icon><Link /></el-icon>
            </div>
            <div class="step-content">
              <h3>无缝对接</h3>
              <p>院士工作站与博士后科研工作站</p>
            </div>
            <div class="flow-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="flow-step">
            <div class="step-icon">
              <el-icon><FirstAidKit /></el-icon>
            </div>
            <div class="step-content">
              <h3>三级医院</h3>
              <p>临床研究与应用转化中心</p>
            </div>
          </div>
        </div>



      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
// 导入所需的组件和图标
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  DataAnalysis,
  Medal,
  FirstAidKit,
  Monitor,
  User,
  Cpu,
  Female,
  Connection,
  DataBoard,
  Link,
  ArrowRight
} from '@element-plus/icons-vue'

import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.third-hospital-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px; /* 为固定头部留出空间 */
  padding-bottom: 80px; /* 为底部导航栏留出空间 */
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.back-button:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 8px; /* 进一步减少内边距 */
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px; /* 减小圆角 */
  overflow: hidden;
  margin-bottom: 16px; /* 进一步减少下边距 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  transition: all 0.5s ease; /* 添加过渡效果 */
}

.banner-section:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease; /* 添加图片过渡效果 */
}

.banner-section:hover .banner-image {
  transform: scale(1.03); /* 悬停时图片轻微放大 */
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px; /* 进一步减少内边距 */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%); /* 增强渐变效果 */
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.3; /* 添加行高优化 */
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5; /* 添加行高优化 */
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px; /* 减小圆角 */
  padding: 12px; /* 减少内边距 */
  margin-bottom: 12px; /* 减少下边距 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 20px; /* 优化字体大小 */
  color: #0f9da8;
  margin: 0 0 12px 0; /* 进一步减少下边距 */
  gap: 8px;
  line-height: 1.4; /* 添加行高优化 */
}

/* 蓝图数据化模块样式 */
.blueprint-data {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.data-item {
  background: linear-gradient(135deg, #f0f9fa, #e6f7f8);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #d0eef0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.data-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(15, 157, 168, 0.15);
}

.data-number {
  font-size: 36px;
  font-weight: 700;
  color: #0f9da8;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 3px;
  margin-bottom: 10px;
}

.data-main {
  font-size: 36px;
}

.data-unit {
  font-size: 16px;
  color: #0f9da8;
  font-weight: 500;
  margin-left: 2px;
}

.data-label {
  font-size: 14px;
  color: #666;
}

/* 核心学科规划模块样式 */
.discipline-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  text-align: center;
}

/* 中心核心卡片 */
.discipline-center-card {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  border-radius: 20px;
  padding: 30px;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(15, 157, 168, 0.3);
  transition: all 0.4s ease;
  margin-bottom: 30px;
}

.discipline-center-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(15, 157, 168, 0.4);
}

.center-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.center-icon {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.9;
}

.center-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 15px 0;
}

.center-desc {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.9;
}

.center-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* 专科科室卡片网格 */
.discipline-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.discipline-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  border: 2px solid #f0f9fa;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.discipline-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.discipline-card:hover::before {
  transform: scaleX(1);
}

.discipline-card:hover {
  transform: translateY(-5px);
  border-color: #0f9da8;
  box-shadow: 0 10px 30px rgba(15, 157, 168, 0.15);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f9fa, #e6f7f8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f9da8;
  font-size: 20px;
  margin: 0 auto 12px;
  transition: all 0.3s ease;
}

.discipline-card:hover .card-icon {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  transform: scale(1.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.card-desc {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* 产医融合模块样式 */
.integration-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  text-align: center;
}

/* 产医融合流程图 */
.integration-flow {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.flow-step {
  background: white;
  border-radius: 16px;
  padding: 20px;
  border: 2px solid #f0f9fa;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.flow-step:hover {
  transform: translateY(-3px);
  border-color: #0f9da8;
  box-shadow: 0 8px 30px rgba(15, 157, 168, 0.1);
}

.step-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.flow-step:hover .step-icon {
  transform: scale(1.1);
}

.step-icon .el-icon {
  font-size: 28px;
  color: white;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.step-content p {
  font-size: 15px;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.flow-arrow {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #0f9da8;
  font-size: 24px;
  background: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s infinite;
}

.flow-step:last-child .flow-arrow {
  display: none;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
}









.flow-arrow {
  font-size: 24px;
  color: #0f9da8;
  margin: 0 15px;
  opacity: 0.7;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .blueprint-data {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
  }

  .data-item {
    padding: 25px;
  }

  .discipline-cards {
    grid-template-columns: repeat(3, 1fr);
  }

  .integration-flow {
    flex-direction: row;
    align-items: stretch;
  }

  .flow-step {
    flex: 1;
    margin-right: 30px;
  }

  .flow-step:last-child {
    margin-right: 0;
  }

  .flow-steps {
    flex-direction: row;
  }
}

@media (max-width: 1024px) and (min-width: 768px) {
  .integration-flow {
    flex-direction: column;
    gap: 16px;
  }

  .flow-step {
    margin-right: 0;
  }

  .flow-arrow {
    display: none;
  }
}

@media (max-width: 767px) {
  .content {
    padding: 8px;
  }

  /* 横幅优化 */
  .banner-section {
    margin-bottom: 16px;
  }

  .banner-image {
    height: 200px;
    object-fit: cover;
  }

  .banner-overlay {
    padding: 16px;
  }

  .banner-title {
    font-size: 20px;
    margin-bottom: 6px;
  }

  .banner-subtitle {
    font-size: 14px;
  }

  /* 通用部分优化 */
  .section {
    padding: 12px;
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .discipline-subtitle,
  .integration-subtitle {
    font-size: 13px;
    margin-bottom: 16px;
  }

  .blueprint-data {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .data-item {
    padding: 16px 12px;
  }

  .data-number {
    font-size: 28px;
  }

  .data-unit {
    font-size: 14px;
  }

  .data-label {
    font-size: 12px;
  }

  .discipline-center-card {
    padding: 20px 16px;
    margin-bottom: 16px;
  }

  .center-title {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .center-desc {
    font-size: 13px;
    margin-bottom: 12px;
  }

  .feature-tag {
    font-size: 11px;
    padding: 4px 8px;
  }

  .discipline-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .discipline-card {
    padding: 12px;
  }

  .card-icon {
    width: 36px;
    height: 36px;
    margin-bottom: 8px;
  }

  .card-icon .el-icon {
    font-size: 18px;
  }

  .card-title {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .card-desc {
    font-size: 11px;
  }

  .integration-flow {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }

  .flow-step {
    margin-right: 0;
    padding: 14px;
    flex-direction: row;
    text-align: left;
  }

  .step-icon {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }

  .step-icon .el-icon {
    font-size: 22px;
  }

  .step-content h3 {
    font-size: 16px;
    margin-bottom: 4px;
  }

  .step-content p {
    font-size: 13px;
    line-height: 1.4;
  }

  .flow-arrow {
    display: none;
  }

  .step-content {
    max-width: none;
  }
}

@media (max-width: 480px) {
  .blueprint-data {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .data-item {
    padding: 15px 12px;
  }

  .data-number {
    font-size: 26px;
  }

  .data-unit {
    font-size: 13px;
  }

  .data-label {
    font-size: 12px;
  }

  .discipline-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .discipline-center-card {
    padding: 25px 20px;
  }

  .center-title {
    font-size: 20px;
  }

  .center-desc {
    font-size: 14px;
  }





  .integration-flow {
    padding: 20px;
  }

  .flow-title {
    font-size: 18px;
  }
}

@media (max-width: 360px) {
  .discipline-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}
@media (max-width: 768px) {
  .back-button {
    display: none;
  }
}
</style>