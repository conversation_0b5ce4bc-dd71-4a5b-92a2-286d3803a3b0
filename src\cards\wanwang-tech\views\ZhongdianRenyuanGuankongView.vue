<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 尝试使用浏览器的返回功能
  if (window.history.length > 1 && document.referrer) {
    const referrerUrl = new URL(document.referrer)
    const currentUrl = new URL(window.location.href)

    if (referrerUrl.origin === currentUrl.origin && referrerUrl.pathname.includes('/card/wanwang-tech')) {
      router.go(-1)
      return
    }
  }

  // 如果没有合适的历史记录或来源不是本站，则跳转到案例中心
  router.push('/case-center')
}

// 核心功能数据
const coreFeatures = [
  {
    title: '涉诈人员分值评定与等级划分',
    description: '通过对涉诈人员的多维度数据分析，进行自动打分评定。根据评分结果，将涉诈人员划分为一类、二类、三类、四类人员。风险等级分为：高风险、中风险、低风险三个等级。动态调整分值，确保管控措施的及时性和针对性。',
    icon: '📊'
  },
  {
    title: '打卡上报与自我管理',
    description: '提供小程序端，支持涉诈人员主动打卡上报位置及生活状况。引入积分机制，鼓励涉诈人员积极配合管控工作，增强自我约束意识。管控人员和领导干部通过平台审核打卡信息，确保上报数据的真实性和准确性。',
    icon: '📱'
  },
  {
    title: '预警与责任监督',
    description: '设立预警机制，实时监控管控责任人的履职情况。对未按时督促涉诈人员打卡上报或监管不到位的责任人自动触发预警。实施考核排名，强化责任落实，提升管控效率。',
    icon: '⚠️'
  },
  {
    title: '数据可视化与分析',
    description: '可视化大屏端展示涉诈人员、管控责任人员、管控领导等关键数据。通过图表、地图等多种形式，直观呈现涉诈人员分布、趋势变化、任务完成情况等信息。为市级、县级公安部门领导提供决策支持，便于监督指导各乡镇管控工作。',
    icon: '📈'
  }
]

// 系统架构数据
const systemArchitecture = [
  {
    title: '小程序打卡上报端',
    description: '涉诈人员打卡上报、管控人员审核、管控领导干部监督',
    features: ['操作简便', '实时性强', '支持移动端访问'],
    images: [
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/zhongdian1111.png',
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/zhongdian1112.png'
    ]
  },
  {
    title: '系统管理端',
    description: '功能全面，权限清晰，支持高效的数据处理与管理',
    features: [
      '涉诈人员管理：信息录入、管控登记、等级变更、积分记录',
      '管控人员管理：人员信息、部门管理、区域管理',
      '管控中心：等待管控、正在管控、撤销管控、管控异常处理',
      '任务中心：任务记录与分配',
      '数据统计：乡镇考核、人员考核',
      '应用管理：帮助中心',
      '评定变更：变更申请与审核',
      '系统管理：系统设置、系统日志、登录日志、权限管理、运维管理'
    ],
    images: [
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/zhongdian1113.png',
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/zhongdian1114.png'
    ]
  },
  {
    title: '可视化领导决策端',
    description: '直观、动态、全面，为决策提供有力支持',
    features: [
      '涉诈人员统计：总数、男女比例、新增数等',
      '涉诈人员分类与趋势分析',
      '全市地图展示各县涉诈人员数据，支持点击进入县级平台',
      '各县区分管涉诈人员统计与任务完成情况',
      '管控人员数量与构成分析',
      '管控等级变化展示'
    ],
    images: [
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/zhongdian1115.png'
    ]
  }
]

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>涉诈人员动态管控平台</h1>
    </div>

    <div class="content">
      <!-- 产品概述 -->
      <div class="section overview-section">
        <div class="section-header">
          <h2>产品概述</h2>
        </div>
        <div class="overview-content">
          <div class="overview-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhongDianRenYuanGuanKong.png" alt="涉诈人员动态管控平台" />
          </div>
          <div class="overview-text">
            <p>涉诈人员动态管控平台是公安局为应对日益严峻的涉诈风险而精心设计的一款智能化管理工具。该平台深度融合大数据、云计算等前沿技术，实现对涉诈人员的精准识别、动态监控与高效管理，旨在提升公安部门的管控效能，有效遏制电信网络诈骗犯罪活动，维护社会稳定和公众财产安全。</p>
          </div>
        </div>
      </div>

      <!-- 核心功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>核心功能</h2>
        </div>
        <div class="features-grid">
          <div v-for="(feature, index) in coreFeatures" :key="index" class="feature-card">
            <div class="feature-icon">{{ feature.icon }}</div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 系统架构与组成 -->
      <div class="section architecture-section">
        <div class="section-header">
          <h2>系统架构与组成</h2>
          <p class="section-subtitle">系统分为三端：小程序打卡上报端、系统管理端、可视化领导决策端</p>
        </div>
        
        <div v-for="(system, index) in systemArchitecture" :key="index" class="system-block">
          <div class="system-header">
            <h3>{{ index + 1 }}. {{ system.title }}</h3>
            <p class="system-description">{{ system.description }}</p>
          </div>
          
          <div class="system-content">
            <div class="system-features">
              <h4>功能特点：</h4>
              <ul>
                <li v-for="(feature, fIndex) in system.features" :key="fIndex">{{ feature }}</li>
              </ul>
            </div>
            
            <div class="system-images">
              <div v-for="(image, imgIndex) in system.images" :key="imgIndex" class="system-image">
                <img :src="image" :alt="`${system.title}截图${imgIndex + 1}`" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

.section-subtitle {
  margin: 1rem 0 0 0;
  color: #666;
  font-size: 1.1rem;
  font-weight: 500;
}

/* 产品概述样式 */
.overview-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.overview-image {
  width: 200px;
  height: 200px;
  flex-shrink: 0;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.overview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overview-text {
  flex: 1;
}

.overview-text p {
  margin: 0;
  color: #555;
  line-height: 1.8;
  font-size: 1.1rem;
  text-align: justify;
}

/* 核心功能样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(22, 147, 210, 0.15);
  border-color: #1693d2;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1693d2;
}

.feature-card p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  text-align: justify;
}

/* 系统架构样式 */
.system-block {
  margin-bottom: 3rem;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  overflow: hidden;
}

.system-block:last-child {
  margin-bottom: 0;
}

.system-header {
  background: #f8fafc;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.system-header h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #1693d2;
}

.system-description {
  margin: 0;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
}

.system-content {
  padding: 2rem;
}

.system-features {
  margin-bottom: 2rem;
}

.system-features h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.system-features ul {
  margin: 0;
  padding-left: 1.5rem;
}

.system-features li {
  margin-bottom: 0.5rem;
  color: #555;
  line-height: 1.6;
}

.system-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.system-image {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.system-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header h1 {
    font-size: 1rem;
  }

  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .overview-content {
    flex-direction: column;
    text-align: center;
  }

  .overview-image {
    width: 150px;
    height: 150px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .system-header {
    padding: 1rem 1.5rem;
  }

  .system-content {
    padding: 1.5rem;
  }

  .system-images {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
</style>
