<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Link, 
  School, 
  OfficeBuilding, 
  ArrowLeftBold,
  ArrowRightBold
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import ImageViewer from '../../../components/ImageViewer.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/ai-sz-agent')
}

// 标签页切换相关
const activeTab = ref('promoter')

// 轮播图相关
interface SlideState {
  [key: string]: number;
}

const activeSlides = ref<SlideState>({
  '政府与公共部门': 0,
  '文旅行业': 0
})

// 自动轮播定时器
let autoSlideTimers: { [key: string]: number } = {}

// 启动自动轮播
const startAutoSlide = () => {
  // 清除之前的定时器
  stopAutoSlide()
  
  // 为每个类别创建定时器
  agentCases.forEach(category => {
    autoSlideTimers[category.title] = window.setInterval(() => {
      nextSlide(category.title)
    }, 5000)
  })
  
  // 为推介官案例创建定时器
  autoSlideTimers['promoter'] = window.setInterval(() => {
    // 这里可以添加推介官案例的自动轮播逻辑，如果需要
  }, 5000)
}

// 停止自动轮播
const stopAutoSlide = () => {
  Object.values(autoSlideTimers).forEach(timer => {
    clearInterval(timer)
  })
  autoSlideTimers = {}
}

// 切换轮播图
const prevSlide = (categoryTitle: string) => {
  const category = agentCases.find(cat => cat.title === categoryTitle)
  if (!category) return
  
  const currentIndex = activeSlides.value[categoryTitle]
  activeSlides.value[categoryTitle] = (currentIndex - 1 + category.images.length) % category.images.length
}

const nextSlide = (categoryTitle: string) => {
  const category = agentCases.find(cat => cat.title === categoryTitle)
  if (!category) return
  
  const currentIndex = activeSlides.value[categoryTitle]
  activeSlides.value[categoryTitle] = (currentIndex + 1) % category.images.length
}

// 图片查看器相关
const imageViewerVisible = ref(false)
const currentImage = ref('')

// 打开图片查看器
const openImageViewer = (imageUrl: string) => {
  currentImage.value = imageUrl
  imageViewerVisible.value = true
}

// 打开链接
const openLink = (url: string) => {
  window.open(url, '_blank')
}

// 生命周期钩子
onMounted(() => {
  startAutoSlide()
})

onBeforeUnmount(() => {
  stopAutoSlide()
})

// 案例数据
const promoterCases = {
  government: [
    {
      id: 1,
      name: '上饶市数字技术应用协会',
      link: 'https://www.sdtaa.com/contact',
      image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SDTAA-FM.jpeg',
      logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SDTAA-Logo.jpg'
    },
    {
      id: 5,
      name: '广丰市场监督管理局(知识产权融资)',
      link: 'https://zl.sdtaa.com/card/gfq-scjgj',
      image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/GFSCJDGLJ.png',
      logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/GFSCJDGLJ.png'
    },
    {
      id: 4,
      name: '上饶传媒集团',
      link: 'https://www.sdtaa.com/external-promotion/srcm',
      image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SRCMJT.png',
      logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SRCMJT.png'
    }
  ],
  enterprise: [
    {
      id: 2,
      name: '师大儿保托育中心',
      link: 'https://zl.sdtaa.com/card/shida-erbao-tuoyu',
      image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SDEBTYZX.png',
      logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SDEBTYZX-Logo.png'
    },
    {
      id: 3,
      name: '后日资本',
      link: 'https://zl.sdtaa.com/card/houri-capital',
      image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg',
      logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg'
    }
  ]
}

const agentCases = [
  {
    title: '政府与公共部门',
    needs: '您可能需要的：更高效的政策宣传与解读、7x24小时在线的政务咨询、更亲民的城市形象推广、更智能的文旅导览服务。',
    solutions: 'AI数智代言人能帮您：打造永不缺席的政策宣传员、智能政务助手，提升公共服务效率与市民满意度，创新互动体验。',
    images: [
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhengWu-1.png',
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhengWu-2.png',
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhengWu-3.png',
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhengWu-4.png'
    ]
  },
  {
    title: '文旅行业',
    needs: '您可能需要的：更具吸引力的景区/酒店推广、7x24小时的智能导游/客服、更个性化的产品推荐与预订服务。',
    solutions: 'AI数智代言人能帮您：塑造独特的品牌IP，提供全天候智能导览与咨询，优化游客/顾客体验，提升服务坪效与复购率。',
    images: [
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/WenHua-1.png',
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/WenHua-2.png',
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/WenHua-3.png',
      'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/LvYou-1.png'
    ]
  }
]
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>案例中心</h1>
    </div>

    <div class="content">
      <div class="case-center-container">
        <!-- 导航标签 -->
        <div class="tabs-container">
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'promoter' }" 
            @click="activeTab = 'promoter'"
          >
            AI数智推介官
          </div>
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'agent' }" 
            @click="activeTab = 'agent'"
          >
            AI数智代言人
          </div>
        </div>

        <!-- AI数智推介官案例 -->
        <div v-if="activeTab === 'promoter'" class="promoter-cases">
          <!-- 政务案例 -->
          <div class="case-section">
            <div class="section-header">
              <el-icon><OfficeBuilding /></el-icon>
              <h2>政务案例</h2>
            </div>
            <div class="case-grid">
              <div v-for="(item, index) in promoterCases.government" :key="index" class="case-card" @click="openLink(item.link)">
                <div class="case-image">
                  <img :src="item.image" :alt="item.name">
                </div>
                <div class="case-info">
                  <div class="case-logo">
                    <img :src="item.logo" :alt="`${item.name} logo`">
                  </div>
                  <div class="case-title">{{ item.name }}</div>
                </div>
                <div class="case-link">
                  <el-icon><Link /></el-icon>
                  查看详情
                </div>
              </div>
            </div>
          </div>

          <!-- 企业案例 -->
          <div class="case-section">
            <div class="section-header">
              <el-icon><School /></el-icon>
              <h2>企业案例</h2>
            </div>
            <div class="case-grid">
              <div v-for="(item, index) in promoterCases.enterprise" :key="index" class="case-card" @click="openLink(item.link)">
                <div class="case-image">
                  <img :src="item.image" :alt="item.name">
                </div>
                <div class="case-info">
                  <div class="case-logo">
                    <img :src="item.logo" :alt="`${item.name} logo`">
                  </div>
                  <div class="case-title">{{ item.name }}</div>
                </div>
                <div class="case-link">
                  <el-icon><Link /></el-icon>
                  查看详情
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI数智代言人案例 -->
        <div v-if="activeTab === 'agent'" class="agent-cases">
          <div v-for="(category, index) in agentCases" :key="index" class="agent-category">
            <h2 class="category-title">{{ category.title }}</h2>
            
            <div class="category-content">
              <div class="category-text">
                <p class="category-needs">{{ category.needs }}</p>
                <p class="category-solutions">{{ category.solutions }}</p>
              </div>
              
              <!-- 移动端显示轮播图 -->
              <div class="carousel-container mobile-carousel">
                <div class="carousel-wrapper">
                  <div class="carousel-slide" 
                       :style="{ transform: `translateX(-${activeSlides[category.title] * 100}%)` }">
                    <div v-for="(image, imgIndex) in category.images" :key="imgIndex" 
                         class="carousel-item" @click="openImageViewer(image)">
                      <img :src="image" :alt="`${category.title} 案例图${imgIndex + 1}`">
                    </div>
                  </div>
                  <div class="carousel-controls">
                    <div class="carousel-arrow carousel-arrow-left" 
                         @click="prevSlide(category.title)">
                      <el-icon><ArrowLeftBold /></el-icon>
                    </div>
                    <div class="carousel-indicators">
                      <span v-for="(_, imgIndex) in category.images" 
                            :key="imgIndex"
                            class="indicator" 
                            :class="{ active: activeSlides[category.title] === imgIndex }"
                            @click="activeSlides[category.title] = imgIndex"></span>
                    </div>
                    <div class="carousel-arrow carousel-arrow-right" 
                         @click="nextSlide(category.title)">
                      <el-icon><ArrowRightBold /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 桌面端显示轮播图 -->
              <div class="carousel-container desktop-carousel">
                <div class="carousel-wrapper">
                  <div class="carousel-slide" 
                       :style="{ transform: `translateX(-${activeSlides[category.title] * 100}%)` }">
                    <div v-for="(image, imgIndex) in category.images" :key="imgIndex" 
                         class="carousel-item" @click="openImageViewer(image)">
                      <img :src="image" :alt="`${category.title} 案例图${imgIndex + 1}`">
                    </div>
                  </div>
                  <div class="carousel-controls">
                    <div class="carousel-arrow carousel-arrow-left" 
                         @click="prevSlide(category.title)">
                      <el-icon><ArrowLeftBold /></el-icon>
                    </div>
                    <div class="carousel-indicators">
                      <span v-for="(_, imgIndex) in category.images" 
                            :key="imgIndex"
                            class="indicator" 
                            :class="{ active: activeSlides[category.title] === imgIndex }"
                            @click="activeSlides[category.title] = imgIndex"></span>
                    </div>
                    <div class="carousel-arrow carousel-arrow-right" 
                         @click="nextSlide(category.title)">
                      <el-icon><ArrowRightBold /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <TabBar />
    
    <!-- 图片查看器组件 -->
    <ImageViewer v-model:visible="imageViewerVisible" :image-url="currentImage" />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #409EFF, #64b5f6);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem; /* 为底部标签栏留出空间 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.case-center-container {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  padding: 0;
}

/* 标签页样式 */
.tabs-container {
  display: flex;
  border-bottom: 1px solid #edf2f7;
  background-color: #f8fafc;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 1rem;
  cursor: pointer;
  font-weight: 500;
  color: #555;
  transition: all 0.3s;
  position: relative;
}

.tab-item.active {
  color: #409EFF;
  background-color: white;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #64b5f6);
}

/* 推介官案例样式 */
.promoter-cases, .agent-cases {
  padding: 1.5rem;
}

.case-section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  color: #409EFF;
}

.section-header .el-icon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.section-header h2 {
  font-size: 1.2rem;
  margin: 0;
  font-weight: 500;
}

.case-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
}

.case-card {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid #edf2f7;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer; /* 添加指针样式，提示整个卡片可点击 */
}

.case-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.15);
}

.case-image {
  height: 180px;
  overflow: hidden;
}

.case-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.case-card:hover .case-image img {
  transform: scale(1.05);
}

.case-info {
  display: flex;
  align-items: center;
  padding: 1rem;
}

.case-logo {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.75rem;
  border: 1px solid #edf2f7;
  flex-shrink: 0;
}

.case-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.case-title {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.case-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  background-color: #f0f7ff;
  color: #409EFF;
  text-decoration: none;
  font-size: 0.9rem;
  gap: 0.5rem;
  transition: background-color 0.3s;
}

.case-link:hover {
  background-color: #e1eeff;
}

/* 代言人案例样式 */
.agent-category {
  margin-bottom: 2.5rem;
}

.category-title {
  font-size: 1.2rem;
  color: #409EFF;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #edf2f7;
}

.category-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.category-text {
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 0.75rem;
  border: 1px solid #edf2f7;
}

.category-needs {
  margin-top: 0;
  color: #555;
  font-size: 0.9rem;
}

.category-solutions {
  margin-bottom: 0;
  color: #409EFF;
  font-weight: 500;
  font-size: 0.9rem;
}

/* 轮播图通用样式 */
.carousel-container {
  width: 100%;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 0.75rem;
  border: 1px solid #edf2f7;
  aspect-ratio: 16/9;
}

.carousel-slide {
  display: flex;
  transition: transform 0.5s ease;
  height: 100%;
}

.carousel-item {
  flex: 0 0 100%;
  height: 100%;
  cursor: pointer; /* 添加指针样式，提示可点击 */
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-controls {
  position: absolute;
  bottom: 1rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.carousel-indicators {
  display: flex;
  gap: 0.5rem;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s;
}

.indicator.active {
  background-color: #fff;
}

.carousel-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  cursor: pointer;
  transition: background-color 0.3s;
}

.carousel-arrow:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
.mobile-carousel {
  display: block;
}

.desktop-carousel {
  display: none;
}

@media (min-width: 768px) {
  .case-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .category-content {
    flex-direction: row;
  }
  
  .category-text {
    flex: 1;
  }
}

@media (min-width: 992px) {
  .case-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .mobile-carousel {
    display: none;
  }
  
  .desktop-carousel {
    display: block;
    flex: 2;
  }
}
</style> 