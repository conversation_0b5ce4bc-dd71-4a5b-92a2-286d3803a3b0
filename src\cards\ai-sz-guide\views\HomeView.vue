<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'
import DigitalHuman from '../components/DigitalHuman.vue'
import NavigationButtons from '../components/NavigationButtons.vue'

const pcBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZLSZR-PC.png'

// 添加打字机效果
const welcomeText = ref('')
const fullText = '在激烈的区域招商竞争中，是否感觉传统PPT和宣传册难以脱颖而出？我们用AI为您打造一个“AI数智推介官”，它能7x24小时展示区域优势、创新招商手段，用智能互动提升潜在投资者的洽谈转化率，为您的招商工作强势赋能。'
const typingSpeed = 50
let currentIndex = 0
let typingTimer: number | null = null

const typeText = () => {
  if (currentIndex < fullText.length) {
    welcomeText.value += fullText.charAt(currentIndex)
    currentIndex++
    typingTimer = setTimeout(typeText, typingSpeed) as unknown as number
  }
}

const chatWithAI = () => {
  // 这里可以替换为实际的AI对话链接
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=9ho7aq84tu1pq7vhymum9fng', '_blank')
}

onMounted(() => {
  setTimeout(() => {
    typeText()
  }, 500)
})

onBeforeRouteLeave(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<template>
  <div class="home-container">
    <div class="digital-human-container">
      <div class="digital-human-wrapper">
        <DigitalHuman />
      </div>
      <div class="pc-background" :style="{ backgroundImage: `url(${pcBackgroundImage})` }"></div>
    </div>

    <div class="bottom-section">
      <div class="content-wrapper">
        <div class="ai-content-container">
          <!-- AI助手卡片 -->
          <div class="ai-card">
            <div class="ai-content">
              <h2>智小链 <span class="badge">AI推介官</span></h2>
              <div class="typing-container">
                <p class="welcome-text">{{ welcomeText }}<span class="cursor" v-if="welcomeText.length < fullText.length">|</span></p>
              </div>
              <div class="action-container">
                <el-button type="primary" class="chat-btn" @click="chatWithAI">
                  立即对话智小链
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <div class="navigation-container">
          <NavigationButtons />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.digital-human-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.digital-human-wrapper {
  width: 100%;
  height: 100%;
}

.pc-background {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.85) 15%, rgba(255, 255, 255, 0.95));
  z-index: 2;
  backdrop-filter: blur(5px);
  padding-bottom: 1rem; /* 移除底部标签栏空间 */
  height: 45%; /* 保持手机端高度 */
}

.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-top: 1rem;
}

/* AI内容卡片样式 */
.ai-content-container {
  padding: 1rem;
  display: flex;
  justify-content: center;
}

.ai-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  max-width: 400px;
  width: 100%;
}

.ai-content {
  padding: 1.5rem;
}

.ai-content h2 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #4a90e2;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.badge {
  background: linear-gradient(135deg, #4a90e2, #64b5f6);
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  display: inline-block;
}

.typing-container {
  margin-bottom: 1.5rem;
  min-height: 3rem;
}

.welcome-text {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #333;
  margin: 0;
}

.cursor {
  animation: blink 1s infinite;
  color: #4a90e2;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.action-container {
  display: flex;
  justify-content: center;
}

.chat-btn {
  background: linear-gradient(135deg, #4a90e2, #64b5f6);
  border: none;
  border-radius: 2rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

.navigation-container {
  padding: 0rem 1rem 1.5rem 1rem;
}

@media (min-width: 768px) {
  .bottom-section {
    height: 50%; /* 减小桌面端高度 */
  }

  .content-wrapper {
    justify-content: center;
    padding-bottom: 2rem; /* 底部留出空间 */
    padding-top: 1rem; /* 减少顶部空间，使内容往下移 */
  }

  .digital-human-container {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }

  .digital-human-wrapper {
    width: auto;
    height: 100%;
    min-height: 100vh;
    max-width: none;
    display: flex;
    justify-content: center;
  }

  .digital-human-wrapper :deep(img),
  .digital-human-wrapper :deep(video) {
    height: 100%;
    min-height: 100vh;
    width: auto;
    object-fit: cover;
    object-position: center;
  }

  .pc-background {
    display: block;
  }

  /* 桌面端AI卡片样式 */
  .ai-card {
    max-width: 700px;
  }

  .ai-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }

  .ai-content h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .welcome-text {
    font-size: 1rem;
  }

  .ai-content-container {
    padding: 0 1.5rem;
    margin-bottom: 1rem;
  }

  .navigation-container {
    padding: 0 1.5rem 0 1.5rem;
  }
}

@media (min-width: 1200px) {
  .bottom-section {
    height: 55%; /* 减小大屏幕设备高度 */
  }

  .content-wrapper {
    padding-bottom: 3rem; /* 大屏幕底部留出更多空间 */
    padding-top: 2rem; /* 减少大屏幕顶部空间 */
  }

  .ai-card {
    max-width: 800px;
    max-height: 320px;
  }

  .ai-content {
    padding: 2.5rem;
  }

  .ai-content h2 {
    font-size: 1.75rem;
  }

  .welcome-text {
    font-size: 1.1rem;
  }

  .ai-content-container {
    margin-bottom: 1.5rem;
  }

  .navigation-container .navigation-grid {
    max-width: 600px; /* 大屏幕增加导航按钮的最大宽度 */
  }
}
</style> 