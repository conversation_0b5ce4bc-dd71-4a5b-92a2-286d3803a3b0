# 直接使用内置的 dict 类型
Args = dict
Output = dict

async def main(args: Args) -> Output:
    # ✅ 验证输入参数
    if not args or not isinstance(args, dict):
        return {"error": "Invalid input: args is null or not a dictionary"}

    params = args.get('params', {})
    if not params:
        # 如果没有 params 层级，直接使用 args
        params = args

    # ✅ 根据实际字段名提取数据
    title = params.get('title', '')
    desc = params.get('desc', '')
    nickname = params.get('nickname', '')
    videoUrl = params.get('video', '') or ''

    # ✅ 使用正确的字段名并安全转换为数字
    liked_count = params.get('liked_count', '0')
    try:
        likedCount = int(liked_count) if liked_count else 0
    except (ValueError, TypeError):
        likedCount = 0

    collected_count = params.get('collected_count', '0')
    try:
        collectedCount = int(collected_count) if collected_count else 0
    except (ValueError, TypeError):
        collectedCount = 0

    # ✅ 处理图片列表
    imageList = params.get('image_list', [])
    if not isinstance(imageList, list):
        imageList = []

    # ✅ 构建笔记链接
    note_id = params.get('note_id', '')
    url = f"https://www.xiaohongshu.com/explore/{note_id}" if note_id else ''

    # ✅ 构建记录 - 只包含您需要的8个字段
    records = [{"fields": {
        "title": title,
        "desc": desc,
        "nickname": nickname,
        "likeCount": likedCount,
        "videoUrl": videoUrl,
        "imageList": imageList,
        "collectedCount": collectedCount,
        "url": url,
    }}]

    # 构建输出对象
    ret = {
        "records": records
    }
    return ret
