<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { model: 'FKX3-12', airflow: '7200', units: '1', filterCount: '12', area: '264', dimensions: '1021×2275×4513' },
  { model: 'FKX3-24', airflow: '14400', units: '2', filterCount: '24', area: '528', dimensions: '2042×2275×4513' },
  { model: 'FKX3-36', airflow: '21600', units: '3', filterCount: '36', area: '792', dimensions: '3063×2275×4513' },
  { model: 'FKX3-48', airflow: '28800', units: '4', filterCount: '48', area: '1056', dimensions: '4084×2275×4513' },
  { model: 'FKX3-60', airflow: '36000', units: '5', filterCount: '60', area: '1320', dimensions: '5105×2275×4513' },
  { model: 'FKX3-72', airflow: '43200', units: '6', filterCount: '72', area: '1584', dimensions: '6126×2275×4513' },
  { model: 'FKX4-16', airflow: '9600', units: '1', filterCount: '16', area: '352', dimensions: '1021×2275×4996' },
  { model: 'FKX4-32', airflow: '19200', units: '2', filterCount: '32', area: '704', dimensions: '2042×2275×4996' },
  { model: 'FKX4-48', airflow: '28800', units: '3', filterCount: '48', area: '1056', dimensions: '3063×2275×4996' },
  { model: 'FKX4-64', airflow: '38400', units: '4', filterCount: '64', area: '1408', dimensions: '4084×2275×4996' },
  { model: 'FKX4-80', airflow: '48000', units: '5', filterCount: '80', area: '1760', dimensions: '5105×2275×4996' },
  { model: 'FKX4-90', airflow: '54000', units: '6', filterCount: '90', area: '1980', dimensions: '6126×2275×4996' }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '设计灵活',
    description: '多种组合布置，适用于工厂局部或整个工厂的小型粉尘治理',
    icon: '🔧'
  },
  {
    title: '结构紧凑',
    description: '在处理相同风量的情况下，占地空间仅相当于滤袋除尘器的1/3，节省工厂宝贵面积',
    icon: '📦'
  },
  {
    title: '吸尘罩灵活',
    description: '结构灵活，灵活多样，可设置移动式、悬臂式、固定式等，有效地局部控制',
    icon: '🌪️'
  },
  {
    title: '高效过滤',
    description: '褶式滤筒的过滤面积可比传统滤袋高300%，安装简便。进口纳米覆膜滤材表面过滤性能好，使除尘效率达到99.99%以上',
    icon: '✨'
  },
  {
    title: '维护简便',
    description: '抽屉式滤筒设计，维修和更换滤筒更简便、更快捷、更安全，大大缩短了停机时间',
    icon: '🔄'
  },
  {
    title: '智能控制',
    description: '配备智能控制系统，可根据工况自动调节清灰频率和风量，实现节能运行',
    icon: '🧠'
  }
])

// 应用场景
const applications = reactive([
  {
    title: '焊接作业',
    description: '各种焊接工艺产生的焊接烟尘处理',
    icon: '🔥'
  },
  {
    title: '切割作业',
    description: '等离子切割、激光切割等烟尘净化',
    icon: '⚡'
  },
  {
    title: '打磨抛光',
    description: '金属表面打磨抛光粉尘收集',
    icon: '⚙️'
  },
  {
    title: '机械加工',
    description: '机械加工过程中的油雾粉尘处理',
    icon: '🏭'
  }
])

// 技术优势
const technicalAdvantages = reactive([
  {
    title: '模块化设计',
    description: '采用模块化设计，可根据需要灵活组合，满足不同规模的除尘需求。',
    icon: '🧩'
  },
  {
    title: '高效节能',
    description: '先进的风机技术和智能控制系统，在保证除尘效果的同时实现节能运行。',
    icon: '⚡'
  },
  {
    title: '环保达标',
    description: '排放浓度远低于国家标准，满足各种环保要求，为企业提供合规保障。',
    icon: '🌱'
  },
  {
    title: '运行稳定',
    description: '设备运行稳定可靠，故障率低，维护成本低，使用寿命长。',
    icon: '🛡️'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>集成式焊接烟尘除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/3bf77dc0-8acc-4ba7-aa12-6b7e869c1cfd.jpg" alt="一体化焊烟除尘器" />
        </div>
        <div class="hero-content">
          <h2>集成式焊接烟尘除尘器</h2>
          <p class="product-subtitle">专为焊接烟尘治理设计的一体化除尘解决方案</p>
          <div class="product-intro">
            <p>集成式焊接烟尘除尘器是专门针对焊接、切割、打磨等工艺过程中产生的烟尘而设计的高效除尘设备。</p>
            <p>采用模块化设计和先进的滤筒过滤技术，能够高效收集和过滤各种焊接烟尘，保护操作人员健康，改善工作环境，满足环保要求。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>一体化焊烟除尘器通过吸尘罩收集焊接过程中产生的烟尘，经管道输送至除尘器内部。</p>
          <p>烟尘经过预分离后进入滤筒进行高效过滤，清洁空气经风机排出，收集的粉尘通过脉冲清灰系统定期清理，确保设备持续高效运行。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>风量<br/>(m³/h)</th>
                <th>单元数</th>
                <th>滤筒数量<br/>(个)</th>
                <th>过滤面积<br/>(m²)</th>
                <th>外形尺寸<br/>(L×W×H mm)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.units }}</td>
                <td>{{ spec.filterCount }}</td>
                <td>{{ spec.area }}</td>
                <td class="dimensions-cell">{{ spec.dimensions }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications-grid">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <div class="app-icon">{{ app.icon }}</div>
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          技术优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in technicalAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          工程案例
        </h3>
        <div class="project-cases-grid">
          <div class="case-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/7-zyjc/1.jpg" alt="一体化焊烟除尘器工程案例1" />
          </div>
          <div class="case-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/7-zyjc/2.jpg" alt="一体化焊烟除尘器工程案例2" />
          </div>
          <div class="case-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/7-zyjc/202212071357113.jpg" alt="一体化焊烟除尘器工程案例3" />
          </div>
          <div class="case-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/7-zyjc/202212071357116.jpg" alt="一体化焊烟除尘器工程案例4" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品展示区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 产品特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.feature-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.75rem;
}

.specs-table th {
  background: #f8fafc;
  padding: 0.5rem 0.25rem;
  text-align: center;
  font-weight: 600;
  color: #1e3470;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
  font-size: 0.7rem;
}

.specs-table td {
  padding: 0.5rem 0.25rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  white-space: nowrap;
  font-size: 0.75rem;
}

.specs-table tbody tr:hover {
  background: #f8fafc;
}

.dimensions-cell {
  font-size: 0.65rem;
}

/* 应用场景网格 */
.applications-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 技术优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .specs-table {
    font-size: 0.8rem;
  }

  .specs-table th {
    font-size: 0.75rem;
    padding: 0.75rem 0.5rem;
  }

  .specs-table td {
    font-size: 0.8rem;
    padding: 0.75rem 0.5rem;
  }

  .dimensions-cell {
    font-size: 0.7rem;
  }

  .applications-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .specs-table {
    font-size: 0.85rem;
  }

  .specs-table th {
    font-size: 0.8rem;
  }

  .specs-table td {
    font-size: 0.85rem;
  }

  .dimensions-cell {
    font-size: 0.75rem;
  }

  /* 工程案例网格样式 */
  .project-cases-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .case-image {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
  }

  .case-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }

  .case-image img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain;
  }
}

/* 移动端工程案例样式 */
@media (max-width: 767px) {
  .project-cases-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .case-image {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
  }

  .case-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }

  .case-image img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain;
  }
}
</style>
