<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 性能特点
const productFeatures = reactive([
  {
    title: 'PLC全自动化控制，配套可操作触摸屏',
    description: '采用PLC全自动化控制系统，配套可操作触摸屏，操作简便，节能省力，实现智能化管理。'
  },
  {
    title: '无火焰氧化，安全高效',
    description: '无火焰氧化技术，安全高效，可布置在防爆生产场合，消除安全隐患。'
  },
  {
    title: '蜂窝陶瓷载体贵金属催化剂',
    description: '采用蜂窝陶瓷载体贵金属催化剂，催化活性高，性能稳定，阻力小，使用寿命长。'
  },
  {
    title: '高性能活性炭吸附',
    description: '高性能活性炭吸附，比表面积大，吸脱附性能好，过风阻力小，处理效果优异。'
  },
  {
    title: '配套消防系统，充分保证设施安全',
    description: '根据废气特性，吸附床内可配套消防系统，充分保证设施安全，多重保护措施。'
  },
  {
    title: '净化效率高，保证达标排放',
    description: '净化效率高，确保废气处理达到国家排放标准，实现环保达标排放。'
  },
  {
    title: '净化设施阻力小，降低风机功率及噪音',
    description: '净化设施阻力小，可有效降低风机功率及噪音，节能环保，运行成本低。'
  },
  {
    title: '多重安全预警系统',
    description: '配备非稳态控制、温度预警、停机警报及故障应急处置措施等多重安全预警系统。'
  },
  {
    title: '严格控制有机废气浓度',
    description: '严格控制进入系统中有机废气浓度低于其爆炸极限下限值1/4，确保系统安全运行。'
  }
])

// 处理对象
const treatmentObjects = reactive([
  { name: '苯', description: '苯类有机化合物的高效处理' },
  { name: '甲苯', description: '甲苯废气的催化燃烧处理' },
  { name: '二甲苯', description: '二甲苯有机废气净化' },
  { name: '三苯', description: '三苯类化合物处理' },
  { name: '烃类', description: '各种烃类有机化合物' },
  { name: '醇类', description: '醇类有机溶剂废气' },
  { name: '醚类', description: '醚类化合物废气处理' },
  { name: '酚类', description: '酚类有机化合物净化' },
  { name: '酮类', description: '酮类有机溶剂处理' },
  { name: '脂类', description: '脂类有机化合物处理' }
])

// 应用范围
const applications = reactive([
  {
    title: '化工行业',
    description: '各类有机化学品生产，药物生产及各类树脂生产过程中的废气处理。'
  },
  {
    title: '汽车、摩托车、自行车行业',
    description: '金属件和塑料件的表面涂装废气处理，确保生产环境清洁。'
  },
  {
    title: '机械、船舶、家电、家具、建材行业',
    description: '金属件和塑料件的表面涂装废气处理，满足环保要求。'
  },
  {
    title: '制鞋行业',
    description: '制鞋行业的"三苯"废气处理，保护工人健康和环境安全。'
  },
  {
    title: '电子、漆包线生产',
    description: '电子行业和漆包线生产过程中各类有机废气的处理。'
  },
  {
    title: '食品加工行业',
    description: '食品加工过程中产生的各类有机废气处理。'
  },
  {
    title: '印刷、印制铁罐行业',
    description: '印刷和印制铁罐行业的各类有机溶剂废气处理。'
  },
  {
    title: '沥青、橡胶制品生产',
    description: '沥青和橡胶制品生产过程中各类有机废气处理。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>VOCs催化燃烧系统</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/fc4292dc-89ab-4ea7-95d9-b9f76ce6c091.jpg" alt="VOCs催化燃烧系统" />
        </div>
        <div class="hero-content">
          <h2>VOCs催化燃烧废气处理系统</h2>
          <p class="product-subtitle">利用低温氧化技术的高效有机废气处理系统</p>
          <div class="product-intro">
            <p>催化燃烧废气处理系统是利用低温氧化技术，即在贵金属催化剂作用下，将有机气体加热到分解温度使气体净化达标排放。在高浓度低风量废气环境下使用效果最好。</p>
            <p>催化净化是典型的气固相催化反应，其实质是活性炭参与的深度氧化作用。在催化净化过程中，催化剂的作用是降低活化能，同时催化剂表面具有吸附作用，使反应物分子富集于表面提高了反应速率，加快了反应的进行。</p>
            <p>借助催化剂可使有机废气在较低的起燃温度条件下，发生无焰燃烧，并氧化分解为CO2和H2O，同时放出大量热能，从而达到去除废气中的有害物的方法。</p>
          </div>
        </div>
      </div>

      <!-- 处理对象 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          处理对象
        </h3>
        <div class="treatment-objects">
          <div
            v-for="(obj, index) in treatmentObjects"
            :key="index"
            class="object-card"
          >
            <h4>{{ obj.name }}</h4>
            <p>{{ obj.description }}</p>
          </div>
        </div>
      </div>

      <!-- 性能特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          性能特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 应用范围 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用范围
        </h3>
        <div class="applications">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🔥</div>
            <div class="indicator-content">
              <h4>催化燃烧</h4>
              <div class="indicator-value">低温氧化</div>
              <p>贵金属催化剂</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🌡️</div>
            <div class="indicator-content">
              <h4>起燃温度</h4>
              <div class="indicator-value">较低温度</div>
              <p>节能高效</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔍</div>
            <div class="indicator-content">
              <h4>净化效率</h4>
              <div class="indicator-value">高效净化</div>
              <p>达标排放</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🛡️</div>
            <div class="indicator-content">
              <h4>安全性</h4>
              <div class="indicator-value">无火焰氧化</div>
              <p>防爆场合适用</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🤖</div>
            <div class="indicator-content">
              <h4>控制系统</h4>
              <div class="indicator-value">PLC自动化</div>
              <p>触摸屏操作</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">⚠️</div>
            <div class="indicator-content">
              <h4>安全控制</h4>
              <div class="indicator-value">浓度<1/4LEL</div>
              <p>爆炸极限控制</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 工艺原理及流程 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工艺原理及流程
        </h3>
        <div class="process-flow">
          <div class="process-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>废气收集</h4>
              <p>有机废气通过收集系统进入催化燃烧装置，预处理去除颗粒物和有害杂质。</p>
            </div>
          </div>
          <div class="process-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>活性炭吸附</h4>
              <p>低浓度废气首先通过活性炭吸附浓缩，提高废气浓度，为催化燃烧创造条件。</p>
            </div>
          </div>
          <div class="process-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>催化燃烧</h4>
              <p>浓缩后的废气在贵金属催化剂作用下，在较低温度下发生无焰燃烧反应。</p>
            </div>
          </div>
          <div class="process-step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4>氧化分解</h4>
              <p>有机物质被氧化分解为CO2和H2O，同时放出大量热能，实现废气净化。</p>
            </div>
          </div>
          <div class="process-step">
            <div class="step-number">5</div>
            <div class="step-content">
              <h4>热能回收</h4>
              <p>燃烧产生的热能用于预热进入的废气，降低系统能耗，提高经济性。</p>
            </div>
          </div>
          <div class="process-step">
            <div class="step-number">6</div>
            <div class="step-content">
              <h4>达标排放</h4>
              <p>处理后的洁净气体经过冷却后达标排放，实现环保要求。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          系统优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🔥</div>
            <div class="advantage-content">
              <h4>低温催化燃烧技术</h4>
              <p>采用贵金属催化剂，在较低温度下实现有机废气的完全氧化分解，能耗低，效率高，是传统燃烧技术的理想替代方案。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🛡️</div>
            <div class="advantage-content">
              <h4>无火焰安全燃烧</h4>
              <p>无火焰氧化技术，消除了明火安全隐患，可安全布置在防爆生产场合，为企业提供安全可靠的废气处理解决方案。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🤖</div>
            <div class="advantage-content">
              <h4>智能化自动控制</h4>
              <p>PLC全自动化控制系统，配套触摸屏操作界面，操作简便，可实现远程监控，降低人工成本，提高管理效率。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔍</div>
            <div class="advantage-content">
              <h4>高效催化剂技术</h4>
              <p>蜂窝陶瓷载体贵金属催化剂，催化活性高，性能稳定，阻力小，使用寿命长，确保系统长期稳定运行。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔄</div>
            <div class="advantage-content">
              <h4>热能回收利用</h4>
              <p>燃烧产生的热能可用于预热进入的废气，实现热能回收利用，显著降低系统运行能耗，提高经济效益。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚠️</div>
            <div class="advantage-content">
              <h4>多重安全保障</h4>
              <p>配备多重安全预警系统，包括温度控制、浓度监测、故障报警等，严格控制废气浓度低于爆炸极限，确保系统安全。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases">
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/14-gfvoc/20240130142302.jpg" alt="VOCs催化燃烧系统工程案例1" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/14-gfvoc/20240130142306.jpg" alt="VOCs催化燃烧系统工程案例2" />
          </div>
          
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/14-gfvoc/IMG_20230313_165359.jpg" alt="VOCs催化燃烧系统工程案例4" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/14-gfvoc/IMG_20230303_165446.jpg" alt="VOCs催化燃烧系统工程案例3" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 处理对象 */
.treatment-objects {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.object-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.object-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.object-card h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.object-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.3;
  font-size: 0.75rem;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.3;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 应用范围 */
.applications {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.application-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 工艺流程 */
.process-flow {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
  border-left: 4px solid #3b82f6;
}

.process-step:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.step-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.step-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.step-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 系统优势 */
.advantages {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 工程案例样式 */
.project-cases {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.case-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.case-item img {
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .treatment-objects {
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
  }

  .object-card {
    padding: 1.25rem;
  }

  .object-card h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .object-card p {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }

  .feature-card p {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .process-flow {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .process-step {
    padding: 1.5rem;
  }

  .step-content h4 {
    font-size: 1.1rem;
  }

  .step-content p {
    font-size: 0.9rem;
  }

  .advantages {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .application-card {
    padding: 1.5rem;
  }

  .application-card h4 {
    font-size: 1.1rem;
  }

  .application-card p {
    font-size: 0.9rem;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .process-flow {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
