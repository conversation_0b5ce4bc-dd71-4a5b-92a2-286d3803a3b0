<script setup lang="ts">
import { ref } from 'vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

// 汉氏联合slogan标语
const slogan = ref('细胞承载希望，科技实现梦想')
// 桌面端背景图
const pcBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/szr-pc.jpeg'
// 移动端背景图
const mobileBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/szr.png'
</script>

<template>
  <div class="home-container">
    <!-- 背景图容器 -->
    <div class="background-container">
      <!-- 移动端背景图 -->
      <div class="mobile-background" :style="{ backgroundImage: `url(${mobileBackgroundImage})` }"></div>
      <!-- 桌面端背景图 -->
      <div class="pc-background" :style="{ backgroundImage: `url(${pcBackgroundImage})` }"></div>
    </div>
    
    <!-- 底部内容区域 -->
    <div class="bottom-section">
      <div class="content-wrapper">
        <!-- slogan标语区域 -->
        <div class="slogan-container">
          <div class="slogan-wrapper">
            <h1 class="slogan">{{ slogan }}</h1>
            <div class="tech-line"></div>
          </div>
        </div>
        
        <!-- 导航按钮区域 -->
        <div class="navigation-container">
          <NavigationButtons />
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<style scoped>
/* 主容器样式 */
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

/* 背景图容器 */
.background-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 移动端背景图 */
.mobile-background {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

/* 桌面端背景图 */
.pc-background {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

/* 底部内容区域 */
.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.85) 15%, rgba(255, 255, 255, 0.95));
  z-index: 2;
  backdrop-filter: blur(5px);
  padding-bottom: 4rem; /* 为底部标签栏留出空间 */
  height: 45%; /* 移动端高度 */
}

/* 内容包装器 */
.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-top: 1rem;
}

/* slogan容器 */
.slogan-container {
  padding: 1rem 1rem 0.5rem 1rem;
  display: flex;
  justify-content: center;
}

/* slogan包装器 */
.slogan-wrapper {
  position: relative;
  max-width: 340px;
  padding-bottom: 0.5rem;
}

/* 科技感装饰线 */
.tech-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #0f9da8 30%, #0f9da8 70%, transparent);
}

/* slogan文字样式 */
.slogan {
  font-size: 1.25rem;
  line-height: 1.5;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

/* 导航按钮容器 */
.navigation-container {
  padding: 1rem 1rem 1.5rem 1rem;
}

/* 桌面端响应式设计 */
@media (min-width: 768px) {
  /* 显示桌面端背景，隐藏移动端背景 */
  .mobile-background {
    display: none;
  }
  
  .pc-background {
    display: block;
  }
  
  /* 调整底部区域高度 */
  .bottom-section {
    height: 50%; /* 减小桌面端高度 */
  }
  
  /* 内容居中对齐 */
  .content-wrapper {
    justify-content: center;
    padding-bottom: 2rem; /* 底部留出空间 */
    padding-top: 1rem; /* 减少顶部空间 */
  }
  
  /* slogan样式调整 */
  .slogan-wrapper {
    max-width: 700px; /* 增加PC端slogan的最大宽度 */
    padding-bottom: 0.75rem;
  }

  .slogan {
    font-size: 1.75rem;
    background: linear-gradient(135deg, #0f9da8, #2bc5d2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* 容器间距调整 */
  .slogan-container {
    padding: 0 1.5rem 0.5rem 1.5rem;
    margin-bottom: 1rem; /* 减少与导航按钮的间距 */
  }
  
  .navigation-container {
    padding: 0 1.5rem 0 1.5rem;
  }
}

/* 大屏幕设备优化 */
@media (min-width: 1200px) {
  .bottom-section {
    height: 55%; /* 减小大屏幕设备高度 */
  }
  
  .content-wrapper {
    padding-bottom: 3rem; /* 大屏幕底部留出更多空间 */
  }
  
  .slogan {
    font-size: 2rem;
  }
}
</style>