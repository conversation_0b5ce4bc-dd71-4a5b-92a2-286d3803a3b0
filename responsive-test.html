<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式设计测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .breakpoint-info {
            background: #1693d2;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .grid-demo {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .grid-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }
        
        .stats-demo {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-item {
            background: rgba(22, 147, 210, 0.1);
            border: 2px solid #1693d2;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1693d2;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .image-demo {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        /* 超小屏幕 (≤480px) */
        @media (max-width: 480px) {
            .breakpoint-info::before {
                content: "📱 超小屏幕 (≤480px) - ";
            }
            
            .grid-demo {
                grid-template-columns: 1fr;
            }
            
            .stats-demo {
                grid-template-columns: 1fr 1fr;
            }
            
            .stat-number {
                font-size: 1.5rem;
            }
            
            .test-container {
                padding: 15px;
                margin: 10px;
            }
        }
        
        /* 小屏幕 (481px-767px) */
        @media (min-width: 481px) and (max-width: 767px) {
            .breakpoint-info::before {
                content: "📱 小屏幕 (481px-767px) - ";
            }
            
            .grid-demo {
                grid-template-columns: 1fr;
            }
            
            .stats-demo {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        /* 平板 (768px-1023px) */
        @media (min-width: 768px) and (max-width: 1023px) {
            .breakpoint-info::before {
                content: "📱 平板设备 (768px-1023px) - ";
            }
            
            .grid-demo {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .stats-demo {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        /* 桌面 (1024px-1439px) */
        @media (min-width: 1024px) and (max-width: 1439px) {
            .breakpoint-info::before {
                content: "💻 桌面设备 (1024px-1439px) - ";
            }
            
            .grid-demo {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .stats-demo {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        /* 大屏幕 (≥1440px) */
        @media (min-width: 1440px) {
            .breakpoint-info::before {
                content: "🖥️ 大屏幕 (≥1440px) - ";
            }
            
            .grid-demo {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .stats-demo {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        .instructions {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            color: #0ea5e9;
            margin-top: 0;
        }
        
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="breakpoint-info">
            当前断点检测
        </div>
        
        <h2>网格布局演示</h2>
        <div class="grid-demo">
            <div class="grid-item">功能模块 1</div>
            <div class="grid-item">功能模块 2</div>
            <div class="grid-item">功能模块 3</div>
        </div>
        
        <h2>统计数据演示</h2>
        <div class="stats-demo">
            <div class="stat-item">
                <div class="stat-number">10+</div>
                <div class="stat-label">年行业深耕</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">500+</div>
                <div class="stat-label">家信赖客户</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">3000+</div>
                <div class="stat-label">个成功项目</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">50+</div>
                <div class="stat-label">项软件著作权</div>
            </div>
        </div>
        
        <h2>图片/媒体演示</h2>
        <div class="image-demo">
            响应式图片/视频容器
        </div>
        
        <div class="instructions">
            <h3>📋 响应式测试说明</h3>
            <ul>
                <li><strong>调整浏览器窗口大小</strong>来测试不同断点的效果</li>
                <li><strong>使用开发者工具</strong>模拟不同设备尺寸</li>
                <li><strong>检查布局变化</strong>：网格列数、间距、字体大小等</li>
                <li><strong>验证内容可读性</strong>：确保在所有尺寸下内容都清晰可读</li>
                <li><strong>测试交互元素</strong>：按钮、链接等在移动端是否易于点击</li>
            </ul>
            
            <h4>🎯 主要断点：</h4>
            <ul>
                <li><strong>≤480px</strong>：超小屏幕（小手机）</li>
                <li><strong>481px-767px</strong>：小屏幕（大手机）</li>
                <li><strong>768px-1023px</strong>：平板设备</li>
                <li><strong>1024px-1439px</strong>：桌面设备</li>
                <li><strong>≥1440px</strong>：大屏幕桌面</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 实时显示当前窗口尺寸
        function updateWindowSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const info = document.querySelector('.breakpoint-info');
            info.innerHTML = info.innerHTML.split(' - ')[0] + ` - 当前尺寸: ${width}×${height}px`;
        }
        
        window.addEventListener('resize', updateWindowSize);
        updateWindowSize();
    </script>
</body>
</html>
