<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('YuqingDubanView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>舆情督办系统</h1>
    </div>

    <div class="content">
      <!-- 项目介绍 -->
      <div class="section intro-section">
        <div class="intro-card">
          <div class="intro-visual">
            <div class="intro-image">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yuqingduban.png" alt="舆情督办系统" />
            </div>
          </div>
          <div class="intro-content">
            <div class="intro-title">
              <h3>舆情督办系统</h3>
              <div class="subtitle">提高处置效率、跟踪工作进展</div>
              <div class="title-decoration"></div>
            </div>
            <div class="intro-description">
              <div class="description-item">
                <div class="item-icon">📊</div>
                <p>舆情督办系统是为了提高舆情工作处置效率、实时跟踪舆情工作进程而开发的"舆情任务管理器"。督办系统提供舆情的上报、下发、流程监控操作，并可以对舆情工作情况进行统计，对舆情事件进行处理。还增加了点到系统，实时查看人员在岗情况，确保舆情处理的时效性。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">🎯</div>
                <p>舆情督办系统是在大量的督查督办项目实践中，在汇总需求和工作经验的基础上，开发的一套使用与政府机关、企事业单位工作部署与督察的系统，实现对工作、任务的分解，下派、执行及办理过程的监督，防止工作积压、工作责任不明、工作贻误等问题，减少工作汇报，提高工作的执行效率。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">🔄</div>
                <p>系统通过监测平台或者第三方软件提供的数据，自动更新最新舆情信息，并支持人工录入。以站帖形式下发处置任务，有效打通各部门之间交流不畅的壁垒，实现舆情工作在部门间的无缝对接。直观呈现任意时间内的舆情事件演变及事件处理进度，方便用户进行及时应对。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">📈</div>
                <p>系统可对舆情事件的处理结果进行多角度统计，形成各类统计分析图表，并可对具体舆情工作情况进行即时打分。对于舆情事件、舆情任务，督办系统会根据处理情况、处置阶段进行不同的标记，事件进度一目了然，方便归类查找。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>主要功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">📡</div>
            <h3>信息采集</h3>
            <div class="feature-content">
              <p>系统通过监测平台或者第三方软件提供的数据，自动更新最新舆情信息，并支持人工录入。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📰</div>
            <h3>舆情日报</h3>
            <div class="feature-content">
              <p>系统可对舆情事件的处理结果进行统计，形成当日统计分析图表，并可导出数据进一步分析。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📅</div>
            <h3>排班管理</h3>
            <div class="feature-content">
              <p>系统通过录入排班表到平台进行排班人员管理。可以查看人员到岗情况，方便上班工作交流。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">✅</div>
            <h3>点到系统</h3>
            <div class="feature-content">
              <p>系统通过统计多平台的用户登陆进行人员在岗管理，管理员通过群发点到通知，了解员工在岗情况。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <h3>统计报表管理</h3>
            <div class="feature-content">
              <p>系统可对舆情事件的处理结果进行多角度统计，形成各类统计分析图表，并可导出数据进一步分析。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔐</div>
            <h3>权限分配</h3>
            <div class="feature-content">
              <p>系统通过设置管理员以及下属部门进行权限管理，每个用户都有单独的权限控制，使用灵活方便。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>产品价值</h2>
        </div>
        <div class="value-grid">
          <div class="value-item">
            <div class="value-header">
              <div class="value-icon">🎯</div>
              <h3>工作分解与派发</h3>
            </div>
            <div class="value-content">
              <p>对单位的重点舆论工作、重大事项、重点项目进行任务细分，并下派给各委办单位或个人执行。</p>
            </div>
          </div>
          <div class="value-item">
            <div class="value-header">
              <div class="value-icon">👁️</div>
              <h3>工作督办与进程监督</h3>
            </div>
            <div class="value-content">
              <p>对委派的工作监督执行进度、查阅和批示委办单位或个人执行工作过程中的反馈。</p>
            </div>
          </div>
          <div class="value-item">
            <div class="value-header">
              <div class="value-icon">💬</div>
              <h3>即时反馈办理问题</h3>
            </div>
            <div class="value-content">
              <p>委派单位或个人在执行任务过程中，随时可以提交反馈意见和资料，便于负责人决策。</p>
            </div>
          </div>
          <div class="value-item">
            <div class="value-header">
              <div class="value-icon">📈</div>
              <h3>汇总统计与体现效能</h3>
            </div>
            <div class="value-content">
              <p>按部门、按舆论类型、舆论状态统计工作数量，并以表格、柱状图折线图进行展示。提高统计工作效能。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>系统优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <h3>高效处置</h3>
            <p>通过自动化流程和智能分配，大幅提升舆情处置效率，确保及时响应。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">👀</div>
            <h3>实时监控</h3>
            <p>全程跟踪舆情处理进度，实时掌握工作状态，防止遗漏和延误。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🤝</div>
            <h3>协同办公</h3>
            <p>打通部门壁垒，实现跨部门协同处理，提升整体工作效能。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📊</div>
            <h3>数据分析</h3>
            <p>多维度统计分析，为决策提供数据支撑，持续优化工作流程。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔒</div>
            <h3>权限管控</h3>
            <p>精细化权限管理，确保信息安全，满足不同层级的使用需求。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📱</div>
            <h3>移动办公</h3>
            <p>支持移动端操作，随时随地处理舆情事务，提升工作灵活性。</p>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section application-value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="application-value-content">
          <p>舆情督办系统通过信息化手段，有效解决了传统舆情处理中的痛点问题。系统实现了从舆情发现、任务分配、处理跟踪到结果统计的全流程管理，大大提升了舆情处理的效率和质量。通过实时监控和智能分析，帮助管理者及时掌握舆情动态，科学决策，有效防范舆情风险。系统的协同办公功能打破了部门间的信息壁垒，实现了舆情处理的统一调度和协调配合，为维护良好的舆论环境提供了强有力的技术支撑。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 项目介绍样式 */
.intro-card {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(22, 147, 210, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1693d2 0%, #0d7ab8 100%);
}

.intro-visual {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
}

.intro-image {
  width: 100%;
  max-width: 600px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(22, 147, 210, 0.2);
  position: relative;
}

.intro-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.intro-content {
  text-align: center;
}

.intro-title {
  margin-bottom: 2rem;
}

.intro-title h3 {
  color: #1693d2;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.title-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #1693d2 0%, #0d7ab8 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-description {
  display: grid;
  gap: 1.5rem;
  text-align: left;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #1693d2;
}

.item-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.description-item p {
  margin: 0;
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
}

/* 主要功能样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.feature-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 产品价值样式 */
.value-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.value-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.value-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
}

.value-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.value-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.value-content {
  padding: 2rem;
}

.value-content p {
  color: #333;
  line-height: 1.6;
  margin: 0;
  text-align: justify;
}

/* 系统优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* 应用价值样式 */
.application-value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .intro-description {
    gap: 1rem;
  }

  .description-item {
    padding: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .value-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .value-header {
    padding: 1rem 1.5rem;
  }

  .value-content {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }

  .intro-title h3 {
    font-size: 1.5rem;
  }
}
</style>
