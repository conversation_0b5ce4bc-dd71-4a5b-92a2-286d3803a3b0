:root {
  --primary: #3060b0;
  --primary-light: #4a7aca;
  --primary-dark: #1b4283;
  --secondary: #4d91ff;
  --accent: #1e88e5;
  --background: #f5f7fa;
  --text: #333;
  --text-light: #666;
  --white: #fff;
  --success: #42b983;
  --warning: #e6a23c;
  --danger: #f56c6c;
  --font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  
  font-family: var(--font-family);
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: var(--text);
  background-color: var(--background);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  font-size: 16px;
}

h1 {
  font-size: 2rem;
  line-height: 1.2;
  margin-bottom: 1rem;
  font-weight: 500;
}

h2 {
  font-size: 1.5rem;
  line-height: 1.25;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

p {
  margin: 0 0 1rem;
}

#app {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.btn-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.text-gradient {
  background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}