<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}


// 治理方案
const treatmentSolutions = reactive([
  {
    title: '粉尘治理',
    icon: '🌪️',
    methods: [
      {
        name: '源头控制',
        description: '改进生产工艺，使用低尘低污染的原材料和设备，从源头上减少粉尘的产生。'
      },
      {
        name: '收集处理',
        description: '采用高效除尘设备，如滤筒除尘器、布袋除尘器等，对产生的粉尘进行收集和处理。'
      },
      {
        name: '定期维护',
        description: '对除尘设备进行定期维护和保养，确保其正常运行和高效性能。'
      }
    ]
  },
  {
    title: '废气治理',
    icon: '💨',
    methods: [
      {
        name: '吸收法',
        description: '利用液体吸收剂与废气中的有害成分发生化学反应，将其转化为无害物质。'
      },
      {
        name: '吸附法',
        description: '使用活性炭、分子筛等吸附材料，将废气中的有害物质吸附去除。'
      },
      {
        name: '催化燃烧',
        description: '在催化剂作用下，将有机废气在较低温度下氧化分解为CO2和H2O。'
      }
    ]
  }
])

// 服务优势
const serviceAdvantages = reactive([
  {
    title: '专业团队',
    description: '拥有经验丰富的环保工程师团队，提供专业的技术支持和解决方案。',
    icon: '👥'
  },
  {
    title: '定制方案',
    description: '根据客户具体需求和现场条件，量身定制最适合的治理方案。',
    icon: '🎯'
  },
  {
    title: '全程服务',
    description: '从方案设计、设备安装到后期维护，提供全程一站式服务。',
    icon: '🔧'
  },
  {
    title: '达标保证',
    description: '确保治理效果达到国家环保标准，为客户提供合规保障。',
    icon: '✅'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>粉尘与废气综合治理设计方案</h1>
    </div>

    <div class="content">
      <!-- 服务主图和基本信息 -->
      <div class="service-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/6036fe1c-816f-4b86-a941-c8aa24ac901f.jpg" alt="粉尘与废气综合治理设计方案" />
        </div>
        <div class="hero-content">
          <h2>粉尘与废气综合治理设计方案</h2>
          <p class="service-subtitle">致力于粉尘和废气的综合治理，保护环境，维护生态平衡</p>
          <div class="service-intro">
            <p>随着工业化的快速发展，粉尘和废气的排放问题日益严重，对环境造成了严重污染。为了保护环境，维护生态平衡，福运环保设计方案致力于粉尘和废气的综合治理。</p>
            <p>福运环保方案旨在通过综合治理手段，减少粉尘和废气的排放，提高空气质量，达到国家环保标准。</p>
          </div>
        </div>
      </div>

      <!-- 背景概述 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📋</span>
          背景概述
        </h3>
        <div class="background-content">
          <p>工业生产过程中产生的粉尘和废气是主要的大气污染源，不仅影响环境质量，还对人体健康造成严重威胁。有效的粉尘与废气治理已成为企业可持续发展的重要课题。</p>
          <p>我们的综合治理方案结合了先进的环保技术和丰富的工程经验，为各类工业企业提供量身定制的解决方案，确保达到国家环保标准要求。</p>
        </div>
      </div>

      <!-- 治理方案 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🛠️</span>
          治理方案
        </h3>
        <div class="solutions-grid">
          <div
            v-for="(solution, index) in treatmentSolutions"
            :key="index"
            class="solution-card"
          >
            <div class="solution-header">
              <span class="solution-icon">{{ solution.icon }}</span>
              <h4>{{ solution.title }}</h4>
            </div>
            <div class="methods-list">
              <div
                v-for="(method, methodIndex) in solution.methods"
                :key="methodIndex"
                class="method-item"
              >
                <h5>{{ method.name }}</h5>
                <p>{{ method.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          服务优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in serviceAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases-grid">
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/13-wfkfq/02212071436444.jpg" alt="工程案例1" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/13-wfkfq/02212071436448.jpg" alt="工程案例2" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/13-wfkfq/20221207143644.jpg" alt="工程案例3" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/13-wfkfq/202212071436443.jpg" alt="工程案例4" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 服务展示区域 */
.service-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.service-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.service-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.service-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 背景内容 */
.background-content p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.background-content p:last-child {
  margin-bottom: 0;
}

/* 治理方案网格 */
.solutions-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.solution-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border-left: 4px solid #3b82f6;
}

.solution-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.solution-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

.solution-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
}

.methods-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.method-item {
  background: white;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.method-item h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e3470;
}

.method-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.8rem;
}

/* 服务优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 联系我们部分 */
.contact-section {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #3b82f6;
}

.contact-content {
  text-align: center;
}

.contact-text {
  margin: 0 0 1.5rem 0;
  color: #4b5563;
  font-size: 1rem;
  line-height: 1.5;
}

.contact-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.contact-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.call-btn {
  background: #3b82f6;
  color: white;
}

.call-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.copy-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.copy-btn:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.phone-number {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

/* 工程案例样式 */
.project-cases-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.case-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.case-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.case-item img {
  width: 100%;
  height: 200px;
  object-fit: contain;
  background-color: #f8fafc;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .service-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .service-subtitle {
    font-size: 1.1rem;
  }

  .service-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .background-content p {
    font-size: 1rem;
  }

  .solutions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .contact-buttons {
    gap: 1.5rem;
  }

  .contact-btn {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}
</style>
