<template>
  <div class="hospital-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>上饶经开汉氏联合医院</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/lianheyiyuan.png" 
          alt="上饶经开汉氏联合医院" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">上饶经开汉氏联合医院</h2>
          <p class="banner-subtitle">细胞科技临床转化，开启再生治疗新篇章</p>
        </div>
      </div>

      <!-- 核心特色模块 -->
      <div class="section core-feature-section">
        <h2 class="section-title"><el-icon><Star /></el-icon> 核心特色</h2>
        <div class="feature-cards">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><SetUp /></el-icon>
            </div>
            <div class="feature-content">
              <h3>技术驱动</h3>
              <p>以再生医学为核心，将前沿的细胞治疗技术应用于临床实践，为疑难病症提供全新可能。</p>
            </div>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="feature-content">
              <h3>专家团队</h3>
              <p>汇聚行业权威专家作为学科带头人，并建立起技术精湛、经验丰富的医疗骨干队伍。</p>
            </div>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Service /></el-icon>
            </div>
            <div class="feature-content">
              <h3>人文关怀</h3>
              <p>我们致力于提供一个宽松、舒适的就医环境，简化流程，注重医患沟通，传递有温度的医疗。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要诊疗范围模块 -->
      <div class="section treatment-section">
        <h2 class="section-title"><el-icon><Operation /></el-icon> 主要诊疗范围</h2>
        <p class="treatment-subtitle">常规医疗与特色技术并行</p>
        
        <!-- 诊疗项目网格 -->
        <div class="treatment-grid">
          <div class="treatment-item">
            <div class="treatment-icon">
              <el-icon><FirstAidKit /></el-icon>
            </div>
            <div class="treatment-info">
              <h3>内科</h3>
              <p>心血管、呼吸、消化等</p>
            </div>
          </div>
          
          <div class="treatment-item">
            <div class="treatment-icon">
              <el-icon><Scissor /></el-icon>
            </div>
            <div class="treatment-info">
              <h3>外科</h3>
              <p>普外、骨科、神经外科等</p>
            </div>
          </div>
          
          <div class="treatment-item">
            <div class="treatment-icon">
              <el-icon><Female /></el-icon>
            </div>
            <div class="treatment-info">
              <h3>妇产科</h3>
              <p>妇科、产科、计划生育</p>
            </div>
          </div>
          
          <div class="treatment-item">
            <div class="treatment-icon">
              <el-icon><Position /></el-icon>
            </div>
            <div class="treatment-info">
              <h3>康复医学科</h3>
              <p>功能康复、理疗、康复训练</p>
            </div>
          </div>
        </div>
        
        <!-- 特色诊疗单独展示 -->
        <div class="special-treatment">
          <div class="special-icon">
            <el-icon><Opportunity /></el-icon>
          </div>
          <div class="special-content">
            <h3>🌟 特色诊疗</h3>
            <p>细胞技术临床应用 - 针对特定适应症的再生医学干预</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
// 导入所需的组件和图标
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Star, 
  SetUp, 
  UserFilled,
  Service,
  Operation,
  Female,
  Position,
  // 新增图标
  FirstAidKit,
  Scissor,
  Opportunity
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.hospital-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px; /* 为固定头部留出空间 */
  padding-bottom: 80px; /* 为底部导航栏留出空间 */
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.back-button:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 8px; /* 进一步减少内边距 */
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px; /* 减小圆角 */
  overflow: hidden;
  margin-bottom: 16px; /* 进一步减少下边距 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  transition: all 0.5s ease; /* 添加过渡效果 */
}

.banner-section:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease; /* 添加图片过渡效果 */
}

.banner-section:hover .banner-image {
  transform: scale(1.03); /* 悬停时图片轻微放大 */
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px; /* 进一步减少内边距 */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%); /* 增强渐变效果 */
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.3; /* 添加行高优化 */
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5; /* 添加行高优化 */
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px; /* 减小圆角 */
  padding: 12px; /* 进一步减少内边距 */
  margin-bottom: 12px; /* 进一步减少下边距 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 20px; /* 优化字体大小 */
  color: #0f9da8;
  margin: 0 0 12px 0; /* 进一步减少下边距 */
  gap: 8px;
  line-height: 1.4; /* 添加行高优化 */
}

/* 核心特色卡片 */
.feature-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px; /* 减少间距 */
}

.feature-card {
  background: #f8fafc;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px; /* 减少内边距 */
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 添加阴影 */
}

.feature-card:hover {
  transform: translateY(-3px); /* 增强悬停效果 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.feature-icon {
  width: 50px; /* 减小图标尺寸 */
  height: 50px; /* 减小图标尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px; /* 减少下边距 */
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.feature-card:hover .feature-icon {
  transform: scale(1.1); /* 悬停时图标放大 */
}

.feature-icon .el-icon {
  font-size: 28px; /* 增大图标 */
  color: white;
}

.feature-card h3 {
  font-size: 20px; /* 增大标题 */
  margin: 0 0 14px 0; /* 增加下边距 */
  color: #333;
  font-weight: 600; /* 加粗标题 */
  line-height: 1.3; /* 优化标题行高 */
}

.feature-card p {
  font-size: 16px; /* 进一步增大描述文字 */
  color: #666;
  line-height: 1.8; /* 进一步优化行高 */
  margin: 0;
}

/* 主要诊疗范围 */
.treatment-section {
  text-align: center;
}

.treatment-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

/* 诊疗项目网格布局 */
.treatment-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 20px;
}

.treatment-item {
  background: #f8fafc;
  border-radius: 12px;
  padding: 15px;
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 12px;
}

.treatment-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.treatment-icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.treatment-item:hover .treatment-icon {
  transform: scale(1.05);
}

.treatment-icon .el-icon {
  font-size: 22px;
  color: white;
}

.treatment-info {
  flex: 1;
}

.treatment-info h3 {
  font-size: 15px;
  margin: 0 0 4px 0;
  color: #333;
  font-weight: 600;
}

.treatment-info p {
  font-size: 12px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* 特色诊疗单独样式 */
.special-treatment {
  margin-top: 15px;
  background: linear-gradient(135deg, #f0f9fa, #e6f7f8);
  border: 1px solid #d0eef0;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.special-treatment:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(15, 157, 168, 0.15);
}

.special-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(15, 157, 168, 0.3);
  flex-shrink: 0;
}

.special-icon .el-icon {
  font-size: 24px;
  color: white;
}

.special-content {
  flex: 1;
}

.special-content h3 {
  font-size: 16px;
  margin: 0 0 6px 0;
  color: #0f9da8;
  font-weight: 600;
}

.special-content p {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .feature-cards {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .treatment-list {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .special-item {
    grid-column: span 3;
  }
  
  .banner-title {
    font-size: 32px; /* 增大标题 */
  }
  
  .banner-subtitle {
    font-size: 20px; /* 增大副标题 */
  }
}

@media (min-width: 992px) {
  .treatment-list {
    grid-template-columns: repeat(5, 1fr);
  }

  .special-item {
    grid-column: span 5;
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  /* 页面容器优化 */
  .hospital-container {
    padding-top: 50px; /* 减少顶部间距 */
    padding-bottom: 70px; /* 减少底部间距 */
  }

  /* 内容区域优化 */
  .content {
    padding: 8px;
  }

  /* 横幅优化 */
  .banner-section {
    margin-bottom: 16px;
  }

  .banner-image {
    height: 200px;
    object-fit: cover;
  }

  .banner-overlay {
    padding: 16px;
  }

  .banner-title {
    font-size: 20px;
    margin-bottom: 6px;
  }

  .banner-subtitle {
    font-size: 14px;
  }

  /* 通用部分优化 */
  .section {
    padding: 12px;
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 12px;
  }

  /* 核心特色卡片优化 - 改为横向紧凑布局 */
  .feature-cards {
    display: flex;
    flex-direction: column;
    gap: 10px; /* 减少间距 */
  }

  .feature-card {
    display: flex;
    align-items: center;
    padding: 16px; /* 增加触摸友好的内边距 */
    border-radius: 10px;
    min-height: 60px; /* 确保最小触摸区域 */
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .feature-card:active {
    transform: translateY(0);
  }

  .feature-icon {
    width: 40px; /* 减小图标尺寸 */
    height: 40px;
    margin-bottom: 0; /* 移除下边距 */
    margin-right: 12px; /* 添加右边距 */
    flex-shrink: 0;
  }

  .feature-icon .el-icon {
    font-size: 20px; /* 减小图标字体 */
  }

  .feature-content {
    flex: 1;
  }

  .feature-card h3 {
    font-size: 15px; /* 减小标题字体 */
    margin-bottom: 4px; /* 减少下边距 */
  }

  .feature-card p {
    font-size: 12px; /* 减小描述字体 */
    line-height: 1.4; /* 减少行高 */
  }

  /* 诊疗范围优化 */
  .treatment-subtitle {
    font-size: 14px;
    margin-bottom: 15px;
  }

  /* 移动端网格布局调整为单列 */
  .treatment-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    margin-top: 15px;
  }

  .treatment-item {
    padding: 12px;
    gap: 10px;
  }

  .treatment-icon {
    width: 35px;
    height: 35px;
  }

  .treatment-icon .el-icon {
    font-size: 18px;
  }

  .treatment-info h3 {
    font-size: 14px;
    margin-bottom: 2px;
  }

  .treatment-info p {
    font-size: 11px;
  }

  /* 特色诊疗移动端样式 */
  .special-treatment {
    margin-top: 12px;
    padding: 12px;
    gap: 10px;
  }

  .special-icon {
    width: 40px;
    height: 40px;
  }

  .special-icon .el-icon {
    font-size: 20px;
  }

  .special-content h3 {
    font-size: 15px;
    margin-bottom: 4px;
  }

  .special-content p {
    font-size: 12px;
  }
}
@media (max-width: 768px) {
  .back-button {
    display: none;
  }
}
</style>