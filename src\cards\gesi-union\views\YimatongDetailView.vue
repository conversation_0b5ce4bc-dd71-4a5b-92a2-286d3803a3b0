<template>
  <div class="page">
    <!-- 顶部导航 -->
    <div class="header">
      <button @click="goBack" class="back-btn">‹ 返回</button>
      <h1>一码通上饶</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 产品头部展示 -->
      <div class="hero-section">
        <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/yimatongshangrao.jpg" alt="一码通上饶" class="hero-image" loading="lazy" />
      </div>

      <!-- 项目简介 -->
      <div class="section">
        <div class="intro-card">
          <div class="intro-header">
            <h2>项目简介</h2>
          </div>
          <div class="intro-content">
            <p class="intro-text">"一码通上饶"是协会倾力打造的数字化商业生态平台。通过整合本地文体、酒店、餐饮、零售、生活服务等各行业优质商家资源，为市民提供便捷、优惠的消费体验，同时帮助传统商家实现数字化转型，打通线上引流渠道，开拓新的增长点。</p>

            <div class="platform-features">
              <div class="feature-grid">
                <div class="feature-item">
                  <div class="feature-name">餐饮美食</div>
                </div>
                <div class="feature-item">
                  <div class="feature-name">酒店住宿</div>
                </div>
                <div class="feature-item">
                  <div class="feature-name">购物零售</div>
                </div>
                <div class="feature-item">
                  <div class="feature-name">文体娱乐</div>
                </div>
                <div class="feature-item">
                  <div class="feature-name">生活服务</div>
                </div>
                <div class="feature-item">
                  <div class="feature-name">出行服务</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 商家价值 -->
      <div class="section">
        <div class="value-section">
          <div class="value-header">
            <h2>商家价值</h2>
            <p class="value-subtitle">为商家提供全方位数字化解决方案</p>
          </div>
          <div class="value-cards">
            <div class="value-card">
              <div class="value-card-header">
                <h3>精准引流</h3>
              </div>
              <div class="value-card-content">
                <p>借助协会平台，获得海量曝光与精准客流</p>
                <div class="value-benefits">
                  <div class="benefit-item">✓ 平台推荐位展示</div>
                  <div class="benefit-item">✓ 精准用户匹配</div>
                  <div class="benefit-item">✓ 多渠道流量导入</div>
                </div>
              </div>
            </div>

            <div class="value-card">
              <div class="value-card-header">
                <h3>数字化赋能</h3>
              </div>
              <div class="value-card-content">
                <p>轻松拥有线上店铺，实现数字化运营</p>
                <div class="value-benefits">
                  <div class="benefit-item">✓ 一键开店服务</div>
                  <div class="benefit-item">✓ 智能营销工具</div>
                  <div class="benefit-item">✓ 数据分析报告</div>
                </div>
              </div>
            </div>

            <div class="value-card">
              <div class="value-card-header">
                <h3>跨界合作</h3>
              </div>
              <div class="value-card-content">
                <p>与本市各行业优秀企业形成合力，共享资源</p>
                <div class="value-benefits">
                  <div class="benefit-item">✓ 行业资源整合</div>
                  <div class="benefit-item">✓ 联合营销活动</div>
                  <div class="benefit-item">✓ 商家互推合作</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目成果与邀约 -->
      <div class="section">
        <div class="results-section">
          <div class="results-header">
            <h2>项目成果与邀约</h2>
            <p class="results-subtitle">携手共建上饶数字化商业生态</p>
          </div>

          <div class="achievements">
            <div class="achievement-item">
              <div class="achievement-content">
                <h4>多场邀约会</h4>
                <p>成功举办商家邀约会，搭建合作桥梁</p>
              </div>
            </div>
            <div class="achievement-item">
              <div class="achievement-content">
                <h4>品牌入驻</h4>
                <p>数十家本地知名品牌率先入驻平台</p>
              </div>
            </div>
            <div class="achievement-item">
              <div class="achievement-content">
                <h4>生态建设</h4>
                <p>构建完整的数字化商业服务体系</p>
              </div>
            </div>
          </div>

          

          <div class="invitation-cta">
            <div class="cta-background">
              <div class="cta-content">
                <h3>诚邀加入一码通上饶大家庭</h3>
                <p>共同助力上饶消费，普惠广大市民，共创商业繁荣！</p>
                <div class="cta-buttons">
                  <button @click="goToContact" class="primary-btn">立即咨询</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/gesi-union/product-center')
}

const goToContact = () => {
  router.push('/card/gesi-union/ai-promoter')
}
</script>

<style scoped>
.page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 70px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
  .page {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 防止移动端缩放 */
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* 允许文本选择 */
  .intro-text, .value-card-content p, .achievement-content p, .cta-content p {
    -webkit-user-select: text;
    user-select: text;
  }
}

.header {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.2s;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  flex: 1;
  text-align: center;
  padding-right: 3rem;
}

.content {
  padding-top: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  margin-bottom: 2rem;
}

/* Hero Section */
.hero-section {
  margin-bottom: 2rem;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.hero-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 1rem;
}

/* 移动端图片优化 */
@media (max-width: 768px) {
  .hero-image {
    border-radius: 0.75rem;
    /* 防止图片闪烁 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Intro Card */
.intro-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.intro-header {
  margin-bottom: 1.5rem;
}

.intro-header h2 {
  color: #c41b21;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.intro-text {
  color: #555;
  font-size: 1rem;
  line-height: 1.7;
  margin: 0 0 2rem 0;
  text-align: justify;
}

.platform-features {
  margin-top: 2rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.feature-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
  .feature-item:active {
    transform: scale(0.98);
    background: #f0f0f0;
  }
}



.feature-name {
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Value Section */
.value-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.value-header {
  text-align: center;
  margin-bottom: 2rem;
}

.value-title-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.value-header h2 {
  color: #c41b21;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.value-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.value-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.value-card {
  background: linear-gradient(135deg, #fef5f5, #fff);
  border: 2px solid #fed7d7;
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.value-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #c41b21, #e53e3e);
}

.value-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.15);
  border-color: #c41b21;
}

/* 移动端价值卡片触摸优化 */
@media (max-width: 768px) {
  .value-card:active {
    transform: scale(0.98);
    box-shadow: 0 4px 15px rgba(196, 27, 33, 0.1);
  }
}

.value-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.value-card-icon {
  font-size: 2.5rem;
  background: white;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.value-card h3 {
  color: #c41b21;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.value-card-content p {
  color: #555;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.value-benefits {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.benefit-item {
  color: #333;
  font-size: 0.9rem;
  padding: 0.5rem 0;
  border-left: 3px solid #c41b21;
  padding-left: 1rem;
  background: rgba(196, 27, 33, 0.05);
  border-radius: 0 0.25rem 0.25rem 0;
}

/* Results Section */
.results-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.results-header {
  text-align: center;
  margin-bottom: 2rem;
}

.results-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.results-header h2 {
  color: #c41b21;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.results-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.achievements {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.achievement-item {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.75rem;
  border: 1px solid #e9ecef;
}

.achievement-content h4 {
  color: #c41b21;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.achievement-content p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.partners-showcase h3 {
  color: #c41b21;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.partner-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.partner-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 移动端合作伙伴卡片触摸优化 */
@media (max-width: 768px) {
  .partner-card:active {
    transform: scale(0.95);
    background: #f0f0f0;
  }
}

.join-card {
  background: linear-gradient(135deg, #fef5f5, #fff);
  border-color: #c41b21;
  border-style: dashed;
}



.partner-name {
  color: #333;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.partner-category {
  color: #666;
  font-size: 0.8rem;
  margin: 0;
}

.invitation-cta {
  margin-top: 2rem;
}

.cta-background {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  color: white;
  box-shadow: 0 8px 30px rgba(196, 27, 33, 0.3);
}



.cta-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.cta-content p {
  font-size: 1rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primary-btn, .secondary-btn {
  padding: 0.75rem 2rem;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid white;
}

.primary-btn {
  background: white;
  color: #c41b21;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.secondary-btn {
  background: transparent;
  color: white;
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* 移动端按钮触摸优化 */
@media (max-width: 768px) {
  .primary-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
  }

  .secondary-btn:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.15);
  }

  /* 增加按钮触摸区域 */
  .primary-btn, .secondary-btn {
    min-height: 44px;
    touch-action: manipulation;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .section {
    margin-bottom: 1.5rem;
  }

  /* 头部优化 */
  .header {
    padding: 0.5rem 0.75rem;
    height: 3rem;
  }

  .header h1 {
    font-size: 1rem;
    padding-right: 2.5rem;
  }

  .back-btn {
    font-size: 1rem;
    padding: 0.25rem 0.5rem;
  }

  .content {
    padding-top: 3.5rem;
  }

  /* 英雄区域移动端优化 */
  .hero-section {
    margin-bottom: 1.5rem;
    border-radius: 0.75rem;
  }

  .hero-image {
    border-radius: 0.75rem;
  }

  /* 介绍卡片移动端优化 */
  .intro-card {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .intro-header {
    margin-bottom: 1rem;
  }

  .intro-header h2 {
    font-size: 1.25rem;
  }

  .intro-text {
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }

  /* 功能网格移动端优化 */
  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .feature-item {
    padding: 0.75rem 0.5rem;
    border-radius: 0.5rem;
  }



  .feature-name {
    font-size: 0.8rem;
  }

  /* 价值区域移动端优化 */
  .value-section {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .value-header {
    margin-bottom: 1.5rem;
  }

  .value-title-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
  }

  .value-header h2 {
    font-size: 1.4rem;
  }

  .value-subtitle {
    font-size: 0.9rem;
  }

  .value-cards {
    gap: 1rem;
  }

  .value-card {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .value-card-header {
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .value-card-icon {
    width: 3rem;
    height: 3rem;
    font-size: 2rem;
  }

  .value-card h3 {
    font-size: 1.1rem;
  }

  .value-card-content p {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
  }

  .benefit-item {
    font-size: 0.8rem;
    padding: 0.4rem 0;
    padding-left: 0.75rem;
  }

  /* 成果区域移动端优化 */
  .results-section {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .results-header {
    margin-bottom: 1.5rem;
  }

  .results-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
  }

  .results-header h2 {
    font-size: 1.4rem;
  }

  .results-subtitle {
    font-size: 0.9rem;
  }

  .achievements {
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }

  .achievement-item {
    padding: 0.75rem;
    border-radius: 0.5rem;
  }

  .achievement-content h4 {
    font-size: 1rem;
  }

  .achievement-content p {
    font-size: 0.8rem;
  }

  /* 合作伙伴移动端优化 */
  .partners-showcase h3 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .partner-card {
    padding: 0.75rem 0.5rem;
    border-radius: 0.5rem;
  }



  .partner-name {
    font-size: 0.8rem;
    margin-bottom: 0.125rem;
  }

  .partner-category {
    font-size: 0.7rem;
  }

  /* CTA区域移动端优化 */
  .invitation-cta {
    margin-top: 1.5rem;
  }

  .cta-background {
    padding: 1.5rem;
    border-radius: 0.75rem;
  }



  .cta-content h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .cta-content p {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .primary-btn, .secondary-btn {
    width: 100%;
    max-width: 280px;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* 超小屏幕优化 (320px-480px) */
@media (max-width: 480px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .header {
    padding: 0.5rem;
  }

  .header h1 {
    font-size: 0.9rem;
  }

  /* 功能网格超小屏优化 */
  .feature-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .feature-item {
    padding: 0.5rem 0.25rem;
  }



  .feature-name {
    font-size: 0.75rem;
  }

  /* 价值卡片超小屏优化 */
  .value-card {
    padding: 1rem;
  }

  .value-card-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.5rem;
  }

  .value-card h3 {
    font-size: 1rem;
  }

  /* 合作伙伴超小屏优化 */
  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .partner-card {
    padding: 0.75rem;
  }

  /* 成就项目超小屏优化 */
  .achievement-item {
    text-align: center;
  }

  /* CTA按钮超小屏优化 */
  .primary-btn, .secondary-btn {
    max-width: 240px;
    padding: 0.75rem 1rem;
  }
}

@media (min-width: 768px) {
  .hero-section {
    height: 350px;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.3rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .stat-card {
    min-width: 140px;
    padding: 1.25rem;
  }

  .feature-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .value-cards {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .value-card {
    padding: 2rem;
  }

  .achievements {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .partners-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .intro-card, .value-section, .results-section {
    padding: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .hero-section {
    height: 400px;
  }

  .hero-title {
    font-size: 3.5rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .stat-card {
    min-width: 160px;
    padding: 1.5rem;
  }

  .feature-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  .value-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .partners-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .cta-buttons {
    gap: 2rem;
  }
}
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

</style>
