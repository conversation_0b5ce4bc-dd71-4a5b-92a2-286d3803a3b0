/**
 * 异步工具函数，用于处理可能导致消息通道错误的异步操作
 */

/**
 * 安全的复制文本到剪贴板
 * @param text 要复制的文本
 * @param successMessage 成功提示消息
 * @returns Promise<boolean> 是否成功复制
 */
export const safeClipboardWrite = async (text: string, successMessage: string = '已复制到剪贴板'): Promise<boolean> => {
  try {
    // 检查是否支持 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      alert(successMessage)
      return true
    } else {
      // 降级方案：使用传统的 document.execCommand
      return fallbackCopyTextToClipboard(text, successMessage)
    }
  } catch (err) {
    console.warn('Clipboard API failed, falling back to execCommand:', err)
    return fallbackCopyTextToClipboard(text, successMessage)
  }
}

/**
 * 降级复制方案
 * @param text 要复制的文本
 * @param successMessage 成功提示消息
 * @returns boolean 是否成功复制
 */
const fallbackCopyTextToClipboard = (text: string, successMessage: string): boolean => {
  try {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    
    if (successful) {
      alert(successMessage)
      return true
    } else {
      console.error('Failed to copy text using execCommand')
      return false
    }
  } catch (err) {
    console.error('Fallback copy failed:', err)
    return false
  }
}

/**
 * 安全的打开新窗口
 * @param url 要打开的URL
 * @param target 目标窗口名称，默认为 '_blank'
 * @param features 窗口特性
 * @returns Window | null 新窗口对象或null
 */
export const safeWindowOpen = (url: string, target: string = '_blank', features?: string): Window | null => {
  try {
    const newWindow = window.open(url, target, features)
    
    // 检查是否被弹窗拦截器阻止
    if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
      console.warn('Popup blocked, trying alternative approach')
      // 可以在这里添加用户提示，告知用户允许弹窗
      alert('请允许弹窗以打开链接，或者手动复制链接：' + url)
      return null
    }
    
    return newWindow
  } catch (err) {
    console.error('Failed to open window:', err)
    alert('无法打开链接，请手动访问：' + url)
    return null
  }
}

/**
 * 安全的定时器设置，避免内存泄漏
 * @param callback 回调函数
 * @param delay 延迟时间（毫秒）
 * @returns number 定时器ID
 */
export const safeSetTimeout = (callback: () => void, delay: number): number => {
  try {
    return window.setTimeout(() => {
      try {
        callback()
      } catch (err) {
        console.error('Timer callback error:', err)
      }
    }, delay)
  } catch (err) {
    console.error('Failed to set timeout:', err)
    return 0
  }
}

/**
 * 安全的间隔定时器设置
 * @param callback 回调函数
 * @param interval 间隔时间（毫秒）
 * @returns number 定时器ID
 */
export const safeSetInterval = (callback: () => void, interval: number): number => {
  try {
    return window.setInterval(() => {
      try {
        callback()
      } catch (err) {
        console.error('Interval callback error:', err)
      }
    }, interval)
  } catch (err) {
    console.error('Failed to set interval:', err)
    return 0
  }
}

/**
 * 安全的清除定时器
 * @param timerId 定时器ID
 */
export const safeClearTimeout = (timerId: number | null): void => {
  if (timerId !== null && timerId !== 0) {
    try {
      window.clearTimeout(timerId)
    } catch (err) {
      console.error('Failed to clear timeout:', err)
    }
  }
}

/**
 * 安全的清除间隔定时器
 * @param timerId 定时器ID
 */
export const safeClearInterval = (timerId: number | null): void => {
  if (timerId !== null && timerId !== 0) {
    try {
      window.clearInterval(timerId)
    } catch (err) {
      console.error('Failed to clear interval:', err)
    }
  }
}
