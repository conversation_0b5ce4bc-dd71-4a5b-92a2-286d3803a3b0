<template>
  <div class="health-center-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>汉氏医学健康体检中心</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/jiankangtijianzhongxin.png" 
          alt="汉氏医学健康体检中心" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">汉氏医学健康体检中心</h2>
          <p class="banner-subtitle">引领精准预防医学，预见您的健康未来</p>
        </div>
      </div>

      <!-- 标签页导航 -->
      <div class="section tab-section">
        <div class="tab-navigation">
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'concept' }"
            @click="switchTab('concept')"
          >
            <el-icon><Star /></el-icon>
            <span>核心理念</span>
          </div>
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'tech' }"
            @click="switchTab('tech')"
          >
            <el-icon><Cpu /></el-icon>
            <span>尖端科技</span>
          </div>
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'service' }"
            @click="switchTab('service')"
          >
            <el-icon><Service /></el-icon>
            <span>尊享服务</span>
          </div>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content">
          <!-- 核心理念内容 -->
          <div v-show="activeTab === 'concept'" class="tab-panel">
            <div class="concept-cards">
              <div class="concept-card">
                <div class="concept-icon">
                  <el-icon><PictureFilled /></el-icon>
                </div>
                <h3>环境之美</h3>
                <p>告别传统医院的冰冷，我们在花园式的细胞谷中为您打造宁静、舒适的五星级体检空间。</p>
              </div>
              <div class="concept-card">
                <div class="concept-icon">
                  <el-icon><Aim /></el-icon>
                </div>
                <h3>精准之核</h3>
                <p>不止于发现疾病，我们更专注于通过前沿科技评估您的健康风险，实现真正的"上医治未病"。</p>
              </div>
              <div class="concept-card">
                <div class="concept-icon">
                  <el-icon><Service /></el-icon>
                </div>
                <h3>服务之暖</h3>
                <p>从预约到报告解读，我们提供"一对一"全程导检与健康管理师服务，让您感受有温度的关怀。</p>
              </div>
            </div>
          </div>

          <!-- 尖端科技内容 -->
          <div v-show="activeTab === 'tech'" class="tab-panel">
            <div class="tech-cards">
              <div class="tech-card">
                <div class="tech-info">
                  <h3>美国GE 1.5T光纤磁共振</h3>
                  <p>业界唯一定量，可实现早期脑梗的精准筛查。</p>
                </div>
              </div>
              <div class="tech-card">
                <div class="tech-info">
                  <h3>美国GE Revolution 16 CT</h3>
                  <p>拥有8大低剂量金标准保障，是早期肺癌筛查的利器。</p>
                </div>
              </div>
              <div class="tech-card">
                <div class="tech-info">
                  <h3>美国GE Senographe Crystal乳腺机</h3>
                  <p>更高敏感性，更低放射剂量，提供乳腺癌筛查的最佳解决方案。</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 尊享服务内容 -->
          <div v-show="activeTab === 'service'" class="tab-panel">
           

            <!-- 服务流程详解 -->
            <div class="service-process">
              <h3>🔄 7步尊享体检流程</h3>
              <div class="process-timeline">
                <div class="process-step">
                  <div class="step-number">01</div>
                  <div class="step-content">
                    <h4>专业咨询</h4>
                    <p>健康管理师一对一咨询，了解健康状况和需求，制定个性化体检方案</p>
                    <span class="step-time">预计用时：15分钟</span>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">02</div>
                  <div class="step-content">
                    <h4>VIP接待</h4>
                    <p>专属接待员全程陪同，提供贵宾休息区、免费WiFi和精美茶点</p>
                    <span class="step-time">预计用时：10分钟</span>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">03</div>
                  <div class="step-content">
                    <h4>一对一导检</h4>
                    <p>专业导检护士全程陪同，优化检查路线，确保检查流程顺畅高效</p>
                    <span class="step-time">预计用时：90分钟</span>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">04</div>
                  <div class="step-content">
                    <h4>营养餐点</h4>
                    <p>检查间隙提供营养早餐或午餐，补充体力，确保检查质量</p>
                    <span class="step-time">预计用时：30分钟</span>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">05</div>
                  <div class="step-content">
                    <h4>三级会诊</h4>
                    <p>主任医师、副主任医师、主治医师三级会诊，确保诊断准确无误</p>
                    <span class="step-time">预计用时：20分钟</span>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">06</div>
                  <div class="step-content">
                    <h4>档案建立</h4>
                    <p>建立个人健康档案，详细记录检查结果和健康建议，便于长期追踪</p>
                    <span class="step-time">预计用时：10分钟</span>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">07</div>
                  <div class="step-content">
                    <h4>持续追踪</h4>
                    <p>健康管理师定期回访，提供健康指导和复查提醒，全年健康守护</p>
                    <span class="step-time">全年服务</span>
                  </div>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Star, 
  Cpu, 
  Service,
  PictureFilled,
  Aim,
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 当前激活的标签页
const activeTab = ref('concept')

// 返回上一页
const goBack = () => {
  router.back()
}

// 切换标签页
const switchTab = (tab: string) => {
  activeTab.value = tab
}
</script>

<style scoped>
/* 页面容器 */
.health-center-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px; /* 为固定头部留出空间 */
  padding-bottom: 80px; /* 为底部导航栏留出空间 */
}

/* 移动端优化页面容器 */
@media (max-width: 767px) {
  .health-center-container {
    padding-top: 52px; /* 减少顶部间距 */
    padding-bottom: 75px; /* 减少底部间距 */
  }
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.back-button:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 8px; /* 进一步减少内边距 */
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px; /* 减小圆角 */
  overflow: hidden;
  margin-bottom: 12px; /* 减少标签页下边距 */ /* 进一步减少下边距 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  transition: all 0.5s ease; /* 添加过渡效果 */
}

.banner-section:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease; /* 添加图片过渡效果 */
}

.banner-section:hover .banner-image {
  transform: scale(1.03); /* 悬停时图片轻微放大 */
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px; /* 进一步减少内边距 */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%); /* 增强渐变效果 */
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 移动端横幅优化 */
@media (max-width: 767px) {
  .banner-image {
    max-height: 180px; /* 限制移动端图片高度 */
    object-fit: cover; /* 保持图片比例并裁剪 */
  }
  
  .banner-overlay {
    padding: 16px; /* 减少移动端内边距 */
  }
  
  .banner-title {
    font-size: 22px; /* 优化移动端标题字体大小 */
    margin: 0 0 8px 0;
    line-height: 1.3; /* 添加行高优化 */
  }
  
  .banner-subtitle {
    font-size: 16px; /* 增大移动端副标题字体 */
    line-height: 1.5; /* 添加行高优化 */
  }
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px; /* 减小圆角 */
  padding: 12px; /* 进一步减少内边距 */
  margin-bottom: 12px; /* 进一步减少下边距 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 20px; /* 优化字体大小 */
  color: #0f9da8;
  margin: 0 0 12px 0; /* 进一步优化下边距 */
  line-height: 1.4; /* 添加行高优化 */
  gap: 8px;
}

/* 标签页导航样式 */
.tab-section {
  padding: 0; /* 移除内边距，让标签页占满宽度 */
}

.tab-navigation {
  display: flex;
  background: #f8fafc;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 16px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影效果 */
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
  font-size: 14px;
  gap: 4px;
}

.tab-item:hover {
  color: #0f9da8;
  background: rgba(15, 157, 168, 0.1);
}

.tab-item.active {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  box-shadow: 0 4px 12px rgba(15, 157, 168, 0.3);
  transform: translateY(-1px); /* 轻微上移效果 */
}

.tab-item .el-icon {
  font-size: 18px;
}

.tab-item span {
  font-weight: 500;
}

/* 标签页内容 */
.tab-content {
  padding: 16px;
  min-height: 300px; /* 设置最小高度避免内容跳动 */
}

.tab-panel {
  animation: fadeIn 0.3s ease-in-out; /* 添加淡入动画 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 核心理念卡片 */
.concept-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px; /* 减少间距 */
}

.concept-card {
  background: #f8fafc;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px; /* 减少内边距 */
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 添加阴影 */
}

.concept-card:hover {
  transform: translateY(-3px); /* 增强悬停效果 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.concept-icon {
  width: 50px; /* 减小图标尺寸 */
  height: 50px; /* 减小图标尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px; /* 减少下边距 */
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.concept-card:hover .concept-icon {
  transform: scale(1.1); /* 悬停时图标放大 */
}

.concept-icon .el-icon {
  font-size: 28px; /* 增大图标 */
  color: white;
}

.concept-card h3 {
  font-size: 20px; /* 增大标题 */
  margin: 0 0 14px 0; /* 增加下边距 */
  color: #333;
  font-weight: 600; /* 加粗标题 */
}

.concept-card p {
  font-size: 15px; /* 增大描述文字 */
  color: #666;
  line-height: 1.7; /* 增加行高 */
  margin: 0;
}

/* 尖端科技卡片 */
.tech-cards {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 减少卡片间距 */
}

.tech-card {
  background: #f8fafc;
  border-radius: 12px; /* 减小圆角 */
  overflow: hidden;
  border: 1px solid #edf2f7;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
  text-align: center; /* 居中文本 */
}

.tech-card:hover {
  transform: translateY(-3px); /* 增强悬停效果 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

/* 移除tech-image相关样式 */

.tech-info {
  padding: 20px; /* 减少内边距 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中内容 */
  align-items: center; /* 水平居中内容 */
}

.tech-info h3 {
  font-size: 20px; /* 增加标题字体大小 */
  margin: 0 0 15px 0; /* 增加下边距 */
  color: #333;
  font-weight: 600; /* 加粗标题 */
  position: relative; /* 为下划线定位 */
  padding-bottom: 12px; /* 为下划线留出空间 */
}

.tech-info h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  border-radius: 3px;
}

.tech-info p {
  font-size: 16px; /* 增加描述文字大小 */
  color: #666;
  line-height: 1.7; /* 增加行高 */
  margin: 0;
}

/* 服务特色样式 */
.service-highlights {
  margin-bottom: 25px;
}

.service-highlights h3 {
  color: #0f9da8;
  margin-bottom: 15px;
  font-size: 18px;
  text-align: center;
}

.highlight-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.highlight-item {
  background: #f8fafc;
  border: 1px solid #edf2f7;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-2px);
  border-color: #0f9da8;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.highlight-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.highlight-item h4 {
  color: #333;
  margin: 0 0 6px 0;
  font-size: 14px;
  font-weight: 600;
}

.highlight-item p {
  color: #666;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

/* 服务流程详解样式 */
.service-process {
  margin-bottom: 25px;
}

.service-process h3 {
  color: #0f9da8;
  margin-bottom: 15px;
  font-size: 18px;
  text-align: center;
}

.process-timeline {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.process-step {
  display: flex;
  align-items: flex-start;
  background: #f8fafc;
  border: 1px solid #edf2f7;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.process-step:hover {
  transform: translateY(-2px);
  border-color: #0f9da8;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.step-number {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(15, 157, 168, 0.3);
}

.step-content {
  flex: 1;
}

.step-content h4 {
  color: #333;
  margin: 0 0 6px 0;
  font-size: 15px;
  font-weight: 600;
}

.step-content p {
  color: #666;
  margin: 0 0 6px 0;
  font-size: 13px;
  line-height: 1.4;
}

.step-time {
  color: #0f9da8;
  font-size: 11px;
  font-weight: 500;
}

/* 增值服务样式 */
.value-added-services h3 {
  color: #0f9da8;
  margin-bottom: 15px;
  font-size: 18px;
  text-align: center;
}

.service-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.service-card {
  background: #f8fafc;
  border: 1px solid #edf2f7;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-2px);
  border-color: #0f9da8;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.service-card h4 {
  color: #333;
  margin: 0 0 6px 0;
  font-size: 14px;
  font-weight: 600;
}

.service-card p {
  color: #666;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

/* 联系卡片 */
.contact-section {
  margin-bottom: 20px; /* 减少下边距 */
}

.contact-card {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  border-radius: 16px; /* 减小圆角 */
  padding: 20px; /* 减少内边距 */
  color: white;
  text-align: center;
  box-shadow: 0 8px 30px rgba(15, 157, 168, 0.3); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
  position: relative;
  overflow: hidden;
}

.contact-card:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(15, 157, 168, 0.4);
}

/* 添加背景动画效果 */
.contact-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
  transform: rotate(30deg);
  transition: all 0.8s ease;
  z-index: 1;
  opacity: 0;
}

.contact-card:hover::before {
  opacity: 1;
  transform: rotate(0deg);
}

.contact-card h3 {
  font-size: 20px; /* 减小标题 */
  margin: 0 0 10px 0; /* 减少下边距 */
  font-weight: 600; /* 加粗标题 */
  position: relative;
  z-index: 2;
}

.contact-card p {
  font-size: 15px; /* 减小描述文字 */
  opacity: 0.9;
  margin: 0 0 16px 0; /* 减少下边距 */
  position: relative;
  z-index: 2;
}

.contact-actions {
  display: flex;
  justify-content: center;
  gap: 15px; /* 减少间距 */
  position: relative;
  z-index: 2;
}

.contact-btn {
  background: white;
  color: #0f9da8;
  border: none;
  padding: 10px 20px; /* 减少内边距 */
  border-radius: 25px; /* 减小圆角 */
  font-weight: 600; /* 加粗文字 */
  font-size: 15px; /* 减小字体 */
  display: flex;
  align-items: center;
  gap: 8px; /* 减少间距 */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.contact-btn:hover {
  background: #f8fafc;
  color: #0f9da8;
  transform: translateY(-2px); /* 添加悬停效果 */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15); /* 增强阴影 */
}

/* 响应式设计 */
@media (min-width: 768px) {
  .concept-cards {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .tech-cards {
    display: grid; /* 改为网格布局 */
    grid-template-columns: repeat(3, 1fr); /* 三列布局 */
    gap: 20px; /* 减少间距 */
  }
  
  .tech-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px 16px; /* 减少内边距 */
  }
  
  /* 移除tech-image相关样式 */
  
  .tech-info {
    padding: 0;
  }
  
  .tech-info h3 {
    font-size: 22px; /* 增大标题字体 */
  }
  
  .tech-info p {
    font-size: 16px; /* 增大描述文字 */
  }
  
  .banner-title {
    font-size: 32px; /* 增大标题 */
  }
  
  .banner-subtitle {
    font-size: 20px; /* 增大副标题 */
  }
  
  .service-flow {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 平板端改为2列 */
    gap: 20px;
  }
  
  .flow-step {
    padding: 20px; /* 减少内边距 */
    height: 100%; /* 确保高度一致 */
  }
  
  .flow-step:hover {
    transform: translateY(-5px); /* 增强悬停效果 */
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }
  
  .step-number {
    width: 50px; /* 减小尺寸 */
    height: 50px; /* 减小尺寸 */
    font-size: 20px; /* 减小字体 */
    margin-bottom: 15px; /* 减少下边距 */
  }
  
  .step-content h3 {
    font-size: 18px; /* 减小标题 */
    margin-bottom: 10px; /* 减少下边距 */
  }
  
  .step-content p {
    font-size: 15px; /* 减小描述文字 */
  }
  
  .step-arrow {
    bottom: -15px; /* 调整位置到底部 */
    left: 50%; /* 水平居中 */
    transform: translateX(-50%); /* 水平居中偏移 */
    font-size: 28px; /* 增大箭头 */
  }
  
  /* 最后一行步骤没有箭头 */
  .flow-step:nth-child(n+5) .step-arrow,
  .flow-step:last-child .step-arrow {
    display: none;
  }
  
  /* 桌面端优化 */
  @media (min-width: 1024px) {
    .service-flow {
      grid-template-columns: repeat(4, 1fr); /* 桌面端4列 */
    }

    .flow-step {
      padding: 20px;
    }

    .step-number {
      width: 50px;
      height: 50px;
      font-size: 20px;
      margin-bottom: 15px;
    }

    .step-content h3 {
      font-size: 18px;
      margin-bottom: 10px;
    }

    .step-content p {
      font-size: 15px;
    }
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .content {
    padding: 8px;
  }

  /* 横幅优化 */
  .banner-section {
    margin-bottom: 16px;
  }

  .banner-image {
    height: 200px;
    object-fit: cover;
  }

  .banner-overlay {
    padding: 16px;
  }

  .banner-title {
    font-size: 20px;
    margin-bottom: 6px;
  }

  .banner-subtitle {
    font-size: 14px;
  }

  /* 通用部分优化 */
  .section {
    margin-bottom: 12px;
    padding: 12px;
  }

  .tab-navigation {
    padding: 3px; /* 减少标签页导航内边距 */
  }

  .tab-item {
    padding: 14px 8px; /* 增加触摸友好的内边距 */
    font-size: 13px;
    min-height: 48px; /* 确保最小触摸区域 */
  }

  .tab-item .el-icon {
    font-size: 18px; /* 保持图标可见性 */
  }

  .tab-content {
    padding: 12px; /* 减少标签页内容内边距 */
    min-height: 250px; /* 减小最小高度 */
  }

  .concept-cards {
    gap: 12px;
  }

  .concept-card {
    padding: 14px; /* 减少内边距 */
  }

  .concept-icon {
    width: 42px; /* 减小图标尺寸 */
    height: 42px;
    margin-bottom: 10px;
  }

  .concept-icon .el-icon {
    font-size: 22px;
  }

  .concept-card h3 {
    font-size: 17px;
    margin-bottom: 8px;
  }

  .concept-card p {
    font-size: 14px;
    line-height: 1.5;
  }

  .tech-cards {
    gap: 12px;
  }

  .tech-card {
    padding: 14px;
  }

  .tech-info h3 {
    font-size: 17px;
    margin-bottom: 8px;
  }

  .tech-info p {
    font-size: 14px;
  }

  /* 服务特色移动端优化 */
  .service-highlights h3,
  .service-process h3,
  .value-added-services h3 {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .highlight-grid,
  .service-cards {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .highlight-item,
  .service-card {
    padding: 12px;
  }

  .highlight-icon,
  .card-icon {
    font-size: 20px;
    margin-bottom: 6px;
  }

  .highlight-item h4,
  .service-card h4 {
    font-size: 13px;
  }

  .highlight-item p,
  .service-card p {
    font-size: 11px;
  }

  /* 服务流程移动端优化 */
  .process-timeline {
    gap: 10px;
  }

  .process-step {
    padding: 12px;
  }

  .step-number {
    width: 32px;
    height: 32px;
    font-size: 10px;
    margin-right: 12px;
  }

  .step-content h4 {
    font-size: 13px;
    margin-bottom: 4px;
  }

  .step-content p {
    font-size: 11px;
    margin-bottom: 4px;
  }

  .step-time {
    font-size: 10px;
  }
}
@media (max-width: 768px) {
  .back-button {
    display: none;
  }
}
</style>