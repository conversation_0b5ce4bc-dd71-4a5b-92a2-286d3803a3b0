<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术参数
const technicalSpecs = reactive([
  { 
    model: 'KG/VC6', 
    filterGrade: 'F6', 
    hepaGrade: '-', 
    dustSpotEfficiency: '65',
    sodiumFlameEfficiency: '-',
    initialResistance: '120',
    temperature: '80'
  },
  { 
    model: 'KG/VC7', 
    filterGrade: 'F7', 
    hepaGrade: '-', 
    dustSpotEfficiency: '85',
    sodiumFlameEfficiency: '-',
    initialResistance: '140',
    temperature: '80'
  },
  { 
    model: 'KG/VC9', 
    filterGrade: 'F9', 
    hepaGrade: '-', 
    dustSpotEfficiency: '95',
    sodiumFlameEfficiency: '-',
    initialResistance: '180',
    temperature: '80'
  },
  { 
    model: 'KG/VC11', 
    filterGrade: '-', 
    hepaGrade: 'H11', 
    dustSpotEfficiency: '-',
    sodiumFlameEfficiency: '>98',
    initialResistance: '125',
    temperature: '80'
  },
  { 
    model: 'KG/VC13', 
    filterGrade: '-', 
    hepaGrade: 'H13', 
    dustSpotEfficiency: '-',
    sodiumFlameEfficiency: '>99.997',
    initialResistance: '250',
    temperature: '80'
  }
])

// 性能特点
const performanceFeatures = reactive([
  {
    title: '适用于需要高洁净度的空气净化场合',
    description: '专为高洁净度要求设计，适用于电子、医药、食品、精密仪器等对空气质量要求极高的行业。'
  },
  {
    title: '可提供各种不同过滤等级的高效过滤器',
    description: '提供F6到H13等级的全系列高效过滤器，满足不同洁净度等级的需求。'
  },
  {
    title: '可提供各种不同规格的高效过滤器',
    description: '过滤器尺寸为292mm、490mm、610mm等多种规格，满足不同设备和系统的安装需求。'
  },
  {
    title: '采用优质滤料和精密制造工艺',
    description: '选用进口优质滤料，采用精密制造工艺，确保过滤效率和产品质量的稳定性。'
  },
  {
    title: '低阻力设计，节能高效',
    description: '优化的结构设计，在保证高效过滤的同时，最大限度降低运行阻力，节约能耗。'
  },
  {
    title: '标准化尺寸，通用性强',
    description: '采用国际标准尺寸设计，与各种空调和净化设备兼容性好，安装维护方便。'
  }
])

// 应用范围
const applications = reactive([
  {
    title: '洁净室工程',
    description: '各等级洁净室的终端过滤，确保洁净室达到设计洁净度要求。'
  },
  {
    title: '电子制造',
    description: '半导体、集成电路、液晶显示器等电子产品制造过程的空气净化。'
  },
  {
    title: '医药生产',
    description: '制药车间、无菌室、生物安全柜等医药生产环境的空气过滤。'
  },
  {
    title: '食品加工',
    description: '食品生产车间、包装间等对卫生要求严格的食品加工环境。'
  },
  {
    title: '精密仪器',
    description: '精密仪器制造、检测实验室等对环境洁净度要求极高的场所。'
  },
  {
    title: '生物实验室',
    description: '生物安全实验室、细胞培养室等生物研究和实验环境。'
  },
  {
    title: '医疗设施',
    description: '手术室、ICU、血液透析室等医疗设施的空气净化系统。'
  },
  {
    title: '光学工业',
    description: '光学元件制造、激光设备等对粉尘控制要求严格的光学工业。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>高效过滤器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gaoxiao.jpg" alt="高效过滤器" />
        </div>
        <div class="hero-content">
          <h2>KG/VC系列高效过滤器</h2>
          <p class="product-subtitle">F6-H13等级，满足最严苛的洁净度要求</p>
          <div class="product-intro">
            <p>高效过滤器是洁净室和高洁净度环境的核心组件，采用优质滤料和精密制造工艺，提供F6到H13等级的高效过滤。具有过滤效率高、阻力低、使用寿命长的特点。</p>
            <p>产品广泛应用于电子制造、医药生产、食品加工、精密仪器、生物实验室等对空气质量要求极高的行业，是构建洁净环境不可缺少的关键设备。</p>
          </div>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术参数
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>过滤器等级</th>
                <th>高效过滤器等级</th>
                <th>平均大气尘比色法效率(%)</th>
                <th>钠焰法效率(%)</th>
                <th>额定流量下的初阻力(Pa)</th>
                <th>耐温(℃)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.filterGrade }}</td>
                <td>{{ spec.hepaGrade }}</td>
                <td>{{ spec.dustSpotEfficiency }}</td>
                <td>{{ spec.sodiumFlameEfficiency }}</td>
                <td>{{ spec.initialResistance }}</td>
                <td>{{ spec.temperature }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 性能特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          性能特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in performanceFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 应用范围 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用范围
        </h3>
        <div class="applications">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🔍</div>
            <div class="indicator-content">
              <h4>过滤等级</h4>
              <div class="indicator-value">F6-H13</div>
              <p>全系列等级</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📊</div>
            <div class="indicator-content">
              <h4>过滤效率</h4>
              <div class="indicator-value">65-99.997%</div>
              <p>高效过滤</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">💨</div>
            <div class="indicator-content">
              <h4>初阻力</h4>
              <div class="indicator-value">120-250Pa</div>
              <p>低阻力设计</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🌡️</div>
            <div class="indicator-content">
              <h4>耐温性能</h4>
              <div class="indicator-value">≤80℃</div>
              <p>稳定可靠</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📏</div>
            <div class="indicator-content">
              <h4>标准尺寸</h4>
              <div class="indicator-value">292-610mm</div>
              <p>多种规格</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🏭</div>
            <div class="indicator-content">
              <h4>制造工艺</h4>
              <div class="indicator-value">精密制造</div>
              <p>质量可靠</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 过滤等级详解 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          过滤等级详解
        </h3>
        <div class="filter-grades">
          <div class="grade-category">
            <h4>中高效过滤器系列</h4>
            <div class="grade-items">
              <div class="grade-item">
                <div class="grade-label">F6</div>
                <div class="grade-info">
                  <div class="grade-efficiency">效率: 65%</div>
                  <div class="grade-desc">中效过滤，空调系统中级过滤</div>
                </div>
              </div>
              <div class="grade-item">
                <div class="grade-label">F7</div>
                <div class="grade-info">
                  <div class="grade-efficiency">效率: 85%</div>
                  <div class="grade-desc">中高效过滤，洁净室预过滤</div>
                </div>
              </div>
              <div class="grade-item">
                <div class="grade-label">F9</div>
                <div class="grade-info">
                  <div class="grade-efficiency">效率: 95%</div>
                  <div class="grade-desc">高中效过滤，高效过滤器前置</div>
                </div>
              </div>
            </div>
          </div>
          <div class="grade-category">
            <h4>高效过滤器系列</h4>
            <div class="grade-items">
              <div class="grade-item">
                <div class="grade-label">H11</div>
                <div class="grade-info">
                  <div class="grade-efficiency">效率: >98%</div>
                  <div class="grade-desc">高效过滤，洁净室终端过滤</div>
                </div>
              </div>
              <div class="grade-item">
                <div class="grade-label">H13</div>
                <div class="grade-info">
                  <div class="grade-efficiency">效率: >99.997%</div>
                  <div class="grade-desc">超高效过滤，最高洁净度要求</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🔬</div>
            <div class="advantage-content">
              <h4>高洁净度专用设计</h4>
              <p>专为高洁净度要求设计，适用于电子、医药、食品、精密仪器等对空气质量要求极高的行业，确保达到最严苛的洁净度标准。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📊</div>
            <div class="advantage-content">
              <h4>全系列过滤等级</h4>
              <p>提供F6到H13等级的全系列高效过滤器，满足不同洁净度等级的需求，可根据具体应用选择最适合的过滤等级。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📏</div>
            <div class="advantage-content">
              <h4>多种规格选择</h4>
              <p>提供292mm、490mm、610mm等多种规格尺寸，满足不同设备和系统的安装需求，具有良好的通用性和兼容性。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🏭</div>
            <div class="advantage-content">
              <h4>精密制造工艺</h4>
              <p>选用进口优质滤料，采用精密制造工艺，严格的质量控制体系，确保每一台过滤器的过滤效率和产品质量的稳定性。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <div class="advantage-content">
              <h4>低阻力节能设计</h4>
              <p>优化的结构设计和滤料配置，在保证高效过滤的同时，最大限度降低运行阻力，减少能耗，降低运营成本。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔧</div>
            <div class="advantage-content">
              <h4>标准化通用设计</h4>
              <p>采用国际标准尺寸设计，与各种空调和净化设备兼容性好，安装维护方便，可直接替换同规格产品。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases">
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/2-zykt/gaoxiaoguolv.jpg" alt="高效过滤器工程案例" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.5rem;
  min-width: 800px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.5rem 0.25rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.45rem;
  line-height: 1.1;
  border: 1px solid #2563eb;
}

.specs-table td {
  padding: 0.5rem 0.25rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.5rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.3;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 应用范围 */
.applications {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.application-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 过滤等级详解 */
.filter-grades {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.grade-category {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border-left: 4px solid #3b82f6;
}

.grade-category h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
}

.grade-items {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.grade-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
  transition: transform 0.2s ease;
}

.grade-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.grade-label {
  font-size: 1rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 0.25rem;
  padding: 0.5rem 0.75rem;
  min-width: 3rem;
  text-align: center;
  flex-shrink: 0;
}

.grade-info {
  flex: 1;
}

.grade-efficiency {
  font-size: 0.9rem;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.grade-desc {
  font-size: 0.8rem;
  color: #4b5563;
  line-height: 1.3;
}

/* 产品优势 */
.advantages {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 工程案例样式 */
.project-cases {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.case-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  max-width: 600px;
  width: 100%;
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.case-item img {
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .specs-table {
    font-size: 0.6rem;
  }

  .specs-table th {
    padding: 0.75rem 0.5rem;
    font-size: 0.55rem;
  }

  .specs-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.6rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }

  .feature-card p {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .filter-grades {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .grade-items {
    grid-template-columns: 1fr;
  }

  .grade-item {
    padding: 1.25rem;
  }

  .grade-label {
    font-size: 1.1rem;
    padding: 0.6rem 0.9rem;
  }

  .grade-efficiency {
    font-size: 1rem;
  }

  .grade-desc {
    font-size: 0.85rem;
  }

  .advantages {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .application-card {
    padding: 1.5rem;
  }

  .application-card h4 {
    font-size: 1.1rem;
  }

  .application-card p {
    font-size: 0.9rem;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.65rem;
  }

  .specs-table th {
    font-size: 0.6rem;
  }

  .specs-table td {
    font-size: 0.65rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .grade-items {
    grid-template-columns: 1fr;
  }
}
</style>
