# 第二个服务：3010端口 - 原有服务（优化iOS语音支持）
server {
    listen 3010 ssl;
    server_name chat.agiser.cn;

    # ================== SSL 证书配置 ==================
    ssl_certificate /www/server/panel/vhost/cert/chat.agiser.cn/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/chat.agiser.cn/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 强制 HTTPS（3010端口）
    error_page 497 https://$host:3010$request_uri;

    # ================== iOS 语音支持优化头部 ==================
    # 添加安全头部，支持 iOS Safari 语音功能
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 关键：允许麦克风权限（iOS 语音识别必需）
    add_header Permissions-Policy "microphone=(self), camera=(), geolocation=(), payment=()" always;
    
    # 支持 Web Speech API
    add_header Feature-Policy "microphone 'self'" always;

    # ================== 反向代理到 127.0.0.1:3000 ==================
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # iOS 优化：确保正确的协议传递
        proxy_set_header X-Forwarded-Ssl on;
        proxy_set_header X-Forwarded-Port 3010;
        
        # 禁用缓存，确保实时通信
        proxy_cache off;
        proxy_buffering off;
        
        # WebSocket 支持优化
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 86400;
    }

    # 静态资源缓存优化（可选）
    location /static/ {
        proxy_pass http://127.0.0.1:3000/static/;
        expires 365d;
        add_header Cache-Control "public";
        access_log off;
    }

    # API 接口优化（如果有语音相关API）
    location /api/ {
        proxy_pass http://127.0.0.1:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间，支持语音处理
        proxy_read_timeout 300;
        proxy_send_timeout 300;
        proxy_connect_timeout 300;
        
        # 支持大文件上传（语音文件）
        client_max_body_size 50M;
    }

    # ================== 安全与日志配置 ==================
    # 禁止访问敏感文件
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md) {
        return 404;
    }

    # 放行 SSL 证书验证目录
    location ~ \.well-known {
        allow all;
    }

    # 日志文件路径（单独命名，避免冲突）
    access_log /www/wwwlogs/chat.agiser.cn_3010.log;
    error_log /www/wwwlogs/chat.agiser.cn_3010.error.log;

    # Monitor-Config-Start 网站监控报表日志发送配置
    access_log syslog:server=unix:/tmp/bt-monitor.sock,nohostname,tag=4__access monitor;
    error_log syslog:server=unix:/tmp/bt-monitor.sock,nohostname,tag=4__error;
    # Monitor-Config-End
}
