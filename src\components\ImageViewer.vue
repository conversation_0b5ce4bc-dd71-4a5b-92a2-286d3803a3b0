<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import { ZoomIn, ZoomOut, Close } from '@element-plus/icons-vue'

// 定义组件的props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    default: ''
  }
})

// 定义组件的事件
const emit = defineEmits(['update:visible', 'close'])

// 图片查看器相关状态
const imageScale = ref(1)
const imageTranslateX = ref(0)
const imageTranslateY = ref(0)
const isDragging = ref(false)
const startX = ref(0)
const startY = ref(0)
const startDistance = ref(0)
const initialScale = ref(1)

// 关闭图片查看器
const closeImageViewer = () => {
  emit('update:visible', false)
  emit('close')
  document.body.style.overflow = ''
}

// 缩放图片
const zoomImage = (factor: number) => {
  const newScale = imageScale.value * factor
  // 限制缩放范围
  if (newScale >= 0.5 && newScale <= 5) {
    imageScale.value = newScale
  }
}

// 处理触摸开始事件
const handleTouchStart = (e: TouchEvent) => {
  if (e.touches.length === 1) {
    // 单指拖动
    isDragging.value = true
    startX.value = e.touches[0].clientX - imageTranslateX.value
    startY.value = e.touches[0].clientY - imageTranslateY.value
  } else if (e.touches.length === 2) {
    // 双指缩放
    startDistance.value = Math.hypot(
      e.touches[0].clientX - e.touches[1].clientX,
      e.touches[0].clientY - e.touches[1].clientY
    )
    initialScale.value = imageScale.value
  }
}

// 处理触摸移动事件
const handleTouchMove = (e: TouchEvent) => {
  if (isDragging.value && e.touches.length === 1) {
    // 单指拖动
    imageTranslateX.value = e.touches[0].clientX - startX.value
    imageTranslateY.value = e.touches[0].clientY - startY.value
    e.preventDefault()
  } else if (e.touches.length === 2) {
    // 双指缩放
    const currentDistance = Math.hypot(
      e.touches[0].clientX - e.touches[1].clientX,
      e.touches[0].clientY - e.touches[1].clientY
    )
    const scaleFactor = currentDistance / startDistance.value
    imageScale.value = initialScale.value * scaleFactor
    
    // 限制缩放范围
    if (imageScale.value < 0.5) imageScale.value = 0.5
    if (imageScale.value > 5) imageScale.value = 5
    
    e.preventDefault()
  }
}

// 处理触摸结束事件
const handleTouchEnd = () => {
  isDragging.value = false
}

// 处理浏览器返回按钮
const handlePopState = (event: PopStateEvent) => {
  if (props.visible) {
    closeImageViewer()
    // 阻止默认的返回行为
    event.preventDefault()
  }
}

// 监听props.visible的变化
const handleVisibleChange = () => {
  if (props.visible) {
    // 打开图片查看器时重置状态
    imageScale.value = 1
    imageTranslateX.value = 0
    imageTranslateY.value = 0
    
    // 添加历史记录，这样当用户点击返回按钮时可以关闭查看器而不是导航回上一页
    window.history.pushState({ imageViewer: true }, '')
    
    // 防止页面滚动
    document.body.style.overflow = 'hidden'
  }
}

// 监听visible属性变化
watch(() => props.visible, handleVisibleChange, { immediate: true })

onMounted(() => {
  window.addEventListener('popstate', handlePopState)
})

onBeforeUnmount(() => {
  window.removeEventListener('popstate', handlePopState)
  // 确保在组件卸载时恢复页面滚动
  document.body.style.overflow = ''
})
</script>

<template>
  <div v-if="visible" class="image-viewer">
    <div 
      class="image-viewer-content" 
      @touchstart="handleTouchStart" 
      @touchmove="handleTouchMove" 
      @touchend="handleTouchEnd"
    >
      <img 
        :src="imageUrl" 
        :style="{
          transform: `scale(${imageScale}) translate(${imageTranslateX / imageScale}px, ${imageTranslateY / imageScale}px)`
        }" 
        alt="查看大图"
      />
    </div>
    <div class="image-viewer-toolbar">
      <el-button class="toolbar-btn" @click="zoomImage(1.2)" circle>
        <el-icon><ZoomIn /></el-icon>
      </el-button>
      <el-button class="toolbar-btn" @click="zoomImage(0.8)" circle>
        <el-icon><ZoomOut /></el-icon>
      </el-button>
      <el-button class="toolbar-btn" @click="closeImageViewer" circle>
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<style scoped>
/* 图片查看器样式 */
.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.image-viewer-toolbar {
  display: flex;
  justify-content: center;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.toolbar-btn {
  margin: 0 0.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
}

.image-viewer-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  touch-action: none; /* 防止浏览器默认的触摸行为 */
}

.image-viewer-content img {
  max-width: 100%;
  max-height: 100%;
  transition: transform 0.1s ease;
  will-change: transform; /* 优化性能 */
}
</style> 