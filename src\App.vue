<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { watch } from 'vue'

const route = useRoute()

// 监听路由变化，动态设置网站图标和页面标题
watch(() => route.path, () => {
  // 更新网站图标
  const iconPath = route.meta.favicon as string
  if (iconPath) {
    let link = document.querySelector("link[rel*='icon']") as HTMLLinkElement
    if (!link) {
      link = document.createElement('link')
      document.getElementsByTagName('head')[0].appendChild(link)
    }
    link.type = 'image/x-icon'
    link.rel = 'shortcut icon'
    link.href = iconPath
  }
  
  // 更新页面标题
  const title = route.meta.title as string
  if (title) {
    document.title = title
  }
}, { immediate: true })
</script>

<template>
  <RouterView />
</template>

<style>
/* Global styles are in style.css */
</style>