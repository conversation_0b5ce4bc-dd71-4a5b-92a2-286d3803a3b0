<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.back()
}

// 轮播图设置
const carouselSettings = ref({
  interval: 3000,  // 自动切换的时间间隔，单位为毫秒
  arrow: 'always', // 始终显示箭头
  type: 'card',    // 卡片化
  height: '300px'  // 轮播图高度
})

// 页面加载时执行
onMounted(() => {
  // 设置页面标题
  document.title = '星图AI体验课'
  
  // 根据屏幕宽度动态设置轮播图高度和类型
  const updateCarouselSettings = () => {
    if (window.innerWidth <= 767) {
      carouselSettings.value.height = '200px'
      carouselSettings.value.type = '' // 移动端使用普通轮播
    } else {
      carouselSettings.value.height = '300px'
      carouselSettings.value.type = 'card' // PC端使用卡片式轮播
    }
  }
  
  // 初始设置
  updateCarouselSettings()
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateCarouselSettings)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>《你好，我的AI新朋友！》</h1>
    </div>

    <div class="content">
      <!-- 板块一：产品介绍 -->
      <div class="hero-section">
        <h2 class="main-title">一场释放想象力的魔法派对</h2>
        <p class="subtitle">星图AI最受欢迎的入门体验活动</p>
      </div>

      <!-- 板块二：课程介绍 -->
      <div class="intro-section">
        <div class="section-card">
          <p class="intro-text" style="text-indent: 2em;">
               这是星图AI最受欢迎的入门体验活动。在这奇妙的90分钟里，我们不讲课，只创造。孩子们将认识他们的第一位AI新朋友，亲手将脑海中的奇思妙想，变成一张全世界独一无二的魔法作品，体验从0到1的创造乐趣与成就感。
          </p>
        </div>
      </div>

      <!-- 板块三：收获能力 -->
      <div class="benefits-section">
        <div class="section-card">
          <h2 class="section-title">在这堂课，孩子将收获</h2>

          <div class="abilities-grid">
            <div class="ability-item">
              <div class="ability-icon">🤖</div>
              <h4 class="ability-title">AI启蒙与数字素养</h4>
              <p class="ability-desc">通过"学习小怪兽"等生动比喻，让孩子正确认识AI，知道它是我们强大的工具和伙伴。</p>
            </div>

            <div class="ability-item">
              <div class="ability-icon">💡</div>
              <h4 class="ability-title">想象力与创意表达</h4>
              <p class="ability-desc">引导孩子将内心世界画出来，并学习用语言（魔法咒语）指挥AI，将创意变为现实。</p>
            </div>

            <div class="ability-item">
              <div class="ability-icon">🎤</div>
              <h4 class="ability-title">自信心与公众表达</h4>
              <p class="ability-desc">在分享环节中，鼓励孩子勇敢表达自己的发现和梦想，每一次发言都是一次自信的绽放。</p>
            </div>

            <div class="ability-item">
              <div class="ability-icon">✨</div>
              <h4 class="ability-title">从0到1的创造乐趣</h4>
              <p class="ability-desc">亲历一个想法从诞生到最终成为精美作品的全过程，种下一颗"我能行"的自信种子。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块四：作品展示 -->
      <div class="showcase-section">
        <div class="section-card">
          <h2 class="section-title">每个孩子都是小艺术家 (作品展示)</h2>
          <p class="showcase-intro">每一幅作品，都是孩子内心世界的一次独白。AI帮助他们将这份纯真与想象，凝固成可以珍藏的瞬间。</p>

          <div class="showcase-grid">
            <div class="showcase-item">
              <img 
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/zuopin1.png"
                alt="孩子作品1"
                class="showcase-image"
              >
            </div>
            <div class="showcase-item">
              <img 
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/zuopin2.png"
                alt="孩子作品2"
                class="showcase-image"
              >
            </div>
          </div>

          <div class="showcase-videos">
            <div class="video-item">
              <video 
                controls 
                loop 
                playsinline
                poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/zuopinfengmian1.png" 
                preload="metadata"
                class="showcase-video"
              >
                <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/XingTuAI/kepuhuodong/zuopin1.mp4" type="video/mp4">
                您的浏览器不支持视频播放。
              </video>
            </div>
            <div class="video-item">
              <video 
                controls 
                loop 
                playsinline 
                poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/zuopinfengmian2.png" 
                preload="metadata"
                class="showcase-video"
              >
                <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/XingTuAI/kepuhuodong/zuopin2.mp4" type="video/mp4">
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块五：课堂精彩瞬间 -->
      <div class="moments-section">
        <div class="section-card">
          <h2 class="section-title">课堂精彩瞬间 (往期活动)</h2>

          <!-- 上饶师范学院 -->
          <div class="venue-block">
            <h3 class="venue-title">上饶师范学院AI课堂：</h3>
            <el-carousel 
              :interval="carouselSettings.interval" 
              :arrow="carouselSettings.arrow" 
              :type="carouselSettings.type" 
              :height="carouselSettings.height" 
              class="moments-carousel"
            >
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/shiyuan1.jpg"
                    alt="上饶师范学院课堂1"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/shiyuan2.jpg"
                    alt="上饶师范学院课堂2"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/shiyuan3.png"
                    alt="上饶师范学院课堂3"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>

          <!-- 帆书AI课堂 -->
          <div class="venue-block">
            <h3 class="venue-title">帆书AI课堂：</h3>
            <el-carousel 
              :interval="carouselSettings.interval" 
              :arrow="carouselSettings.arrow" 
              :type="carouselSettings.type" 
              :height="carouselSettings.height" 
              class="moments-carousel"
            >
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/fandeng1.JPG"
                    alt="帆书AI课堂1"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/fandeng2.JPG"
                    alt="帆书AI课堂2"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/fandeng3.JPG"
                    alt="帆书AI课堂3"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>

          <!-- 汉氏联合AI课堂 -->
          <div class="venue-block">
            <h3 class="venue-title">汉氏联合AI课堂：</h3>
            <el-carousel 
              :interval="carouselSettings.interval" 
              :arrow="carouselSettings.arrow" 
              :type="carouselSettings.type" 
              :height="carouselSettings.height" 
              class="moments-carousel"
            >
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/hanshi1.JPG"
                    alt="汉氏联合AI课堂1"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/hanshi2.JPG"
                    alt="汉氏联合AI课堂2"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/hanshi3.JPG"
                    alt="汉氏联合AI课堂3"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>

      <!-- 板块六：立即开启体验 -->
      <div class="cta-section">
        <div class="section-card">
          <h2 class="section-title">下一场魔法，等你加入！</h2>

          <div class="cta-content">
            <p class="cta-text" style="text-indent: 2em;">
              想让您的孩子也来体验这场充满创造与惊喜的AI魔法之旅吗？扫码添加我们的官方微信，获取最新的活动排期，并为孩子预约一个宝贵的魔法席位吧！
            </p>
            <div class="qr-code">
              <img
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/erweima.jpg"
                alt="官方微信二维码"
                class="qr-image"
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 页面顶部样式 */
.hero-section {
  text-align: center;
  padding: 2rem 0;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  border-radius: 1rem;
  margin-bottom: 1.5rem;
}

.main-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(45deg, #ffffff, #e3f2fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  opacity: 0.95;
  font-weight: 400;
}

/* 通用卡片样式 */
.section-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(49, 106, 188, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #316abc;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

/* 介绍文本样式 */
.intro-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #4a5568;
  margin: 0;
  text-align: justify;
}

/* 能力收获样式 */
.abilities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.ability-item {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ebf8ff, #bee3f8);
  border-radius: 1rem;
  border: 2px solid #7dbbee;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.ability-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(56, 161, 105, 0.2);
}

.ability-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.ability-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c5282;
  margin: 0 0 0.75rem 0;
}

.ability-desc {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #2c5282;
  margin: 0;
}

/* 作品展示样式 */
.showcase-intro {
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a5568;
  margin: 0 0 2rem 0;
}

.showcase-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.showcase-item {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.showcase-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.showcase-image {
  width: 100%;
  height: auto;
  display: block;
}

.showcase-videos {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.video-item {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.showcase-video {
  width: 100%;
  height: auto;
  display: block;
}

/* 课堂精彩瞬间样式 */
.venue-block {
  margin-bottom: 2rem;
}

.venue-block:last-child {
  margin-bottom: 0;
}

.venue-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #316abc;
  margin: 0 0 1rem 0;
}

.moments-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.moment-item {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.moment-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.moment-image {
  width: 100%;
  height: auto;
  display: block;
}

/* 轮播图样式 */
.moments-carousel {
  margin-top: 20px;
  border-radius: 12px;
  overflow: hidden;
}

.carousel-item {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 12px;
  overflow: hidden;
}

.carousel-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* CTA部分样式 */
.cta-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.cta-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a5568;
  margin: 0;
  text-align: center;
}

.qr-code {
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

.qr-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式样式 */
@media (min-width: 768px) {
  .abilities-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .showcase-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .showcase-videos {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .moments-grid {
    gap: 1.5rem;
  }
  
  .cta-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .cta-text {
    flex: 1;
    text-align: left;
    padding-right: 2rem;
  }
  
  .qr-code {
    margin: 0;
  }
}

@media (max-width: 767px) {
  .moments-grid {
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;
  }
  
  .moment-item {
    margin-bottom: 1rem;
  }
  
  .showcase-grid,
  .showcase-videos {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .video-item {
    margin-bottom: 1rem;
  }
  
  /* 移动端轮播图样式调整 */
  .moments-carousel {
    margin-top: 15px;
    border-radius: 8px;
  }
  
  .carousel-item {
    height: 200px; /* 控制移动端轮播图高度 */
  }
  
  .carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 改为cover确保图片填满容器 */
  }
}
@media (max-width: 768px) {
  .back-button {
    display: none;
  }
}
</style>