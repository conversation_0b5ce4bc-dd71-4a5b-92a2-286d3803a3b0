<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 产品优势
const productAdvantages = reactive([
  {
    title: '净化效率高、运行稳定',
    description: '采用活性炭吸附和UV光氧催化双重技术，净化效率高，系统运行稳定可靠。'
  },
  {
    title: '结构紧凑、新颖、体积小、重量轻',
    description: 'UV灯管紫外线结构设计，设备结构紧凑新颖，体积小重量轻，安装方便。'
  },
  {
    title: '噪声小于45db(A)、风阻小于300Pa',
    description: '低噪音运行，风阻小，对现有通风系统影响最小，运行环境友好。'
  },
  {
    title: '运行成本低、耗电功率小',
    description: '设备能耗低，运行成本低，经济实用，为企业节约运营费用。'
  },
  {
    title: '维护方便、使用寿命长',
    description: '设备维护简单方便，使用寿命长，降低维护成本和频次。'
  },
  {
    title: '安全可靠，价格合理',
    description: '设备安全可靠，性价比高，价格合理，是企业废气治理的理想选择。'
  }
])

// 性能特点
const performanceFeatures = reactive([
  {
    title: '高效除恶臭',
    description: '能高效去除挥发性有机物(VOC)、无机物、硫化氢、氨气、硫醇类等主要污染物，以及各种恶臭味，脱臭效率最高可达99%以上，脱臭效果大大超过国家1993年颁布的恶臭污染物排放标准(GB14554-93)。'
  },
  {
    title: '无需添加任何物质',
    description: '只需要设置相应的排风管道和排风动力，使恶臭气体通过本设备进行脱臭分解净化，无需添加任何物质参与化学反应。'
  },
  {
    title: '适应性强',
    description: '可适应高浓度，大气量，不同恶臭气体物质的脱臭净化处理，可每天24小时连续工作，运行稳定可靠。'
  },
  {
    title: '运行成本低',
    description: '本设备无任何机械动作，无噪音，无需专人管理和日常维护，只需作定期检查，本设备能耗低，(每处理1000立方米/小时，仅耗电约0.2度电能)。设备风阻极低<50pa，可节约大量排风动力能耗。'
  },
  {
    title: '无需预处理',
    description: '恶臭气体无需进行特殊的预处理，如加温、加湿等，设备工作环境温度在-30℃-95℃之间，湿度在30%-98%、PH值在2-13之间均可正常工作。'
  },
  {
    title: '设备占地面积小，自重轻',
    description: '适合于布置紧凑、场地狭小等特殊条件，设备占地面积<1平方米/处理10000m³/h风量。'
  },
  {
    title: '优质进口材料制造',
    description: '防火、防腐蚀性能高，性能稳定，使用寿命长。'
  }
])

// 应用范围
const applications = reactive([
  {
    title: '炼油厂',
    description: '炼油过程中产生的各种有机废气和恶臭气体的净化处理。'
  },
  {
    title: '橡胶厂',
    description: '橡胶生产过程中产生的有机溶剂废气和恶臭气体处理。'
  },
  {
    title: '化工厂',
    description: '化工生产过程中各种有机废气、恶臭气体的净化处理。'
  },
  {
    title: '制药厂',
    description: '制药生产过程中产生的有机溶剂废气和异味气体处理。'
  },
  {
    title: '污水处理厂',
    description: '污水处理过程中产生的硫化氢、氨气等恶臭气体处理。'
  },
  {
    title: '垃圾转运站',
    description: '垃圾处理过程中产生的各种恶臭气体的脱臭净化处理。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>活性炭-UV光氧废气净化器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/975d1350-fb5f-42a1-8052-dedaf485ccc5.jpg" alt="活性炭-UV光氧废气净化器" />
        </div>
        <div class="hero-content">
          <h2>活性炭-UV光氧废气净化器</h2>
          <p class="product-subtitle">结合活性炭吸附和UV光氧技术的复合式废气净化设备</p>
          <div class="product-intro">
            <p>活性炭-UV光氧废气净化器采用活性炭吸附和UV光氧催化相结合的技术，能够高效处理各种有机废气。活性炭负责吸附，UV光氧负责分解，双重净化确保废气达标排放。</p>
            <p>该设备特别适用于处理低浓度大风量的有机废气，通过活性炭的物理吸附和UV光氧的化学分解，实现对废气的深度净化，脱臭效率可达99%以上。</p>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in productAdvantages"
            :key="index"
            class="advantage-card"
          >
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

      <!-- 性能特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          性能特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in performanceFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 应用范围 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用范围
        </h3>
        <div class="applications">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🔍</div>
            <div class="indicator-content">
              <h4>脱臭效率</h4>
              <div class="indicator-value">≥99%</div>
              <p>超国家标准</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔇</div>
            <div class="indicator-content">
              <h4>噪声水平</h4>
              <div class="indicator-value">≤45dB(A)</div>
              <p>低噪音运行</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">💨</div>
            <div class="indicator-content">
              <h4>风阻</h4>
              <div class="indicator-value">≤300Pa</div>
              <p>低阻力设计</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">⚡</div>
            <div class="indicator-content">
              <h4>能耗</h4>
              <div class="indicator-value">0.2度/1000m³</div>
              <p>超低能耗</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🌡️</div>
            <div class="indicator-content">
              <h4>工作温度</h4>
              <div class="indicator-value">-30℃~95℃</div>
              <p>宽温度范围</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">💧</div>
            <div class="indicator-content">
              <h4>湿度范围</h4>
              <div class="indicator-value">30%~98%</div>
              <p>适应性强</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <div class="principle-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>废气收集</h4>
              <p>恶臭气体通过收集系统进入净化器，无需特殊预处理，适应各种工况条件。</p>
            </div>
          </div>
          <div class="principle-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>活性炭吸附</h4>
              <p>废气首先通过活性炭层，利用活性炭的多孔结构和大比表面积吸附有机污染物。</p>
            </div>
          </div>
          <div class="principle-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>UV光氧催化</h4>
              <p>经过活性炭预处理的废气进入UV光氧区，在紫外线照射下产生臭氧和羟基自由基。</p>
            </div>
          </div>
          <div class="principle-step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4>光解氧化</h4>
              <p>臭氧和羟基自由基与有机污染物发生氧化反应，将其分解为CO2和H2O等无害物质。</p>
            </div>
          </div>
          <div class="principle-step">
            <div class="step-number">5</div>
            <div class="step-content">
              <h4>深度净化</h4>
              <p>双重净化技术确保废气得到深度处理，脱臭效率达到99%以上。</p>
            </div>
          </div>
          <div class="principle-step">
            <div class="step-number">6</div>
            <div class="step-content">
              <h4>达标排放</h4>
              <p>经过双重净化的洁净气体达标排放，满足环保要求。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 技术特色 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🌟</span>
          技术特色
        </h3>
        <div class="tech-features">
          <div class="tech-item">
            <div class="tech-icon">🔬</div>
            <div class="tech-content">
              <h4>双重净化技术</h4>
              <p>活性炭物理吸附与UV光氧化学分解相结合，实现对废气的深度净化，处理效果显著优于单一技术。</p>
            </div>
          </div>
          <div class="tech-item">
            <div class="tech-icon">💡</div>
            <div class="tech-content">
              <h4>UV灯管紫外线技术</h4>
              <p>采用高效UV灯管，产生185nm和254nm双波长紫外线，激发空气中的氧分子产生臭氧，增强氧化能力。</p>
            </div>
          </div>
          <div class="tech-item">
            <div class="tech-icon">🌿</div>
            <div class="tech-content">
              <h4>高效活性炭吸附</h4>
              <p>采用优质活性炭，比表面积大，吸附能力强，能够有效去除各种有机污染物和异味分子。</p>
            </div>
          </div>
          <div class="tech-item">
            <div class="tech-icon">🔄</div>
            <div class="tech-content">
              <h4>连续运行设计</h4>
              <p>设备可24小时连续运行，无机械动作，无需专人管理，运行稳定可靠，维护成本低。</p>
            </div>
          </div>
          <div class="tech-item">
            <div class="tech-icon">🛡️</div>
            <div class="tech-content">
              <h4>防腐耐用材料</h4>
              <p>采用优质进口材料制造，防火防腐蚀性能高，适应恶劣工况，使用寿命长。</p>
            </div>
          </div>
          <div class="tech-item">
            <div class="tech-icon">📏</div>
            <div class="tech-content">
              <h4>紧凑型设计</h4>
              <p>设备结构紧凑，占地面积小，适合布置紧凑、场地狭小等特殊条件，安装灵活方便。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases">
         
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/12-ptfq/20221220160919.jpg" alt="活性炭-UV光氧废气净化器工程案例2" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/12-ptfq/202212201609191.jpg" alt="活性炭-UV光氧废气净化器工程案例3" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/12-ptfq/20221220160920.jpg" alt="活性炭-UV光氧废气净化器工程案例4" />
          </div> 
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/12-ptfq/202212201607021.jpg" alt="活性炭-UV光氧废气净化器工程案例1" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 产品优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.advantage-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.3;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.3;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 应用范围 */
.applications {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.application-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 工作原理 */
.working-principle {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.principle-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
  border-left: 4px solid #3b82f6;
}

.principle-step:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.step-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.step-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.step-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术特色 */
.tech-features {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.tech-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.tech-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tech-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tech-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.tech-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 工程案例样式 */
.project-cases {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.case-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.case-item img {
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-card {
    padding: 1.5rem;
  }

  .advantage-card h4 {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }

  .advantage-card p {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }

  .feature-card p {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .working-principle {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .principle-step {
    padding: 1.5rem;
  }

  .step-content h4 {
    font-size: 1.1rem;
  }

  .step-content p {
    font-size: 0.9rem;
  }

  .tech-features {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .tech-item {
    padding: 1.5rem;
  }

  .tech-content h4 {
    font-size: 1.1rem;
  }

  .tech-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .application-card {
    padding: 1.5rem;
  }

  .application-card h4 {
    font-size: 1.1rem;
  }

  .application-card p {
    font-size: 0.9rem;
  }
}

@media (min-width: 1024px) {
  .advantages-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .working-principle {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-features {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
