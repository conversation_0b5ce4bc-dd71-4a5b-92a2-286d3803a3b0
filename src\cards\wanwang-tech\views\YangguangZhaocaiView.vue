<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('YangguangZhaocaiView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>阳光招采平台</h1>
    </div>

    <div class="content">
      <!-- 项目介绍 -->
      <div class="section intro-section">
        <div class="intro-card">
          <div class="intro-visual">
            <div class="intro-image">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yangguangcaizhao.jpg" alt="阳光招采平台" />
            </div>
          </div>
          <div class="intro-content">
            <div class="intro-title">
              <h3>阳光招采平台</h3>
              <div class="subtitle">提高处置效率、跟踪工作进展</div>
              <div class="title-decoration"></div>
            </div>
            <div class="intro-description">
              <div class="description-item">
                <div class="item-icon">🏢</div>
                <p>万网招采平台是由江西万网科技开发的招标采购平台。过去十年，企业管理应用发生翻天覆地的变化，许多管理应用迁移至互联网，但是目前还没有一整套完整的招标采购平台。为此，公司研发了这个平台，主要解决传统招采方式需要耗费巨大的人力物力、采购决策信息不足、可供采购的厂商达不到货比三家的需要等缺点。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">⚡</div>
                <p>发挥信息化招采平台提升企业采购能力、发挥运营效率、节约采购成本、打破信息盲区、开展实时监督、防止采购腐败等方面的优点，助力企业更好更快发展。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">📈</div>
                <p>截至目前，本平台现已在多个地区开展业务为包括各大企事业单位提供各种采购方式在内的交易并持续创造价值。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统特点 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>系统特点</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🛡️</div>
            <h3>稳定可靠</h3>
            <div class="feature-content">
              <p>使用成熟技术和主流的前后端框架开发，使整个应用系统稳定可靠，确保平台长期稳定运行。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <h3>易用性强</h3>
            <div class="feature-content">
              <p>从用户体验出发，创建成熟的，易于使用的人机交互界面，降低用户学习成本。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📱</div>
            <h3>消息提醒</h3>
            <div class="feature-content">
              <p>接入公众号开发，关注公众号的企业能够实时收到招标公告信息，中标通知也能实时通知中标企业。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔄</div>
            <h3>方便推广</h3>
            <div class="feature-content">
              <p>朋友圈随意转发，便于信息传播和平台推广，扩大招标采购的参与范围。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目背景 -->
      <div class="section background-section">
        <div class="section-header">
          <h2>项目背景</h2>
        </div>
        <div class="background-grid">
          <div class="background-item">
            <div class="background-header">
              <div class="background-icon">💻</div>
              <h3>互联网+办公自动化流程</h3>
            </div>
            <div class="background-content">
              <p>过去十年，企业管理应用发生翻天覆地的变化，许多管理应用迁移至互联网，但是目前还没有一整套完整的招标采购平台。</p>
            </div>
          </div>
          <div class="background-item">
            <div class="background-header">
              <div class="background-icon">⚠️</div>
              <h3>传统招采方式缺点</h3>
            </div>
            <div class="background-content">
              <p>传统的招标采购流程需要耗费巨大的人力物力，在物资采购中还存在采购决策所依据的信息不足的现象，有时可供采购的厂商达不到货比三家的需要，与阳光采购、廉洁采购距离较远。</p>
            </div>
          </div>
          <div class="background-item">
            <div class="background-header">
              <div class="background-icon">🌟</div>
              <h3>信息化招采平台优势</h3>
            </div>
            <div class="background-content">
              <p>互联网+招采时代已经到来。电子招采平台在提升企业采购能力、发挥运营效率、节约采购成本、打破信息盲区、开展实时监督、防止采购腐败等方面均能发挥积极作用。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>平台优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">📈</div>
            <h3>提升采购能力</h3>
            <p>通过信息化手段，全面提升企业采购管理能力和专业化水平。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <h3>发挥运营效率</h3>
            <p>优化采购流程，提高运营效率，缩短采购周期，快速响应业务需求。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">💰</div>
            <h3>节约采购成本</h3>
            <p>通过公开透明的竞争机制，有效降低采购成本，提高资金使用效率。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔍</div>
            <h3>打破信息盲区</h3>
            <p>建立信息共享机制，消除信息不对称，为采购决策提供全面支撑。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">👁️</div>
            <h3>开展实时监督</h3>
            <p>全程留痕，实时监控，确保采购过程公开透明，可追溯可监督。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🛡️</div>
            <h3>防止采购腐败</h3>
            <p>通过制度化、规范化管理，有效防范采购过程中的腐败风险。</p>
          </div>
        </div>
      </div>

      <!-- 核心功能 -->
      <div class="section core-functions-section">
        <div class="section-header">
          <h2>核心功能</h2>
        </div>
        <div class="functions-grid">
          <div class="function-item">
            <div class="function-icon">📋</div>
            <h3>招标管理</h3>
            <div class="function-content">
              <p>完整的招标流程管理，从招标公告发布到开标评标，全程电子化操作。</p>
            </div>
          </div>
          <div class="function-item">
            <div class="function-icon">📝</div>
            <h3>投标管理</h3>
            <div class="function-content">
              <p>供应商在线投标，文件上传下载，投标过程便捷高效。</p>
            </div>
          </div>
          <div class="function-item">
            <div class="function-icon">⚖️</div>
            <h3>评标管理</h3>
            <div class="function-content">
              <p>专业的评标系统，支持多种评标方法，确保评标过程公正透明。</p>
            </div>
          </div>
          <div class="function-item">
            <div class="function-icon">📊</div>
            <h3>统计分析</h3>
            <div class="function-content">
              <p>全面的数据统计分析，为采购决策提供数据支撑和趋势分析。</p>
            </div>
          </div>
          <div class="function-item">
            <div class="function-icon">🔐</div>
            <h3>权限管理</h3>
            <div class="function-content">
              <p>多角色权限控制，确保不同用户只能访问相应的功能模块。</p>
            </div>
          </div>
          <div class="function-item">
            <div class="function-icon">📱</div>
            <h3>移动应用</h3>
            <div class="function-content">
              <p>支持移动端操作，随时随地参与招标采购活动，提高工作效率。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="value-content">
          <p>阳光招采平台通过信息化手段，彻底改变了传统招标采购的工作模式。平台实现了招标采购全流程的电子化、规范化管理，大大提高了工作效率，降低了运营成本。通过公开透明的竞争机制，不仅为采购方节约了成本，也为供应商提供了公平竞争的环境。平台的实时监督和全程留痕功能，有效防范了采购过程中的腐败风险，确保了采购活动的阳光透明。同时，平台的数据分析功能为采购决策提供了科学依据，助力企业实现采购管理的数字化转型，为企业的可持续发展提供了强有力的支撑。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 项目介绍样式 */
.intro-card {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(255, 193, 7, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ffc107 0%, #ff9800 100%);
}

.intro-visual {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
}

.intro-image {
  width: 100%;
  max-width: 600px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(255, 193, 7, 0.2);
  position: relative;
}

.intro-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.intro-content {
  text-align: center;
}

.intro-title {
  margin-bottom: 2rem;
}

.intro-title h3 {
  color: #ff9800;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.title-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #ffc107 0%, #ff9800 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-description {
  display: grid;
  gap: 1.5rem;
  text-align: left;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #ff9800;
}

.item-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.description-item p {
  margin: 0;
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
}

/* 系统特点样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.feature-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 项目背景样式 */
.background-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.background-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.background-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.background-header {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
}

.background-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.background-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.background-content {
  padding: 2rem;
}

.background-content p {
  color: #333;
  line-height: 1.6;
  margin: 0;
  text-align: justify;
}

/* 平台优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* 核心功能样式 */
.functions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.function-item {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.function-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.function-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.function-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.function-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 应用价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .intro-description {
    gap: 1rem;
  }

  .description-item {
    padding: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .background-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .functions-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .function-item {
    padding: 1.5rem;
  }

  .background-header {
    padding: 1rem 1.5rem;
  }

  .background-content {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }

  .intro-title h3 {
    font-size: 1.5rem;
  }
}
</style>
