import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'gesiUnionHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片'
    }
  },
  {
    path: '/association-intro',
    name: 'gesiUnionAssociationIntro',
    component: () => import('./views/AssociationIntroView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - 协会介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'gesiUnionAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - AI宣传员'
    }
  },
  {
    path: '/association-members',
    name: 'gesiUnionAssociationMembers',
    component: () => import('./views/AssociationMembersView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - 协会成员'
    }
  },
  {
    path: '/association-news',
    name: 'gesiUnionAssociationNews',
    component: () => import('./views/AssociationNewsView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - 协会动态'
    }
  },
  {
    path: '/product-center',
    name: 'gesiUnionProductCenter',
    component: () => import('./views/ProductCenterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - 产品中心'
    }
  },
  {
    path: '/product-center/yimatong',
    name: 'gesiUnionYimatong',
    component: () => import('./views/YimatongDetailView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - 一码通上饶'
    }
  },
  {
    path: '/product-center/xinzhouchun',
    name: 'gesiUnionXinzhouchun',
    component: () => import('./views/XinzhouchunDetailView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - 信州春大舞台'
    }
  }
]

export default routes
