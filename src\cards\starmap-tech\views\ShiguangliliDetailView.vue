<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.back()
}

// 轮播图设置
const carouselSettings = ref({
  interval: 3000,  // 自动切换的时间间隔，单位为毫秒
  arrow: 'always', // 始终显示箭头
  type: 'card',    // 卡片化
  height: '300px'  // 轮播图高度
})

// 页面加载时执行
onMounted(() => {
  // 设置页面标题
  document.title = 'AI时光礼物情感创作工坊'
  
  // 根据屏幕宽度动态设置轮播图高度和类型
  const updateCarouselSettings = () => {
    if (window.innerWidth <= 767) {
      carouselSettings.value.height = '200px'
      carouselSettings.value.type = '' // 移动端使用普通轮播
    } else {
      carouselSettings.value.height = '300px'
      carouselSettings.value.type = 'card' // PC端使用卡片式轮播
    }
  }
  
  // 初始设置
  updateCarouselSettings()
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateCarouselSettings)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>《AI时光礼物》情感创作工坊</h1>
    </div>

    <div class="content">
      <!-- 板块一：产品介绍 -->
      <div class="hero-section">
        <h2 class="main-title">一份献给爸爸妈妈的AI时光礼物</h2>
        <p class="subtitle">用AI的魔力，把孩子心中的爱变成看得见的礼物</p>
      </div>

      <!-- 板块二：课程介绍 -->
      <div class="intro-section">
        <div class="section-card">
          <p class="intro-text" style="text-indent: 2em;">
            这是一堂与众不同的创作工坊。我们不教绘画技巧，不讲复杂理论，只专注做一件有温度的小事：引导孩子将心中对父母那份最纯粹、却又羞于表达的爱，通过AI的魔力，变成一份可以看见、可以触摸、可以永久珍藏的惊喜礼物。
          </p>
        </div>
      </div>

      <!-- 板块三：收获能力 -->
      <div class="benefits-section">
        <div class="section-card">
          <h2 class="section-title">在这堂课，孩子将收获</h2>

          <div class="abilities-grid">
            <div class="ability-item">
              <div class="ability-icon">💬</div>
              <h4 class="ability-title">情感梳理与表达能力</h4>
              <p class="ability-desc">在AI伙伴的温柔引导下，学习梳理自己对父母的复杂情感，并勇敢地用语言表达出来。</p>
            </div>

            <div class="ability-item">
              <div class="ability-icon">🤖</div>
              <h4 class="ability-title">AI人机协作创造力</h4>
              <p class="ability-desc">亲身体验从口述心声、AI提炼、到生成专属艺术画作和动画的全过程，感受人机协作的无限可能。</p>
            </div>

            <div class="ability-item">
              <div class="ability-icon">❤️</div>
              <h4 class="ability-title">感恩教育与同理心</h4>
              <p class="ability-desc">通过"看见"父母的角色，更深刻地理解父母的付出，在心中种下一颗感恩与爱的种子。</p>
            </div>

            <div class="ability-item">
              <div class="ability-icon">🎁</div>
              <h4 class="ability-title">一份独一无二的"实体"爱意</h4>
              <p class="ability-desc">最终带回家的不仅是一次体验，更是一份凝聚了自己心声与创意的、沉甸甸的实体礼物卡片。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块四：爱的诞生之旅 -->
      <div class="journey-section">
        <div class="section-card">
          <h2 class="section-title">爱的诞生之旅 (活动流程速览)</h2>
          <p class="journey-intro">每一个环节，都是一次将爱具象化的奇妙旅程。</p>

          <div class="journey-timeline">
            <div class="journey-steps-container">
              <!-- 看见爱 -->  
              <div class="journey-step">
                <div class="step-icon-container">
                  <div class="step-icon">1</div>
                  <div class="step-connector"></div>
                </div>
                <div class="step-content">
                  <h3 class="step-title">看见爱</h3>
                  <div class="step-image-container">
                    <img 
                      src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganen4.JPG"
                      alt="看见爱"
                      class="step-image"
                    >
                  </div>
                </div>
              </div>

              <!-- 讲出爱 -->  
              <div class="journey-step">
                <div class="step-icon-container">
                  <div class="step-icon">2</div>
                  <div class="step-connector"></div>
                </div>
                <div class="step-content">
                  <h3 class="step-title">讲出爱</h3>
                  <div class="step-image-container">
                    <img 
                      src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganen5.JPG"
                      alt="讲出爱"
                      class="step-image"
                    >
                  </div>
                </div>
              </div>

              <!-- 画出爱 -->  
              <div class="journey-step">
                <div class="step-icon-container">
                  <div class="step-icon">3</div>
                  <div class="step-connector"></div>
                </div>
                <div class="step-content">
                  <h3 class="step-title">画出爱</h3>
                  <div class="step-image-container">
                    <img 
                      src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganen6.png"
                      alt="画出爱"
                      class="step-image"
                    >
                  </div>
                </div>
              </div>

              <!-- 送出爱 -->  
              <div class="journey-step">
                <div class="step-icon-container">
                  <div class="step-icon">4</div>
                </div>
                <div class="step-content">
                  <h3 class="step-title">送出爱</h3>
                  <div class="step-image-container">
                    <img 
                      src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganen7.jpg"
                      alt="送出爱"
                      class="step-image"
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块五：作品展示 -->
      <div class="showcase-section">
        <div class="section-card">
          <h2 class="section-title">每一份爱，都值得被看见 (作品展示)</h2>
          <p class="showcase-intro">我们珍藏了往期小小创作者们的心声与杰作。每一份礼物背后，都是一个温暖的故事。</p>

          <div class="showcase-grid">
            <div class="showcase-item">
              <img 
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganenzuopin1.png"
                alt="作品1"
                class="showcase-image"
              >
            </div>
            <div class="showcase-item">
              <img 
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganenzuopin2.png"
                alt="作品2"
                class="showcase-image"
              >
            </div>
          </div>

          <div class="showcase-videos">
            <div class="video-item">
              <video 
                controls 
                loop 
                playsinline
                poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganenzuopinfengmian1.png" 
                preload="metadata"
                class="showcase-video"
              >
                <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/XingTuAI/kepuhuodong/ganenzuopin1.mp4" type="video/mp4">
                您的浏览器不支持视频播放。
              </video>
            </div>
            <div class="video-item">
              <video 
                controls 
                loop 
                playsinline 
                poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganenzuopinfengmian2.png" 
                preload="metadata"
                class="showcase-video"
              >
                <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/XingTuAI/kepuhuodong/ganenzuopin2.mp4" type="video/mp4">
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块六：往期活动 -->
      <div class="moments-section">
        <div class="section-card">
          <h2 class="section-title">温暖的创作瞬间 (往期活动)</h2>

          <!-- 师大儿保课堂 -->
          <div class="venue-block">
            <h3 class="venue-title">师大儿保课堂：</h3>
            <el-carousel 
              :interval="carouselSettings.interval" 
              :arrow="carouselSettings.arrow" 
              :type="carouselSettings.type" 
              :height="carouselSettings.height" 
              class="moments-carousel"
            >
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganen1.jpg"
                    alt="师大儿保课堂1"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganen2.jpg"
                    alt="师大儿保课堂2"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="carousel-item">
                  <img 
                    src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganen3.JPG"
                    alt="师大儿保课堂3"
                    class="carousel-image"
                  >
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>

      <!-- 板块七：立即开启体验 -->
      <div class="cta-section">
        <div class="section-card">
          <h2 class="section-title">期待与您共创下一份感动</h2>

          <div class="cta-content">
            <p class="cta-text" style="text-indent: 2em;">
              这份独特的AI时光礼物，不仅是给父母的惊喜，更是孩子成长中一次宝贵的情感表达教育。想让您的孩子也来亲手创造这份感动吗？
            </p>
            <div class="qr-code">
              <img
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/erweima.jpg"
                alt="官方微信二维码"
                class="qr-image"
              >
            </div>
            <p class="cta-text" style="text-align: center; margin-top: 1rem;">
              立即扫码，添加官方微信，咨询最新一期"AI情感创作工坊"的活动安排！
            </p>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 页面顶部样式 */
.hero-section {
  text-align: center;
  padding: 2rem 0;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  border-radius: 1rem;
  margin-bottom: 1.5rem;
}

.main-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(45deg, #ffffff, #ebf8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  opacity: 0.95;
  font-weight: 400;
}

/* 通用卡片样式 */
.section-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(49, 106, 188, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #316abc;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

/* 介绍文本样式 */
.intro-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #4a5568;
  margin: 0;
  text-align: justify;
}

/* 能力收获样式 */
.abilities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.ability-item {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ebf8ff, #bee3f8);
  border-radius: 1rem;
  border: 2px solid #7dbbee;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.ability-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(56, 161, 105, 0.2);
}

.ability-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.ability-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c5282;
  margin: 0 0 0.75rem 0;
}

.ability-desc {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #2c5282;
  margin: 0;
}

/* 爱的诞生之旅样式 */
.journey-intro {
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a5568;
  margin: 0 0 2rem 0;
}

.journey-timeline {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.journey-steps-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.journey-step {
  display: flex;
  gap: 1.5rem;
  position: relative;
}

.step-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-icon {
  width: 3.5rem;
  height: 3.5rem;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 15px rgba(49, 106, 188, 0.3);
  border: 3px solid white;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.step-connector {
  width: 3px;
  background: linear-gradient(to bottom, #316abc, #7dbbee);
  flex-grow: 1;
  margin-top: 0.5rem;
  position: relative;
  z-index: 1;
}

.journey-step:last-child .step-connector {
  display: none;
}

.step-content {
  flex: 1;
  background-color: white;
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(49, 106, 188, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.step-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.journey-step:hover .step-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(49, 106, 188, 0.4);
}

.step-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #316abc;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(49, 106, 188, 0.2);
}

.step-image-container {
  border-radius: 0.75rem;
  overflow: hidden;
  flex-grow: 1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: 300px;
  display: flex;
  align-items: center;
}

.step-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

/* 作品展示样式 */
.showcase-intro {
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a5568;
  margin: 0 0 2rem 0;
}

.showcase-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.showcase-item {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.showcase-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.showcase-image {
  width: 100%;
  height: auto;
  display: block;
}

.showcase-videos {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.video-item {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.showcase-video {
  width: 100%;
  height: auto;
  display: block;
}

/* 课堂精彩瞬间样式 */
.venue-block {
  margin-bottom: 2rem;
}

.venue-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #316abc;
  margin: 0 0 1rem 0;
}

.moments-carousel {
  margin-top: 1rem;
  border-radius: 0.75rem;
  overflow: hidden;
}

.carousel-item {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.carousel-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* CTA区域样式 */
.cta-content {
  text-align: center;
}

.cta-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #4a5568;
  margin: 0 0 1.5rem 0;
}

.qr-code {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid #7dbbee;
  padding: 0.5rem;
  background-color: white;
}

.qr-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  .section-card {
    padding: 1rem;
    border-radius: 0.75rem;
  }
  
  .hero-section {
    padding: 1.5rem 0;
    border-radius: 0.75rem;
  }
  
  .abilities-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .ability-item {
    padding: 0.75rem;
  }

  .journey-steps-container {
    gap: 1.5rem;
  }
  
  .journey-step {
    flex-direction: row;
    gap: 1rem;
  }
  
  .step-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.2rem;
  }
  
  .step-connector {
    width: 2px;
  }
  
  .step-content {
    padding: 1rem;
  }
  
  .step-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
  }
  
  .step-image-container {
    height: 200px;
  }
  
  .step-image-container {
    height: 150px;
  }
  
  .showcase-grid {
    grid-template-columns: 1fr;
  }
  
  .showcase-videos {
    grid-template-columns: 1fr;
  }
  
  .timeline-text {
    position: relative;
    left: 30px;
    top: 0;
    transform: none;
    text-align: left;
    z-index: 10;
    font-size: 1rem;
    font-weight: 700;
    color: #316abc;
    background-color: white;
    padding: 3px 10px;
    border-radius: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(49, 106, 188, 0.2);
    display: inline-block;
    margin-bottom: 5px;
  }
  
  .step-content {
    padding: 0.5rem;
    margin-left: 30px;
    border-radius: 0.75rem;
    width: calc(100% - 35px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  }
  
  .step-image-container {
    height: 200px;
    overflow: hidden;
    border-radius: 0.5rem;
  }
  
  .step-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .showcase-grid,
  .showcase-videos {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }
  
  .showcase-item,
  .video-item {
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
  
  .showcase-image,
  .showcase-video {
    aspect-ratio: 1/1;
    object-fit: cover;
  }
  
  .showcase-intro,
  .journey-intro {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .main-title {
    font-size: 1.4rem;
  }

  .subtitle {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .section-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
  }
  
  .venue-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .moments-carousel {
    margin-top: 0.5rem;
    border-radius: 0.5rem;
  }

  .carousel-item {
    height: 180px;
  }
  
  .venue-block {
    margin-bottom: 1rem;
  }
  
  .qr-code {
    width: 150px;
    height: 150px;
  }
  
  .cta-text {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .back-button {
    font-size: 1rem;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .abilities-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .journey-steps-container {
    flex-wrap: wrap;
  }
  
  .journey-step {
    flex-basis: calc(50% - 0.5rem);
    flex-grow: 0;
    margin-bottom: 2rem;
  }
  
  
  
  
}

@media (min-width: 1024px) {
  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
</style>