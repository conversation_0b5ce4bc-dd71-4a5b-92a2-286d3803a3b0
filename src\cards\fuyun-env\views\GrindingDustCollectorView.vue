<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { 
    model: 'FDM-1500', 
    airflow: '1500', 
    power: '1.5', 
    voltage: '220', 
    filterArea: '16', 
    filterPrecision: '0.3', 
    dimensions: '1000×800×1200'
  },
  { 
    model: 'FDM-2700', 
    airflow: '2700', 
    power: '2.2', 
    voltage: '380', 
    filterArea: '32', 
    filterPrecision: '0.3', 
    dimensions: '1200×1000×1700'
  },
  { 
    model: 'FDM-3000', 
    airflow: '3000', 
    power: '3', 
    voltage: '380', 
    filterArea: '32', 
    filterPrecision: '0.3', 
    dimensions: '1500×1200×1700'
  },
  { 
    model: 'FDM-3600', 
    airflow: '3600', 
    power: '4', 
    voltage: '380', 
    filterArea: '46', 
    filterPrecision: '0.3', 
    dimensions: '2000×1200×1700'
  }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '高效过滤',
    description: '采用高效滤筒，对0.3μm以上粉尘过滤效率达99.9%以上，有效净化打磨粉尘。',
    icon: '🌪️'
  },
  {
    title: '智能控制',
    description: '配备智能控制系统，可根据工况自动调节风量，实现节能运行。',
    icon: '🧠'
  },
  {
    title: '低噪音运行',
    description: '采用低噪音风机和消音设计，运行噪音≤75dB，不影响工作环境。',
    icon: '🔇'
  },
  {
    title: '结构紧凑',
    description: '设备结构紧凑，占地面积小，安装维护方便，适合各种工作场所。',
    icon: '📦'
  },
  {
    title: '自动清灰',
    description: '配备脉冲清灰系统，自动清理滤筒表面粉尘，延长滤筒使用寿命。',
    icon: '🔄'
  },
  {
    title: '安全可靠',
    description: '设备运行稳定可靠，配备多重安全保护装置，确保使用安全。',
    icon: '🛡️'
  }
])

// 应用场景
const applications = reactive([
  {
    title: '金属打磨',
    description: '各种金属制品的打磨、抛光作业粉尘处理',
    icon: '⚙️'
  },
  {
    title: '木工加工',
    description: '木材切割、打磨、雕刻等作业粉尘收集',
    icon: '🪵'
  },
  {
    title: '石材加工',
    description: '石材切割、打磨、雕刻产生的粉尘处理',
    icon: '🪨'
  },
  {
    title: '焊接作业',
    description: '焊接过程中产生的烟尘和有害气体净化',
    icon: '🔥'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>打磨除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/damochuchen.png" alt="打磨除尘器" />
        </div>
        <div class="hero-content">
          <h2>打磨除尘器</h2>
          <p class="product-subtitle">专为打磨作业设计的高效除尘设备</p>
          <div class="product-intro">
            <p>打磨除尘器是专门针对各种打磨、抛光、切割等作业过程中产生的粉尘而设计的除尘设备。</p>
            <p>采用先进的滤筒过滤技术和脉冲清灰系统，能够高效收集和过滤各种金属、木材、石材等打磨粉尘，保护操作人员健康，改善工作环境。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>打磨除尘器通过强力风机产生负压，将打磨作业过程中产生的粉尘吸入除尘器内部。</p>
          <p>粉尘经过滤筒进行高效过滤，清洁空气经风机排出，收集的粉尘通过脉冲清灰系统定期清理，确保设备持续高效运行。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>风量<br/>(m³/h)</th>
                <th>功率<br/>(kW)</th>
                <th>电压<br/>(V)</th>
                <th>过滤面积<br/>(m²)</th>
                <th>过滤精度<br/>(μm)</th>
                <th>外形尺寸<br/>(L×W×H mm)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.power }}</td>
                <td>{{ spec.voltage }}</td>
                <td>{{ spec.filterArea }}</td>
                <td>{{ spec.filterPrecision }}</td>
                <td class="dimensions-cell">{{ spec.dimensions }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications-grid">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <div class="app-icon">{{ app.icon }}</div>
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases">
         
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/8-dmfc/202212121352441.jpg" alt="打磨除尘器工程案例2" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/8-dmfc/202212121352443.jpg" alt="打磨除尘器工程案例3" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/8-dmfc/202212121352444.jpg" alt="打磨除尘器工程案例4" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品展示区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 产品特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.feature-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8rem;
}

.specs-table th {
  background: #f8fafc;
  padding: 0.75rem 0.5rem;
  text-align: center;
  font-weight: 600;
  color: #1e3470;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
  font-size: 0.75rem;
}

.specs-table td {
  padding: 0.75rem 0.5rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  white-space: nowrap;
  font-size: 0.8rem;
}

.specs-table tbody tr:hover {
  background: #f8fafc;
}

.dimensions-cell {
  font-size: 0.7rem;
}

/* 应用场景网格 */
.applications-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 工程案例样式 */
.project-cases {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.case-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* 第三张图片占据两列 */
.case-item:nth-child(3) {
  grid-column: 1 / -1; /* 占据整行 */
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.case-item img {
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .specs-table {
    font-size: 0.85rem;
  }

  .specs-table th {
    font-size: 0.8rem;
    padding: 0.75rem;
  }

  .specs-table td {
    font-size: 0.85rem;
    padding: 0.75rem;
  }

  .dimensions-cell {
    font-size: 0.8rem;
  }

  .applications-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .specs-table {
    font-size: 0.9rem;
  }

  .specs-table th {
    font-size: 0.85rem;
  }

  .specs-table td {
    font-size: 0.9rem;
  }

  .dimensions-cell {
    font-size: 0.85rem;
  }
}

/* 移动端工程案例样式 */
@media (max-width: 767px) {
  .project-cases {
    grid-template-columns: 1fr; /* 移动端单列布局 */
  }
  
  .case-item:nth-child(3) {
    grid-column: 1; /* 移动端重置为单列 */
  }
}
</style>
