<script setup lang="ts">
// 数字人组件
</script>

<template>
  <div class="digital-human">
    <img 
      src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/szr.png" 
      alt="福运环保数字人" 
      class="digital-human-image mobile-image" 
    />
    <img 
      src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/szr-pc.jpeg" 
      alt="福运环保数字人" 
      class="digital-human-image desktop-image" 
    />
  </div>
</template>

<style scoped>
.digital-human {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.digital-human-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.desktop-image {
  display: none;
}

.mobile-image {
  display: block;
}

@media (min-width: 768px) {
  .desktop-image {
    display: block;
  }
  
  .mobile-image {
    display: none;
  }
  
  .digital-human-image {
    width: auto;
    height: 100%;
    max-width: none;
  }
}
</style>
