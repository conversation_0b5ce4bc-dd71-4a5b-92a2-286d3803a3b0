<template>
  <div class="company-intro-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>企业介绍</h1>
    </div>

    <!-- 企业介绍内容 -->
    <div class="intro-content">
      <!-- 宣传标语 -->
      <div class="slogan-banner">
        <h1 class="main-slogan">细胞承载希望，科技实现梦想</h1>
      </div>

      <!-- 宣传视频 -->
      <div class="video-section">
        <video 
          controls 
          class="promo-video"
          poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png"
        >
          <source src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/szrpc.mp4" type="video/mp4">
          您的浏览器不支持视频播放
        </video>
      </div>

      <!-- 集团简介 -->
      <div class="intro-section">
        <h2><el-icon><OfficeBuilding /></el-icon> 集团简介</h2>
        <div class="section-content">
          <p class="intro-text">
            汉氏联合，一家以细胞治疗与再生医学技术为核心的国家高新技术企业。作为中国干细胞领域的先行者与标准制定者，我们已构建从细胞存储、新药研发到临床应用的完整产业链。由韩忠朝院士领衔，致力于用细胞科技守护人类健康。
          </p>
        </div>
      </div>

      <!-- 核心优势 -->
      <div class="features-section">
        <h2><el-icon><Star /></el-icon> 核心优势</h2>
        <div class="features-grid">
          <div class="feature-item">
            <el-icon class="feature-icon"><Connection /></el-icon>
            <h3>全产业链布局</h3>
            <p>从细胞存储到临床应用，全程覆盖，品质可控</p>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Trophy /></el-icon>
            <h3>技术引领者</h3>
            <p>参与制定多项国家行业标准，引领细胞技术革命</p>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Cpu /></el-icon>
            <h3>雄厚研发实力</h3>
            <p>拥有3个院士工作站，荣获多项国家级科技大奖</p>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Location /></el-icon>
            <h3>全球化视野</h3>
            <p>全国布局7大细胞库，并设立法国欧洲研发中心</p>
          </div>
        </div>
      </div>

      <!-- 产业布局与社会责任 -->
      <div class="industry-section">
        <h2><el-icon><SetUp /></el-icon> 产业布局与社会责任</h2>
        <div class="industry-grid">
          <div class="industry-item">
            <el-icon class="industry-icon"><OfficeBuilding /></el-icon>
            <div class="industry-content">
              <h3>打造产业地标</h3>
              <p>倾力打造上饶"国际细胞谷"，构建细胞科技CBD</p>
            </div>
          </div>
          <div class="industry-item">
            <el-icon class="industry-icon"><School /></el-icon>
            <div class="industry-content">
              <h3>赋能未来教育</h3>
              <p>建立省级科普研学基地，激发青少年科学兴趣</p>
            </div>
          </div>
          <div class="industry-item">
            <el-icon class="industry-icon"><FirstAidKit /></el-icon>
            <div class="industry-content">
              <h3>推动健康中国</h3>
              <p>构建线上线下医疗服务网络，助力健康中国战略</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 发展历程 -->
      <div class="timeline-section">
        <h2><el-icon><Timer /></el-icon> 发展历程</h2>
        <div class="timeline-zigzag">
          <!-- 左侧时间节点 -->
          <div class="timeline-item left">
            <div class="timeline-year">2023</div>
            <div class="timeline-content">
              <p>累计7款新药获批临床；启动京南生命健康谷项目</p>
            </div>
          </div>
          <!-- 右侧时间节点 -->
          <div class="timeline-item right">
            <div class="timeline-year">2021</div>
            <div class="timeline-content">
              <p>集团分立四大板块；汉氏联合医院获批执业</p>
            </div>
          </div>
          <!-- 左侧时间节点 -->
          <div class="timeline-item left">
            <div class="timeline-year">2019</div>
            <div class="timeline-content">
              <p>首款1类新药获批临床；体检中心、检验室获批执业</p>
            </div>
          </div>
          <!-- 右侧时间节点 -->
          <div class="timeline-item right">
            <div class="timeline-year">2016</div>
            <div class="timeline-content">
              <p>投资建设上饶国际细胞谷，列为省级重点项目</p>
            </div>
          </div>
          <!-- 左侧时间节点 -->
          <div class="timeline-item left">
            <div class="timeline-year">2014</div>
            <div class="timeline-content">
              <p>荣获国家科技进步一等奖</p>
            </div>
          </div>
          <!-- 右侧时间节点 -->
          <div class="timeline-item right">
            <div class="timeline-year">2008</div>
            <div class="timeline-content">
              <p>创建全球首家胎盘干细胞库</p>
            </div>
          </div>
          <!-- 左侧时间节点 -->
          <div class="timeline-item left">
            <div class="timeline-year">2000</div>
            <div class="timeline-content">
              <p>创建中国首批脐带血造血干细胞库</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 荣誉资质 -->
      <div class="honor-section">
        <h2><el-icon><Medal /></el-icon> 荣誉资质</h2>
        <div class="honor-content">
          <img
            src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/rongyuzizhi.png"
            alt="汉氏联合荣誉资质"
            class="honor-image"
            @click="openImageViewer"
          >
        </div>
      </div>

      <!-- 图片查看器 - 全屏显示 -->
      <div v-if="showImageViewer" class="image-viewer-overlay" @click="closeImageViewer">
        <el-icon class="close-btn-fullscreen" @click="closeImageViewer">
          <Close />
        </el-icon>
        <img
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/rongyuzizhi.png"
          alt="汉氏联合荣誉资质"
          class="viewer-image-fullscreen"
          @click.stop
        >
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>

  
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  Star,
  Trophy,
  Medal,
  OfficeBuilding,
  Connection,
  Cpu,
  Location,
  SetUp,
  School,
  FirstAidKit,
  Timer,
  Close
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 图片查看器状态
const showImageViewer = ref(false)

// 返回上一页
const goBack = () => {
  router.back()
}

// 打开图片查看器
const openImageViewer = () => {
  showImageViewer.value = true
  // 防止背景滚动
  document.body.style.overflow = 'hidden'
}

// 关闭图片查看器
const closeImageViewer = () => {
  showImageViewer.value = false
  // 恢复背景滚动
  document.body.style.overflow = 'auto'
}

</script>

<style scoped>
/* 页面容器 */
.company-intro-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 内容区域 */
.intro-content {
  flex: 1;
  padding: 50px 16px 80px; /* 减少顶部内边距 */
  margin: 0 auto;
  width: 100%;
  max-width: 1200px;
  box-sizing: border-box;
}

/* 宣传标语 */
.slogan-banner {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  padding: 20px 16px; /* 减少内边距 */
  border-radius: 12px;
  margin-top: 10px; /* 减少上边距 */
  margin-bottom: 20px; /* 减少下边距 */
  text-align: center;
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3);
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-slogan {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 3px;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
  line-height: 1.4;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 视频部分 */
.video-section {
  margin-bottom: 32px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.promo-video {
  width: 100%;
  display: block;
  background-color: #000;
}

/* 各部分通用样式 */
.intro-section,
.features-section,
.industry-section,
.timeline-section,
.honor-section {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

h2 {
  color: #0f9da8;
  font-size: 22px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(15, 157, 168, 0.1);
}

h2 .el-icon {
  font-size: 22px;
}

/* 集团简介 */
.section-content {
  color: #333;
  line-height: 1.8;
  font-size: 15px;
  letter-spacing: 0.3px;
}

.intro-text {
  margin: 0;
  text-align: justify;
  text-indent: 2em;
}

/* 核心优势 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 默认为移动端设置两列布局 */
  gap: 16px;
}

.feature-item {
  background-color: #f0f9ff;
  border-radius: 8px;
  padding: 16px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(15, 157, 168, 0.15);
}

.feature-icon {
  font-size: 28px;
  color: #0f9da8;
  margin-bottom: 12px;
}

/* 移动端核心优势样式调整 */
@media (max-width: 767px) {
  .feature-item {
    padding: 12px; /* 减小内边距 */
  }
  
  .feature-icon {
    font-size: 24px; /* 减小图标大小 */
    margin-bottom: 8px;
  }
  
  .feature-item h3 {
    font-size: 15px; /* 减小标题字体大小 */
    margin-bottom: 4px;
  }
  
  .feature-item p {
    font-size: 13px; /* 减小描述文字大小 */
    line-height: 1.5;
  }
}

.feature-item h3 {
  margin: 0 0 8px;
  font-size: 17px;
  color: #222;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.feature-item p {
  margin: 0;
  color: #555;
  font-size: 14.5px;
  line-height: 1.7;
  letter-spacing: 0.2px;
}

/* 产业布局与社会责任 */
.industry-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.industry-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  background-color: #f0f9ff;
  border-radius: 8px;
  padding: 16px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.industry-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(15, 157, 168, 0.15);
}

.industry-icon {
  font-size: 24px;
  color: #0f9da8;
  flex-shrink: 0;
}

.industry-content {
  flex: 1;
}

.industry-content h3 {
  margin: 0 0 8px;
  font-size: 17px;
  color: #222;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.industry-content p {
  margin: 0;
  color: #555;
  font-size: 14.5px;
  line-height: 1.7;
  letter-spacing: 0.2px;
}

/* 发展历程 - 中间主线两边错位布局 */
.timeline-zigzag {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  padding: 25px 0; /* 桌面端适当增加内边距 */
}

/* 中间主线 */
.timeline-zigzag::before {
  content: '';
  position: absolute;
  left: 50%;
  right: auto;
  transform: translateX(-50%);
  width: 4px; /* 桌面端稍粗一些 */
  top: 0;
  bottom: 0; /* 使用top和bottom替代height: 100%，避免超出容器 */
  background: linear-gradient(to bottom, #0f9da8, #1fb5c4);
  z-index: 1;
  clip-path: none; /* 移除曲线效果 */
}

/* 时间线项目通用样式 */
.timeline-item {
  position: relative;
  margin-bottom: 25px; /* 桌面端项目间距 */
  width: 100%;
  display: flex;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

/* 左侧项目 - 错位布局 */
.timeline-item.left {
  justify-content: flex-end;
  padding-left: 0;
  padding-right: calc(50% + 40px); /* 桌面端增加间距 */
  transform: none;
}

/* 右侧项目 - 错位布局 */
.timeline-item.right {
  justify-content: flex-start;
  padding-left: calc(50% + 40px); /* 桌面端增加间距 */
  padding-right: 0;
  transform: none;
}

/* 年份样式 */
.timeline-year {
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: #0f9da8;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(15, 157, 168, 0.4);
  transition: transform 0.3s ease;
}

.timeline-item:hover .timeline-year {
  transform: scale(1.1);
}

/* 年份圆点位置 - 居中在主线上 */
.timeline-item.left .timeline-year,
.timeline-item.right .timeline-year {
  left: 50%;
  right: auto;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 42px; /* 桌面端稍大一些 */
  height: 42px;
  z-index: 3;
}

/* 内容样式 */
.timeline-content {
  background-color: #f0f9ff;
  border-radius: 12px;
  padding: 20px 24px; /* 桌面端适当增加内边距 */
  width: calc(100% - 40px);
  max-width: 400px; /* 桌面端限制最大宽度 */
  position: relative;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  /* 移除边框 */
}

.timeline-item.left .timeline-content {
  transform-origin: right center;
}

.timeline-item.right .timeline-content {
  transform-origin: left center;
}

.timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.2);
  background-color: #e6f7ff;
}

/* 左侧内容连接线 */
.timeline-item.left .timeline-content::after {
  content: '';
  position: absolute;
  right: -20px; /* 桌面端调整连接线位置 */
  top: 50%;
  width: 20px; /* 桌面端增加连接线长度 */
  height: 2px;
  background: #0f9da8;
  transform: translateY(-50%);
}

/* 右侧内容连接线 */
.timeline-item.right .timeline-content::after {
  content: '';
  position: absolute;
  left: -20px; /* 桌面端调整连接线位置 */
  top: 50%;
  width: 20px; /* 桌面端增加连接线长度 */
  height: 2px;
  background: #0f9da8;
  transform: translateY(-50%);
}

.timeline-content p {
  margin: 0;
  color: #333;
  font-size: 14.5px;
  line-height: 1.7;
  letter-spacing: 0.2px;
}

/* 荣誉资质 */
.honor-content {
  text-align: center;
}

.honor-image {
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.honor-image:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 全屏图片查看器样式 */
.image-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: imageViewerFadeIn 0.3s ease-out;
}

.close-btn-fullscreen {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 32px;
  color: white;
  cursor: pointer;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 8px;
  transition: all 0.2s ease;
}

.close-btn-fullscreen:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.viewer-image-fullscreen {
  max-width: 95vw;
  max-height: 95vh;
  width: auto;
  height: auto;
  object-fit: contain;
  cursor: zoom-out;
}

@keyframes imageViewerFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
/* 全局字体样式 */
.intro-content {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* 移动设备上的时间线样式 - 中间主线两边错位布局 */
@media (max-width: 767px) {
  /* 时间线容器 - 减少内边距 */
  .timeline-zigzag {
    padding: 15px 0; /* 大幅减少移动端时间线内边距 */
  }
  
  /* 中间主线 */
  .timeline-zigzag::before {
    left: 50%;
    right: auto;
    transform: translateX(-50%);
    width: 3px;
    top: 0;
    bottom: 0; /* 移动端也使用top和bottom避免高度超出 */
    clip-path: none;
    background: linear-gradient(to bottom, #0f9da8, #1fb5c4);
  }
  
  /* 左侧项目 - 错位布局 */
  .timeline-item.left {
    justify-content: flex-end;
    padding-left: 0;
    padding-right: calc(50% + 25px); /* 减少左右间距 */
    transform: none;
    margin-bottom: 12px; /* 进一步减少项目间距 */
  }
  
  /* 右侧项目 - 错位布局 */
  .timeline-item.right {
    justify-content: flex-start;
    padding-left: calc(50% + 25px); /* 减少左右间距 */
    padding-right: 0;
    transform: none;
    margin-bottom: 12px; /* 进一步减少项目间距 */
  }
  
  /* 年份圆点位置 - 居中在主线上 */
  .timeline-item.left .timeline-year,
  .timeline-item.right .timeline-year {
    left: 50%;
    right: auto;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 36px;
    height: 36px;
    z-index: 3;
  }
  
  /* 左侧内容连接线 */
  .timeline-item.left .timeline-content::after {
    content: '';
    position: absolute;
    right: -12px; /* 调整连接线位置 */
    top: 50%;
    width: 12px; /* 减少连接线长度 */
    height: 2px;
    background: #0f9da8;
    transform: translateY(-50%);
  }
  
  /* 右侧内容连接线 */
  .timeline-item.right .timeline-content::after {
    content: '';
    position: absolute;
    left: -12px; /* 调整连接线位置 */
    top: 50%;
    width: 12px; /* 减少连接线长度 */
    height: 2px;
    background: #0f9da8;
    transform: translateY(-50%);
  }
  
  .timeline-content {
    width: calc(100% - 25px); /* 调整内容宽度 */
    max-width: 180px; /* 减少内容框最大宽度 */
    padding: 12px 16px; /* 减少内容框内边距 */
    font-size: 13px; /* 减小字体大小 */
    line-height: 1.5; /* 调整行高 */
  }
}

@media (min-width: 768px) {
  .header h1 {
    font-size: 20px;
    letter-spacing: 0.5px;
  }
  
  .main-slogan {
    font-size: 30px;
  }
  
  /* 平板设备上的标语样式调整 */
  .slogan-banner {
    padding: 16px 14px; /* 进一步减少平板上的内边距 */
    margin-top: 8px;
    margin-bottom: 16px;
  }
  
  /* 平板设备上的时间线样式调整 */
  .timeline-zigzag {
    padding: 30px 0;
  }
  
  .timeline-content {
    padding: 18px 22px;
  }
  
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .industry-grid {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .industry-item {
    flex: 1 1 calc(50% - 8px);
    min-width: 250px;
  }
}

@media (min-width: 1024px) {
  .intro-content {
    padding-top: 70px; /* 减少顶部内边距 */
    padding-bottom: 90px; /* 减少底部内边距 */
  }
  
  h2 {
    font-size: 24px;
    letter-spacing: 0.8px;
  }
  
  /* 桌面设备上的标语样式调整 */
  .slogan-banner {
    padding: 18px 16px; /* 桌面端适当调整内边距 */
    margin-top: 5px;
    margin-bottom: 15px;
  }
  
  /* 桌面设备上的时间线样式增强 */
  .timeline-zigzag {
    padding: 35px 0; /* 桌面端适当增加内边距 */
  }
  
  .timeline-year {
    font-size: 16px; /* 桌面端字体稍大 */
    font-weight: 700;
  }
  
  .timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(15, 157, 168, 0.2);
  }
  
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .industry-item {
    flex: 1 1 calc(33.333% - 11px);
  }
}

@media (max-width: 768px) {
  .back-btn {
    display: none;
  }

  /* 移动端全屏图片查看器样式 */
  .close-btn-fullscreen {
    top: 15px;
    right: 15px;
    font-size: 28px;
    padding: 6px;
  }

  .viewer-image-fullscreen {
    max-width: 98vw;
    max-height: 92vh;
  }
}
</style>