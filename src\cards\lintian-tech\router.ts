import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'lintianTechHome',
    component: () => import('./views/HomeView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '霖天科技AI名片'
    }
  },
  {
    path: '/product-intro',
    name: 'lintianTechProductIntro',
    component: () => import('./views/ProductIntroView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '霖天科技AI名片 - 产品介绍'
    }
  },
  {
    path: '/service-cases',
    name: 'lintianTechServiceCases',
    component: () => import('./views/ServiceCasesView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '霖天科技AI名片 - 服务案例'
    }
  }
]

export default routes
