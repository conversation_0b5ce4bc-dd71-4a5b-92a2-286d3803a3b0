<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { 
    size: '595×595×46mm',
    filterGrade: 'G3', 
    ratedFlow: '1800',
    weightEfficiency: '83',
    initialResistance: '20',
    finalResistance: '200',
    dustCapacity: '460'
  },
  { 
    size: '595×595×46mm',
    filterGrade: 'G4', 
    ratedFlow: '1800',
    weightEfficiency: '90',
    initialResistance: '30',
    finalResistance: '200',
    dustCapacity: '490'
  },
  { 
    size: '595×595×96mm',
    filterGrade: 'G3', 
    ratedFlow: '3400',
    weightEfficiency: '83',
    initialResistance: '20',
    finalResistance: '200',
    dustCapacity: '250'
  },
  { 
    size: '595×595×96mm',
    filterGrade: 'G4', 
    ratedFlow: '3400',
    weightEfficiency: '90',
    initialResistance: '30',
    finalResistance: '200',
    dustCapacity: '280'
  }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '高效过滤',
    description: '采用优质过滤材料，对粗颗粒粉尘具有良好的过滤效果，重量效率可达83%-90%。',
    icon: '🌪️'
  },
  {
    title: '低阻力设计',
    description: '初阻力低，运行阻力小，有效降低系统能耗，延长设备使用寿命。',
    icon: '⚡'
  },
  {
    title: '大容尘量',
    description: '具有较大的容尘量，可长时间使用，减少更换频率，降低维护成本。',
    icon: '📦'
  },
  {
    title: '结构稳定',
    description: '采用金属框架结构，强度高，不易变形，确保过滤器长期稳定运行。',
    icon: '🔧'
  },
  {
    title: '安装便捷',
    description: '标准化尺寸设计，安装简便，更换方便，适用于各种空调通风系统。',
    icon: '🔄'
  },
  {
    title: '经济实用',
    description: '成本低廉，性价比高，是空调系统初级过滤的理想选择。',
    icon: '💰'
  }
])

// 应用场景
const applications = reactive([
  {
    title: '空调系统',
    description: '中央空调系统的初级过滤，保护后级精密过滤器',
    icon: '❄️'
  },
  {
    title: '通风系统',
    description: '工业通风系统的预过滤，延长主过滤器使用寿命',
    icon: '💨'
  },
  {
    title: '洁净室',
    description: '洁净室空气处理系统的预过滤环节',
    icon: '🏥'
  },
  {
    title: '办公建筑',
    description: '办公楼、商场等建筑的空气净化系统',
    icon: '🏢'
  }
])

// 技术优势
const technicalAdvantages = reactive([
  {
    title: '多级过滤',
    description: '提供G3、G4等多种过滤等级，满足不同应用需求。',
    icon: '🎯'
  },
  {
    title: '标准化设计',
    description: '符合国际标准，尺寸规范，兼容性强。',
    icon: '📏'
  },
  {
    title: '环保材料',
    description: '采用环保过滤材料，可回收处理，符合环保要求。',
    icon: '🌱'
  },
  {
    title: '质量可靠',
    description: '严格的质量控制体系，确保产品质量稳定可靠。',
    icon: '✅'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>板式初效过滤器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/banshiguolvqi.png" alt="板式初效过滤器" />
        </div>
        <div class="hero-content">
          <h2>板式初效过滤器</h2>
          <p class="product-subtitle">空调通风系统的理想初级过滤解决方案</p>
          <div class="product-intro">
            <p>板式初效过滤器是空调通风系统中最常用的初级过滤设备，主要用于过滤空气中的粗颗粒粉尘。</p>
            <p>采用优质过滤材料和坚固的金属框架，具有过滤效率高、阻力低、容尘量大等特点，是保护后级精密过滤器的理想选择。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>板式初效过滤器通过多层过滤材料对空气中的粗颗粒粉尘进行拦截和吸附。</p>
          <p>当含尘空气通过过滤器时，较大的颗粒被外层过滤材料拦截，较小的颗粒被内层材料捕获，从而实现对空气的初步净化。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>外形尺寸</th>
                <th>过滤等级</th>
                <th>额定风量<br/>(m³/h)</th>
                <th>重量效率<br/>(%)</th>
                <th>初阻力<br/>(Pa)</th>
                <th>终阻力<br/>(Pa)</th>
                <th>容尘量<br/>(g)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.size + spec.filterGrade">
                <td>{{ spec.size }}</td>
                <td>{{ spec.filterGrade }}</td>
                <td>{{ spec.ratedFlow }}</td>
                <td>{{ spec.weightEfficiency }}</td>
                <td>{{ spec.initialResistance }}</td>
                <td>{{ spec.finalResistance }}</td>
                <td>{{ spec.dustCapacity }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications-grid">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <div class="app-icon">{{ app.icon }}</div>
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          技术优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in technicalAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品展示区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 产品特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.feature-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8rem;
}

.specs-table th {
  background: #f8fafc;
  padding: 0.75rem 0.5rem;
  text-align: center;
  font-weight: 600;
  color: #1e3470;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
  font-size: 0.75rem;
}

.specs-table td {
  padding: 0.75rem 0.5rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  white-space: nowrap;
  font-size: 0.8rem;
}

.specs-table tbody tr:hover {
  background: #f8fafc;
}

/* 应用场景网格 */
.applications-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 技术优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .specs-table {
    font-size: 0.85rem;
  }

  .specs-table th {
    font-size: 0.8rem;
    padding: 0.75rem;
  }

  .specs-table td {
    font-size: 0.85rem;
    padding: 0.75rem;
  }

  .applications-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .specs-table {
    font-size: 0.9rem;
  }

  .specs-table th {
    font-size: 0.85rem;
  }

  .specs-table td {
    font-size: 0.9rem;
  }
}
</style>
