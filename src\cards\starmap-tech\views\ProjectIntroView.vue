<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, QuestionFilled, Close, Search } from '@element-plus/icons-vue'
import { onMounted } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/starmap-tech')
}

const goToAIPromoter = () => {
  router.push('/card/starmap-tech/ai-promoter')
}

onMounted(() => {
  document.title = '星图AI名片 - 产品介绍'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品介绍</h1>
    </div>

    <div class="content">
      <!-- 板块一：产品介绍 -->
      <div class="hero-section">
        <h1 class="main-title">星图AI未来成长中心</h1>
        <p class="subtitle">为三四线城市家庭打造，与一线同步的AI成长生态</p>
        <p class="hero-desc">我们不教技能，我们培养孩子与AI共生、协同创造的未来核心能力。</p>
      </div>

      <!-- 板块二：产品介绍 -->
      <!-- 宣传视频 -->   
      <div class="video-section">
        <div class="section-card">
          <video controls loop playsinline preload="metadata" poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/shipingfengmiantu.png"
            class="promo-video"
          >
            <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/XingTuAI/xcsp.mp4" type="video/mp4">
            您的浏览器不支持视频播放。
          </video>
        </div>
      </div>

      <!-- 板块二：我们解决的核心问题 -->
      <div class="pain-points-section">
        <div class="section-card">
          <h2 class="section-title">您是否也有同样的困惑？</h2>

          <div class="pain-points-grid">
            <div class="pain-point-card">
              <div class="pain-point-icon">
                <el-icon size="48"><QuestionFilled /></el-icon>
              </div>
              <h4 class="pain-point-title">认知的迷茫</h4>
              <p class="pain-point-desc">"身边的老师、家长都不懂真正的AI，想教却不知从何下手。"</p>
            </div>

            <div class="pain-point-card">
              <div class="pain-point-icon">
                <el-icon size="48"><Close /></el-icon>
              </div>
              <h4 class="pain-point-title">错误的选择</h4>
              <p class="pain-point-desc">"传统的编程课正在教被AI淘汰的技能，学得苦，还没用。"</p>
            </div>

            <div class="pain-point-card">
              <div class="pain-point-icon">
                <el-icon size="48"><Search /></el-icon>
              </div>
              <h4 class="pain-point-title">资源的匮乏</h4>
              <p class="pain-point-desc">"知道AI很重要，但在我们的城市，找不到一个真正优质、成体系的平台。"</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块三：我们的理念 -->
      <div class="philosophy-section">
        <div class="section-card">
          <h2 class="section-title">星图的理念：我们不教技能，我们培养能力</h2>

          <div class="philosophy-content">
            <p class="philosophy-desc">
              我们坚信，AI就是未来的水和电，将无处不在。因此，让孩子学会与AI'共生'，比学会任何单一技能都重要。星图的核心，是培养孩子驾驭AI解决真实问题的能力，这个过程将自然地塑造他们面向未来的三大核心素养：
            </p>

            <div class="core-abilities">
              <div class="ability-item">
                <div class="ability-icon">💡</div>
                <h4 class="ability-title">创造力</h4>
                <p class="ability-desc">将天马行空的想法，变为现实。</p>
              </div>

              <div class="ability-item">
                <div class="ability-icon">🤝</div>
                <h4 class="ability-title">协作力</h4>
                <p class="ability-desc">学会与人类伙伴、AI伙伴高效合作。</p>
              </div>

              <div class="ability-item">
                <div class="ability-icon">👑</div>
                <h4 class="ability-title">领导力</h4>
                <p class="ability-desc">定义目标，分配任务，驾驭资源达成目的。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块四：三大核心权益 -->
      <div class="benefits-section">
        <div class="section-card">
          <h2 class="section-title">一份投入，整个家庭的未来成长计划</h2>

          <div class="benefits-list">
            <div class="benefit-item">
              <h3 class="benefit-title">1. 无限精彩的线下探索（儿童权益）</h3>
              <div class="benefit-content">
                <p class="benefit-desc"><strong>权益内容：</strong>会员年度（或规定时间）内可免费参与我们举办的每周至少一次的线下主题活动。</p>
                <p class="benefit-value"><strong>价值呈现：</strong>我们将联合各类教育及商业机构，在不同地点举办如"AIGC奇想创作营"、"AI侦探城市寻踪"、"小小生活家-AI购物挑战"等丰富多彩的活动，让孩子在真实的玩耍中感知AI、运用AI。</p>
              </div>
            </div>

            <div class="benefit-item">
              <h3 class="benefit-title">2. 顶尖的线上AI启蒙（儿童权益）</h3>
              <div class="benefit-content">
                <p class="benefit-desc"><strong>权益内容：</strong>免费获得由我们精选的，来自国内一流名校教育理念的AI启蒙线上以及线下课程。</p>
                <p class="benefit-value"><strong>价值呈现：</strong>让孩子体系化地了解人工智能，建立前沿的科技认知，与一线城市的孩子站在同一起跑线上。</p>
              </div>
            </div>

            <div class="benefit-item">
              <h3 class="benefit-title">3. 专属的AI时代家长赋能（家长权益）</h3>
              <div class="benefit-content">
                <p class="benefit-desc"><strong>权益内容：</strong>免费获得我们提供的，针对宝妈群体的"AI+教育"系列线上及线下培训课程。</p>
                <p class="benefit-value"><strong>价值呈现：</strong>课程不仅包含AI辅助育儿的新方法，更涵盖如何利用AI技能实现个人价值、兼职创收的路径，赋能家长与孩子共同成长。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块五：咨询服务 -->
      <div class="consultation-section">
        <div class="section-card">
          <h2 class="section-title">有任何疑问？随时问我</h2>

          <div class="consultation-content">
            <div class="consultation-left">
              <div class="ai-avatar">🤖</div>
            </div>
            <div class="consultation-right">
              <p class="consultation-main">从课程细节到活动安排，从AI理念到会员权益，您有任何想了解的，都可以随时咨询我们的专属AI宣传员。</p>
              <p class="consultation-sub">它7x24小时在线，将为您提供最详尽、最及时的解答。</p>
              <el-button type="primary" size="large" class="consultation-btn" @click="goToAIPromoter">
                点击咨询AI顾问星图
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 板块六：立即开启未来之旅 -->
      <div class="cta-section">
        <div class="section-card">
          <h2 class="section-title">给孩子一个与未来同步的机会</h2>

          <div class="cta-content">
            <div class="qr-code">
              <img
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/erweima.jpg"
                alt="官方微信二维码"
                class="qr-image"
              >
            </div>
            <p class="cta-text">
              立即扫码，添加官方微信。第一时间获取我们的最新活动信息，为孩子开启与AI的伙伴关系！
            </p>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 页面顶部样式 */
.hero-section {
  text-align: center;
  padding: 2rem 0;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  border-radius: 1rem;
  margin-bottom: 1.5rem;
}

.main-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(45deg, #ffffff, #e3f2fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  opacity: 0.95;
  font-weight: 400;
}

.hero-desc {
  font-size: 1rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
  font-style: italic;
}

/* 通用卡片样式 */
.section-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(49, 106, 188, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #316abc;
  margin: 0 0 1rem 0;
  text-align: center;
}

/* 视频样式 */
.video-section {
  margin-bottom: 1.5rem;
}

.promo-video {
  width: 100%;
  max-width: 100%;
  height: auto;
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* 痛点部分样式 */
.pain-points-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.pain-point-card {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ebf8ff, #bee3f8);
  border-radius: 1rem;
  border: 2px solid #7dbbee;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pain-point-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(61, 53, 53, 0.2);
}

.pain-point-icon {
  color: #2c5282;
  margin-bottom: 1rem;
}

.pain-point-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c5282;
  margin: 0 0 1rem 0;
}

.pain-point-desc {
  font-size: 1rem;
  line-height: 1.6;
  color: #2c5282;
  margin: 0;
  font-style: italic;
}

/* 理念部分样式 */
.philosophy-content {
  text-align: center;
}

.philosophy-desc {
  font-size: 1rem;
  line-height: 1.7;
  color: #4a5568;
  margin: 0 0 2rem 0;
  text-align: left;
}

.core-abilities {
  display: grid;
  grid-template-columns: repeat(3,1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.ability-item {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ebf8ff, #bee3f8);
  border-radius: 1rem;
  border: 2px solid #7dbbee;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.ability-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(56, 161, 105, 0.2);
}

.ability-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.ability-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c5282;
  margin: 0 0 0.5rem 0;
}

.ability-desc {
  font-size: 1rem;
  line-height: 1.5;
  color: #2c5282;
  margin: 0;
}

/* 权益部分样式 */
.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.benefit-item {
  padding: 1.5rem;
  background: linear-gradient(135deg, #ebf8ff, #bee3f8);
  border-radius: 1rem;
  border-left: 4px solid #316abc;
}

.benefit-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c5282;
  margin: 0 0 1rem 0;
}

.benefit-content {
  margin-left: 1rem;
}

.benefit-desc, .benefit-value {
  font-size: 1rem;
  line-height: 1.6;
  color: #2a4365;
  margin: 0 0 0.75rem 0;
}

/* 咨询部分样式 */
.consultation-content {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.consultation-left {
  flex-shrink: 0;
}

.ai-avatar {
  font-size: 4rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  border-radius: 50%;
  color: white;
}

.consultation-right {
  flex: 1;
}

.consultation-main {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
}

.consultation-sub {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #718096;
  margin: 0 0 1.5rem 0;
}

.consultation-btn {
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  border: none;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
}

/* CTA部分样式 */
.cta-content {
  text-align: center;
}

.qr-code {
  margin-bottom: 1.5rem;
}

.qr-image {
  width: 200px;
  height: 200px;
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.cta-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #4a5568;
  margin: 0;
}

/* 移动端优化 */
@media (max-width: 767px) {
  .content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .hero-section {
    padding: 1.5rem 0.75rem;
    margin-bottom: 1rem;
  }

  .main-title {
    font-size: 1.6rem;
    line-height: 1.2;
    margin-bottom: 0.5rem;
  }

  .subtitle {
    font-size: 0.95rem;
    line-height: 1.3;
    margin-bottom: 0.75rem;
  }

  .hero-desc {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .section-card {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border-radius: 0.75rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }

  .pain-points-grid,
  .core-abilities {
    
    gap: 0.75rem;
  }

  .pain-point-card,
  .ability-item {
    padding: 0.75rem;
    border-radius: 0.75rem;
  }

  .pain-point-title,
  .ability-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .pain-point-desc,
  .ability-desc {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .benefit-item {
    padding: 1.25rem;
    border-radius: 0.75rem;
    margin-bottom: 1rem;
  }

  .benefit-title {
    font-size: 1rem;
    margin-bottom: 1rem;
    line-height: 1.3;
  }

  .benefit-content {
    margin-left: 0.5rem;
  }

  .benefit-desc,
  .benefit-value {
    font-size: 0.85rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
  }

  .benefits-list {
    gap: 1.25rem;
  }

  .consultation-content {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .ai-avatar {
    font-size: 2.5rem;
    width: 50px;
    height: 50px;
    margin: 0 auto;
  }

  .consultation-main {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
  }

  .consultation-sub {
    font-size: 0.8rem;
    line-height: 1.3;
    margin-bottom: 1rem;
  }

  .consultation-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 0.5rem;
  }

  .qr-code {
    margin-bottom: 1rem;
  }

  .qr-image {
    width: 140px;
    height: 140px;
    border-radius: 0.75rem;
  }

  .cta-text {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .philosophy-desc {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
  }

  .philosophy-content {
    text-align: left;
  }

  .ability-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
  }

  .video-section {
    margin-bottom: 1rem;
  }

  .promo-video {
    border-radius: 0.5rem;
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.4rem;
  }

  .hero-desc {
    font-size: 1.1rem;
  }

  .section-card {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .pain-points-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .core-abilities {
    grid-template-columns: repeat(3, 1fr);
  }

  .consultation-content {
    gap: 3rem;
  }
}

@media (min-width: 1024px) {
  .hero-section {
    padding: 3rem 0;
  }

  .main-title {
    font-size: 3rem;
  }

  .subtitle {
    font-size: 1.6rem;
  }

  .hero-desc {
    font-size: 1.2rem;
  }
}
</style>
