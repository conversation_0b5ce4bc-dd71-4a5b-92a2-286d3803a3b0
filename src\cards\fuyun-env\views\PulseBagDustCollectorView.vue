<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 产品技术参数
const technicalSpecs = reactive([
  { 
    model: 'FDMC-32', 
    filterArea: '24', 
    velocity: '1.04-1.67', 
    airflow: '1500-2400', 
    bagCount: '32', 
    bagWeight: '0.032',
    temperature: '200',
    concentration: '30',
    pressure: '0.5-0.7',
    resistance: '5000',
    power: '≤1200',
    fanPower: '4',
    fanModel: 'Y90S-2',
    motorPower: '1.5',
    weightA: '1350',
    weightB: '1220'
  },
  { 
    model: 'FDMC-48', 
    filterArea: '36', 
    velocity: '1.15-1.62', 
    airflow: '2500-3500', 
    bagCount: '48', 
    bagWeight: '0.048',
    temperature: '200',
    concentration: '30',
    pressure: '0.5-0.7',
    resistance: '5000',
    power: '≤1200',
    fanPower: '6',
    fanModel: 'Y90L-2',
    motorPower: '2.2',
    weightA: '1620',
    weightB: '1470'
  },
  { 
    model: 'FDMC-64', 
    filterArea: '48', 
    velocity: '1.21-1.74', 
    airflow: '3500-5000', 
    bagCount: '64', 
    bagWeight: '0.064',
    temperature: '200',
    concentration: '30',
    pressure: '0.5-0.7',
    resistance: '5000',
    power: '≤1200',
    fanPower: '8',
    fanModel: 'Y100L-2',
    motorPower: '3',
    weightA: '1850',
    weightB: '1670'
  },
  { 
    model: 'FDMC-80', 
    filterArea: '60', 
    velocity: '1.25-1.67', 
    airflow: '4500-6000', 
    bagCount: '80', 
    bagWeight: '0.08',
    temperature: '200',
    concentration: '30',
    pressure: '0.5-0.7',
    resistance: '5000',
    power: '≤1200',
    fanPower: '10',
    fanModel: 'Y132S1-2',
    motorPower: '5.5',
    weightA: '2360',
    weightB: '2150'
  },
  { 
    model: 'FDMC-96', 
    filterArea: '72', 
    velocity: '1.27-1.62', 
    airflow: '5500-7000', 
    bagCount: '96', 
    bagWeight: '0.096',
    temperature: '200',
    concentration: '30',
    pressure: '0.5-0.7',
    resistance: '5000',
    power: '≤1200',
    fanPower: '12',
    fanModel: 'Y132S1-2',
    motorPower: '5.5',
    weightA: '2800',
    weightB: '2540'
  },
  { 
    model: 'FDMC-112', 
    filterArea: '84', 
    velocity: '1.28-1.68', 
    airflow: '6500-8500', 
    bagCount: '112', 
    bagWeight: '0.11',
    temperature: '200',
    concentration: '30',
    pressure: '0.5-0.7',
    resistance: '5000',
    power: '≤1200',
    fanPower: '14',
    fanModel: 'Y132S2-2',
    motorPower: '7.5',
    weightA: '3200',
    weightB: '2880'
  }
])

// 性能特点
const productFeatures = reactive([
  {
    title: '广泛应用',
    description: 'FDMC型单机脉冲布袋除尘器广泛用于建材、冶金、矿山、化工、煤炭、非金属矿、细粉加工等行业含尘气体净化处理系统。'
  },
  {
    title: '高压脉冲',
    description: '采用高压(0.5-0.7MPa)大流量脉冲阀逐条除尘布袋喷吹清灰的技术，具有清灰动能大，清灰效率高的特点。'
  },
  {
    title: '结构合理',
    description: '结构设计合理，体积小，重量轻，结构紧凑、安装容易、维护方便。'
  },
  {
    title: '除尘高效',
    description: '除尘效率高，除尘器出口气体含尘浓度在30mg/m³之内，对亚微米粒径的细尘有较高的分级效率。'
  },
  {
    title: '耐高温',
    description: '采用玻璃纤维、聚四氟乙烯、P84等高温滤料时，可在200℃以上的高温条件下运行。'
  },
  {
    title: '排尘灵活',
    description: '排尘方式灵活适用，可选择卸料阀或翻板阀等方式。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>脉冲单机布袋除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/maichong.jpg" alt="脉冲单机布袋除尘器" />
        </div>
        <div class="hero-content">
          <h2>脉冲单机布袋除尘器</h2>
          <p class="product-subtitle">高效脉冲清灰布袋除尘器，适用于各种工业粉尘处理场合</p>
          <div class="product-intro">
            <p>该设备采用高压(0.5-0.7MPa)大流量脉冲逐条喷吹方式，具有除尘效率高、清灰动能大、体积小、重量轻、结构简单紧凑、安装容易、运行稳定、性能可靠，滤袋使用寿命长，维修方便等优点。</p>
            <p>含尘气体由除尘器中箱体中下部进入，通过滤袋将粉尘阻留在滤袋外表面，净气进入袋内到上箱由风机排出。随着过滤时间的延长，滤袋上的粉尘层不断积厚，除尘设备的阻力不断上升，当设备的阻力上升到一定值时，清灰装置在脉冲控制仪的控制下开始清灰。</p>
            <p>清灰时，压缩空气以极短的时间按顺序通过脉冲阀，经喷吹管向滤袋喷射，随着在滤袋外表面上的粉尘脱离滤袋落入灰斗、抽屉或直接落入仓内。清灰结束除尘器恢复正常工作。在脉冲控制装置的作用下，按一定要求进行周期循环清灰，使除尘器始终保持良好的工作状态。</p>
          </div>
        </div>
      </div>

      <!-- 性能特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          性能特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术参数
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th rowspan="2">型号</th>
                <th rowspan="2">过滤面积<br/>(m²)</th>
                <th rowspan="2">过滤风速<br/>(m/min)</th>
                <th rowspan="2">处理风量<br/>(m³/h)</th>
                <th rowspan="2">滤袋数量<br/>(条)</th>
                <th rowspan="2">滤袋重量<br/>(kg)</th>
                <th rowspan="2">入口温度<br/>(℃)</th>
                <th rowspan="2">出口浓度<br/>(g/Nm³)</th>
                <th rowspan="2">喷吹压力<br/>(Mpa)</th>
                <th rowspan="2">承受压力<br/>(pa)</th>
                <th rowspan="2">设备阻力<br/>(pa)</th>
                <th colspan="3">脉冲阀/风机/电机型号</th>
                <th colspan="2">设备重量Kg(参考)</th>
              </tr>
              <tr>
                <th>脉冲阀<br/>(个)</th>
                <th>风机型号</th>
                <th>功率<br/>(KW)</th>
                <th>FDMC(A)-带灰斗和卸料器</th>
                <th>FDMC(B)-法兰式</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.filterArea }}</td>
                <td>{{ spec.velocity }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.bagCount }}</td>
                <td>{{ spec.bagWeight }}</td>
                <td>{{ spec.temperature }}</td>
                <td>{{ spec.concentration }}</td>
                <td>{{ spec.pressure }}</td>
                <td>{{ spec.resistance }}</td>
                <td>{{ spec.power }}</td>
                <td>{{ spec.fanPower }}</td>
                <td>{{ spec.fanModel }}</td>
                <td>{{ spec.motorPower }}</td>
                <td>{{ spec.weightA }}</td>
                <td>{{ spec.weightB }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🌪️</div>
            <div class="indicator-content">
              <h4>除尘效率</h4>
              <div class="indicator-value">≥99%</div>
              <p>出口浓度≤30mg/m³</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">⚡</div>
            <div class="indicator-content">
              <h4>处理风量</h4>
              <div class="indicator-value">1500-8500</div>
              <p>m³/h大范围覆盖</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔧</div>
            <div class="indicator-content">
              <h4>清灰压力</h4>
              <div class="indicator-value">0.5-0.7MPa</div>
              <p>高压脉冲清灰</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🌡️</div>
            <div class="indicator-content">
              <h4>工作温度</h4>
              <div class="indicator-value">≤200℃</div>
              <p>高温滤料可选</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📏</div>
            <div class="indicator-content">
              <h4>过滤面积</h4>
              <div class="indicator-value">24-84m²</div>
              <p>多规格可选</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔇</div>
            <div class="indicator-content">
              <h4>设备阻力</h4>
              <div class="indicator-value">≤1200Pa</div>
              <p>低阻力运行</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <div class="advantage-content">
              <h4>高效清灰</h4>
              <p>采用高压(0.5-0.7MPa)大流量脉冲阀逐条除尘布袋喷吹清灰技术，清灰动能大，清灰效率高，确保滤袋长期稳定运行。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🏗️</div>
            <div class="advantage-content">
              <h4>结构紧凑</h4>
              <p>结构设计合理，体积小，重量轻，结构紧凑，安装容易，维护方便，占地面积小，适合各种工业环境。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🌡️</div>
            <div class="advantage-content">
              <h4>耐高温性</h4>
              <p>采用玻璃纤维、聚四氟乙烯、P84等高温滤料时，可在200℃以上的高温条件下运行，适应恶劣工况。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔄</div>
            <div class="advantage-content">
              <h4>排尘灵活</h4>
              <p>排尘方式灵活适用，可选择卸料阀或翻板阀等方式，满足不同工艺要求和现场条件。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications">
          <div class="application-item">
            <div class="app-icon">🏗️</div>
            <h4>建材行业</h4>
            <p>水泥厂、石灰厂、砖瓦厂等建材生产粉尘处理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">⚒️</div>
            <h4>冶金行业</h4>
            <p>钢铁厂、有色金属冶炼厂粉尘净化处理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">⛏️</div>
            <h4>矿山行业</h4>
            <p>矿物开采、破碎、筛分过程粉尘控制</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🧪</div>
            <h4>化工行业</h4>
            <p>化工原料生产、粉体加工粉尘治理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">⚫</div>
            <h4>煤炭行业</h4>
            <p>煤炭加工、运输、储存过程粉尘净化</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🔬</div>
            <h4>细粉加工</h4>
            <p>各类细粉料加工生产线粉尘收集处理</p>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases">
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/18-gymf/20240130111930.jpg" alt="脉冲单机布袋除尘器工程案例1" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/18-gymf/20240130112106.jpg" alt="脉冲单机布袋除尘器工程案例2" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/18-gymf/202405290859081.jpg" alt="脉冲单机布袋除尘器工程案例3" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/18-gymf/20240529090200.jpg" alt="脉冲单机布袋除尘器工程案例4" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.3;
  font-size: 0.7rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.55rem;
  min-width: 1400px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.5rem 0.3rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.5rem;
  line-height: 1.1;
  border: 1px solid #2563eb;
}

.specs-table td {
  padding: 0.5rem 0.3rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.55rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 产品优势 */
.advantages {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 应用场景 */
.applications {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.application-item {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
}

.application-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 工程案例样式 */
.project-cases {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.case-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.case-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.case-item img {
  width: 100%;
  height: 200px;
  object-fit: cover; /* 移动端填充整个容器，不留空白 */
  background-color: #f8fafc;
  transition: transform 0.3s ease;
}

.case-item:hover img {
  transform: scale(1.02);
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .feature-card p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .specs-table {
    font-size: 0.65rem;
  }

  .specs-table th {
    padding: 0.75rem 0.5rem;
    font-size: 0.6rem;
  }

  .specs-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.65rem;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .advantages {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .application-item {
    padding: 1.5rem;
  }

  .application-item h4 {
    font-size: 1.1rem;
  }

  .application-item p {
    font-size: 0.9rem;
  }

  /* 平板端工程案例样式 */
  .project-cases {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .case-item img {
    height: 250px;
    object-fit: cover; /* 平板端也使用cover，不留空白 */
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .applications {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.7rem;
  }

  .specs-table th {
    font-size: 0.65rem;
  }

  .specs-table td {
    font-size: 0.7rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  /* 桌面端工程案例样式 */
  .project-cases {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .case-item img {
    height: 300px;
    object-fit: cover; /* 桌面端也使用cover，确保图片填满容器 */
  }
}
</style>
