<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'

import TabBar from '../components/TabBar.vue'

// 类型定义
interface CaseData {
  id: number
  title: string
  image: string
  summary: string
  background?: string
  solution?: string
  value?: string
  overview?: string
  design?: string
  technology?: string
}

interface SolutionSection {
  title: string
  description: string
  icon?: any
  image: string // 封面图
  cases: CaseData[]
}

type SectionKey = 'smartGovernance' | 'smartAffairs' | 'smartLife' | 'smartManagement'

const goBack = () => {
  // 返回到主页
  window.location.href = '/card/wanwang-tech'
}



// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}




// 跳转到产品详情页面
const showCaseDetail = (caseData: CaseData) => {
  console.log('点击的卡片标题:', caseData.title)

  // 重点人员管控平台跳转到专门的详细页面
  if (caseData.title === '涉诈人员动态管控平台') {
    console.log('匹配到涉诈人员动态管控平台，准备跳转')
    window.location.href = '/card/wanwang-tech/zhongdian-renyuan-guankong-detail'
    return
  }

  // 智慧党建云平台跳转到专门的详细页面
  if (caseData.title === '智慧党建云平台') {
    console.log('匹配到智慧党建云平台，准备跳转')
    window.location.href = '/card/wanwang-tech/zhihui-dangjian-yun-detail'
    return
  }

  // 智慧工会服务云平台跳转到专门的详细页面
  if (caseData.title === '智慧工会服务云平台') {
    console.log('匹配到智慧工会服务云平台，准备跳转')
    window.location.href = '/card/wanwang-tech/zhihui-gonghui-detail'
    return
  }

  // "敲门嫂"钢城红信息化平台跳转到专门的详细页面
  if (caseData.title === '“敲门嫂”钢城红信息化平台') {
    console.log('匹配到"敲门嫂"钢城红信息化平台，准备跳转')
    window.location.href = '/card/wanwang-tech/qiaomensao-detail'
    return
  }

  // 反诈研判系统跳转到专门的详细页面
  if (caseData.title === '反诈研判系统') {
    console.log('匹配到反诈研判系统，准备跳转')
    window.location.href = '/card/wanwang-tech/fanzha-yanpan-detail'
    return
  }

  // 疫情防控实战平台跳转到专门的详细页面
  if (caseData.title === '疫情防控实战平台') {
    console.log('匹配到疫情防控实战平台，准备跳转')
    window.location.href = '/card/wanwang-tech/yiqing-fangkong-detail'
    return
  }

  // 消防安全管理系统跳转到专门的详细页面
  if (caseData.title === '消防安全管理系统') {
    console.log('匹配到消防安全管理系统，准备跳转')
    window.location.href = '/card/wanwang-tech/xiaofang-anquan-detail'
    return
  }

  // 舆情督办系统跳转到专门的详细页面
  if (caseData.title === '舆情督办系统') {
    console.log('匹配到舆情督办系统，准备跳转')
    window.location.href = '/card/wanwang-tech/yuqing-duban-detail'
    return
  }

  // 阳光招采平台跳转到专门的详细页面
  if (caseData.title === '阳光招采平台') {
    console.log('匹配到阳光招采平台，准备跳转')
    window.location.href = '/card/wanwang-tech/yangguang-zhaocai-detail'
    return
  }

  // 平安义警大数据管理平台跳转到专门的详细页面
  if (caseData.title === '平安义警大数据管理平台') {
    console.log('匹配到平安义警大数据管理平台，准备跳转')
    window.location.href = '/card/wanwang-tech/pingan-yijing-detail'
    return
  }

  // 全域旅游智慧地图管理系统跳转到专门的详细页面
  if (caseData.title === '全域旅游智慧地图管理系统') {
    console.log('匹配到全域旅游智慧地图管理系统，准备跳转')
    window.location.href = '/card/wanwang-tech/quanyu-lvyou-detail'
    return
  }

  // 三重一大信息监管平台跳转到专门的详细页面
  if (caseData.title === '三重一大信息监管平台') {
    console.log('匹配到三重一大信息监管平台，准备跳转')
    window.location.href = '/card/wanwang-tech/sanzhong-yida-detail'
    return
  }

  // 三甲医院门户网站管理系统跳转到专门的详细页面
  if (caseData.title === '三甲医院门户网站管理系统') {
    console.log('匹配到三甲医院门户网站管理系统，准备跳转')
    window.location.href = '/card/wanwang-tech/sanjia-yiyuan-detail'
    return
  }

  // 上饶市信州区人民政府官网跳转到专门的详细页面
  if (caseData.title === '上饶市信州区人民政府') {
    console.log('匹配到上饶市信州区人民政府，准备跳转')
    window.location.href = '/card/wanwang-tech/shangrao-xinzhou-zhengfu-detail'
    return
  }

  // 广丰区人民政府官网跳转到专门的详细页面
  if (caseData.title === '广丰区人民政府') {
    console.log('匹配到广丰区人民政府，准备跳转')
    window.location.href = '/card/wanwang-tech/guangfeng-zhengfu-detail'
    return
  }

  // 上饶国控投资集团有限公司官网跳转到专门的详细页面
  if (caseData.title === '上饶国控投资集团有限公司') {
    console.log('匹配到上饶国控投资集团有限公司，准备跳转')
    window.location.href = '/card/wanwang-tech/guokong-touzi-detail'
    return
  }

  // 上饶文旅集团官网跳转到专门的详细页面
  if (caseData.title === '上饶农文旅集团') {
    console.log('匹配到上饶文旅集团，准备跳转')
    window.location.href = '/card/wanwang-tech/shangrao-wenlv-detail'
    return
  }

  // 广信工投集团官网跳转到专门的详细页面
  if (caseData.title === '广信工投集团') {
    console.log('匹配到广信工投集团，准备跳转')
    window.location.href = '/card/wanwang-tech/guangxin-gongtou-detail'
    return
  }

  // 上饶市人民医院官网跳转到专门的详细页面
  if (caseData.title === '上饶市人民医院') {
    console.log('匹配到上饶市人民医院，准备跳转')
    window.location.href = '/card/wanwang-tech/shangrao-renmin-yiyuan-detail'
    return
  }

  // 上饶中学官网跳转到专门的详细页面
  if (caseData.title === '上饶中学') {
    console.log('匹配到上饶中学，准备跳转')
    window.location.href = '/card/wanwang-tech/shangrao-zhongxue-detail'
    return
  }

  // 上饶卫生学校官网跳转到专门的详细页面
  if (caseData.title === '上饶卫生学校') {
    console.log('匹配到上饶卫生学校，准备跳转')
    window.location.href = '/card/wanwang-tech/shangrao-weisheng-xuexiao-detail'
    return
  }

  // 其他案例显示弹窗

}


// 解决方案数据
const solutions = reactive<Record<SectionKey, SolutionSection>>({
  smartGovernance: {
    title: '智慧治理',
    description: '数字驱动社会治理，提升城市管理效能。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuizhili.jpeg',
    cases: [
      {
        id: 1,
        title: '涉诈人员动态管控平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhongDianRenYuanGuanKong.png',
        summary: '对涉诈等重点人员进行数字化、人性化的全周期闭环管控。',
      },
    ]
  },
  smartAffairs: {
    title: '智慧政务',
    description: '赋能政府数字化，构建高效服务型政府。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuizhengwu.jpeg',
    cases: [
      {
        id: 1,
        title: '智慧党建云平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiDangJianYun.png',
        summary: '互联网+党建，运用信息化新技术，整合各方资源，提高党的执政能力。',
           },
      {
        id: 2,
        title: '智慧工会服务云平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiGongHui.png',
        summary: '构建线上线下融合的职工服务体系，提升工会服务效能。',
           },

    ]
  },
  smartLife: {
    title: '智慧生活',
    description: '科技便利民生，开启智慧城市生活。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuishenghuo.jpeg',
    cases: [
      {
        id: 1,
        title: '“敲门嫂”钢城红信息化平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/qiaomensao.jpg',
        summary: '致力将其打造成“三手”，即老百姓的生活帮手、社区干部的工具助手、党建宣传的工作抓手。',
       },
    
    ]
  },
  smartManagement: {
    title: '智慧管理',
    description: '精细化管理系统，提升企业园区效率。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuiguanli.jpeg',
    cases: [
      {
        id: 1,
        title: '反诈研判系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/fanzhayanpan11.jpg',
        summary: '为达到全面、实时、准确的分析研判辖区内案事件的特点和规律，达到“预防犯罪、发现犯罪、打击犯罪"为目的，进一步突出信息在侦查办案中的作用。',
    },
      {
        id: 2,
        title: '疫情防控实战平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/YiQingFangKongShiZhanPingTai.png',
        summary: '整合多源数据，实现从发现、核查到管控全流程闭环的实战指挥平台。',
       }
    ]
  }
})

// 门户网站建设案例数据
const websiteCases = reactive<CaseData[]>([
  {
    id: 1,
    title: '消防安全管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiXiaoFang.png',
    summary: '“消防安全管理系统”，为解决“九小”场所消防安全问题提供有力支持。',
   },
  {
    id: 2,
    title: '舆情督办系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yuqingduban.png',
    summary: '舆情督办系统是为了提高舆情工作处置效率、实时跟踪舆情工作进程而开发的“舆情任务管理器”。',
  },
  {
    id: 3,
    title: '阳光招采平台',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yangguangcaizhao.jpg',
    summary: '提高处置效率、跟踪工作进展',
},

  {
    id: 4,
    title: '平安义警大数据管理平台',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pijnganyijing.jpg',
    summary: '为义警队伍与管理者提供了一个安全、高效、便利、智慧化的管理平台。',
},

  {
    id: 5,
    title: '全域旅游智慧地图管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/quantuzhihui.jpg',
    summary: '全域旅游智慧地图管理系统是由江西万网科技有限公司倾力打造的一款全方位、智能化的旅游管理系统。',

  },

  {
    id: 6,
    title: '三重一大信息监管平台',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanzhongyida.jpg',
    summary: '“三重一大”信息监管平台是一个针对重大决策、重要干部任免、重大项目投资决策、大额资金使用等核心事项进行综合性管理的系统。',
  },

  {
    id: 7,
    title: '三甲医院门户网站管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanjiawangzhan.jpg',
    summary: '该系统旨在满足医院全方位的信息展示与互动需求，提供从医院概况、科室介绍到就医指南等一站式信息服务提升医院的形象和服务品质。',
   },
  // 上饶市信州区人民政府官网
  {
    id: 8,
    title: '上饶市信州区人民政府',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoRenMinZhenFu.jpg',
    summary: '为信州区人民政府精心打造的官方门户网站，是政府面向公众进行信息发布、提供在线服务、开展政民互动的重要窗口。',
   },
  // 广丰区人民政府官网
  {
    id: 9,
    title: '广丰区人民政府',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuangFengRenMinZhenFu.png',
    summary: '为广丰区人民政府倾力打造的官方门户网站，是集信息发布、解读回应、政民互动、政务服务于一体的权威平台。',
   },
  // 上饶国控投资集团有限公司官网
  {
    id: 10,
    title: '上饶国控投资集团有限公司',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuoKongTouZi.png',
    summary: '为上饶国控集团量身定制的官方门户网站，全面展示了国控集团作为城市建设主力军的雄厚实力和品牌形象。',
   },
  // 上饶文旅集团官网
  {
    id: 11,
    title: '上饶农文旅集团',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoWenLv.jpg',
    summary: '为上饶文旅集团量身定制的官方品牌门户网站，以"高铁枢纽，大美上饶"为主题，全面展示集团产业布局和辉煌成就。',
   },
  // 广信工投集团官网
  {
    id: 12,
    title: '广信工投集团',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuangXinGongTouJiTuan.jpg',
    summary: '为广信工投集团精心打造的官方品牌网站，全面展示集团在工业项目投资、资产管理、园区开发等方面的核心业务与战略布局。',
   },
  // 上饶市人民医院官网
  {
    id: 13,
    title: '上饶市人民医院',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoRenMinYiYuan.jpg',
    summary: '为三级甲等综合医院——上饶市人民医院构建的官方服务平台，旨在为患者提供权威、便捷、全面的线上医疗信息服务。',
   },
  // 上饶中学官网
  {
    id: 14,
    title: '上饶中学',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoZhongXue.jpg',
    summary: '为知名学府——上饶中学量身打造的官方门户网站，是学校对外展示办学理念、校园文化、师资力量和教学成果的核心窗口。',
   },
  // 上饶卫生学校官网
  {
    id: 15,
    title: '上饶卫生学校',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoWeiShengXueXiao.png',
    summary: '为培养医护人才的摇篮——上饶卫生学校建设的官方门户网站，是学校教学成果、招生信息、就业服务的重要发布平台。',
   },

])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>产品中心</h1>
    </div>

    <div class="content">
      <!-- 直接显示所有案例，不分类 -->
      <div class="cards-grid">
        <!-- 智慧解决方案案例 -->
        <template v-for="(solution, solutionKey) in solutions" :key="solutionKey">
          <div
            v-for="case_ in solution.cases"
            :key="`solution-${case_.id}`"
            class="card-item"
            @click="showCaseDetail(case_)"
          >
            <div class="card-image">
              <img :src="case_.image" :alt="case_.title" />
            </div>
            <div class="card-content">
              <h3>{{ case_.title }}</h3>
              <p>{{ case_.summary }}</p>
            </div>
          </div>
        </template>

        <!-- 网站建设案例 -->
        <div
          v-for="case_ in websiteCases"
          :key="`website-${case_.id}`"
          class="card-item"
          @click="showCaseDetail(case_)"
        >
          <div class="card-image">
            <img :src="case_.image" :alt="case_.title" />
          </div>
          <div class="card-content">
            <h3>{{ case_.title }}</h3>
            <p>{{ case_.summary }}</p>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}





/* 卡片网格布局 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.8rem;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}

/* 统一的卡片样式 */
.card-item {
  background: #f8fafc;
  border-radius: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  min-height: 120px;
}

.card-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.15);
  border-color: #1693d2;
}

.card-image {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
  margin-right: 0.6rem;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  border-radius: 0.5rem;
}

.card-item:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  flex: 1;
  padding: 0.6rem;
}

.card-content h3 {
  margin: 0 0 0.4rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1693d2;
  line-height: 1.2;
}

.card-content p {
  margin: 0;
  color: #666;
  line-height: 1.3;
  font-size: 0.8rem;
}



.solution-section {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.section-header {
  padding: 1.25rem 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafbfc;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.section-header:hover {
  background: #f0f9ff;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.section-icon {
  font-size: 1.3rem;
  color: #1693d2;
}

.expand-icon {
  font-size: 1.2rem;
  color: #666;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.section-description {
  padding: 0 1.5rem 1.25rem 1.5rem;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}





.case-card-placeholder {
  padding: 2rem;
  text-align: center;
  color: #999;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 2px dashed #e5e7eb;
}











/* 响应式设计 */
@media (max-width: 768px) {


  /* 移动端卡片统一样式 */
  .card-item {
    min-height: 140px !important;
    height: 140px;
    display: flex;
    align-items: center;
  }

  .card-image {
    width: 70px !important;
    height: 70px !important;
    margin-right: 1rem !important;
  }

  .card-content {
    flex: 1;
    padding: 1rem !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .card-content h3 {
    font-size: 1rem !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.3 !important;
  }

  .card-content p {
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }


}

.case-detail-image {
  margin-bottom: 1.5rem;
}

.case-detail-image img {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.case-detail-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.detail-section h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1693d2;
  border-left: 4px solid #1693d2;
  padding-left: 0.75rem;
}

.detail-section p {
  margin: 0 0 0.5rem 0;
  color: #555;
  line-height: 1.6;
}

/* 网站案例内容样式 */
.websites-content {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  text-align: left;
}

.websites-content .placeholder h3 {
  color: #1693d2;
  margin-bottom: 1rem;
}

.websites-content .placeholder p {
  color: #666;
}

/* 门户网站建设案例样式 */
.websites-content {
  padding: 1rem 0;
}

.website-category {
  margin-bottom: 3rem;
}

.category-header {
  margin-bottom: 2rem;
  text-align: center;
}

.category-header h2 {
  color: #1693d2;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.category-description {
  color: #666;
  font-size: 1.2rem;
  line-height: 1.6;
  margin: 0;
  max-width: 800px;
  margin: 0 auto;
}

.websites-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  padding: 0 1rem;
}

.website-card {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.website-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(22, 147, 210, 0.15);
}

.website-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.website-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.website-card:hover .website-image img {
  transform: scale(1.05);
}

.website-content {
  padding: 1.5rem;
}

.website-content h3 {
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.website-content p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.intranet-tag {
  background: #f0f9ff;
  color: #1693d2;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  margin-top: 0.75rem;
  display: inline-block;
  border: 1px solid #e0f2fe;
}

/* 弹窗中的门户网站样式 */
.subsection {
  margin-bottom: 1.5rem;
}

.subsection h4 {
  color: #1693d2;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}



/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .cards-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;
    padding: 0 2rem;
  }

  .card-image {
    width: 80px;
    height: 80px;
    margin-right: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .card-content h3 {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }

  .card-content p {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .tab-btn {
    font-size: 1rem;
    padding: 1rem 1.5rem;
  }

  .section-header {
    padding: 1.5rem 2rem;
  }

  .section-description {
    padding: 0 2rem 1.5rem 2rem;
  }

  .websites-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;
    padding: 0 2rem;
  }

  .waterfall-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;
    padding: 0 2rem;
  }

  .website-image {
    height: 250px;
  }
}

@media (min-width: 1024px) {

  .cards-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .card-image {
    width: 90px;
    height: 90px;
    margin-right: 1.2rem;
  }

  .card-content {
    padding: 1.2rem;
  }

  .card-content h3 {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }

  .card-content p {
    font-size: 0.95rem;
    line-height: 1.6;
  }

  .websites-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .waterfall-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .website-image {
    height: 280px;
  }


}



/* 网站类型Tab标签样式 */
.website-tabs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.website-tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 2rem;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.website-tab-btn:hover {
  border-color: #1693d2;
  color: #1693d2;
  transform: translateY(-2px);
}

.website-tab-btn.active {
  background: #1693d2;
  border-color: #1693d2;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
}

/* 当前分类描述样式 */
.current-category-description {
  text-align: center;
  margin-bottom: 2rem;
}

.current-category-description p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 瀑布流卡片布局样式 */
.waterfall-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-top: 1.5rem;
  padding: 0 1rem;
}

.waterfall-card {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.waterfall-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.waterfall-card:hover .card-image img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(22, 147, 210, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.waterfall-card:hover .card-overlay {
  opacity: 1;
}

.overlay-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-weight: 500;
}

.card-content {
  padding: 1.5rem;
}

.card-content h3 {
  color: #1693d2;
  font-size: 1.3rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  line-height: 1.3;
}

.card-content p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.intranet-badge,
.online-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
}

.intranet-badge {
  background: #f0f0f0;
  color: #666;
}

.online-badge {
  background: #e8f5e8;
  color: #4caf50;
}

.intranet-overlay {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
}



/* 弹窗内子标题样式 */
.subsection {
  margin-bottom: 1.5rem;
}

.subsection h4 {
  color: #1693d2;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.subsection p {
  color: #666;
  line-height: 1.6;
}

/* 解决方案详情和网站详情 */
.solution-detail, .website-detail {
  background: white;
  border-radius: 1rem;
  margin-top: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.solution-detail-header, .website-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.solution-detail-header h2, .website-detail-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #1693d2;
}

/* 统一卡片列表 */
.cards-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

.card-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.card-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.15);
}

.card-image {
  width: 120px;
  height: 120px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-item:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  padding: 1.25rem;
  flex: 1;
}

.card-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1693d2;
}

.card-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 分类标题样式 */
.section-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  margin-bottom: 1rem;
  position: relative;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1693d2;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.section-header .back-btn {
  background: #1693d2;
  border: 1px solid #1693d2;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  margin-right: 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
}

.section-header .back-btn:hover {
  background-color: #0f7bb8;
  border-color: #0f7bb8;
}


</style>
