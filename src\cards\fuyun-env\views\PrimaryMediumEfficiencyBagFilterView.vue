<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { 
    type: 'KC36', 
    dimensions: '292×592×360', 
    filterGrade: 'G1/G2 G3/G4', 
    efficiency: '50-90% 计数法/Dust-Spot',
    ratedAirFlow: '1700',
    initialResistance: '40-50',
    finalResistance: '200',
    pockets: '3',
    humidity: '100',
    fireproofClass: 'Class2 (UL-900-1996)'
  },
  { 
    type: 'KC36', 
    dimensions: '490×592×360', 
    filterGrade: 'G1/G2 G3/G4', 
    efficiency: '50-90% 计数法/Dust-Spot',
    ratedAirFlow: '2800',
    initialResistance: '40-50',
    finalResistance: '200',
    pockets: '5',
    humidity: '100',
    fireproofClass: 'Class2 (UL-900-1996)'
  },
  { 
    type: 'KC36', 
    dimensions: '592×592×360', 
    filterGrade: 'G1/G2 G3/G4', 
    efficiency: '50-90% 计数法/Dust-Spot',
    ratedAirFlow: '3400',
    initialResistance: '40-50',
    finalResistance: '200',
    pockets: '6',
    humidity: '100',
    fireproofClass: 'Class2 (UL-900-1996)'
  },
  { 
    type: 'KC65', 
    dimensions: '292×592×650', 
    filterGrade: 'F5/F6 F7/F8', 
    efficiency: '40-95% 比色法/Dust-Spot',
    ratedAirFlow: '1700',
    initialResistance: '60-80',
    finalResistance: '300',
    pockets: '3',
    humidity: '100',
    fireproofClass: 'Class2 (UL-900-1996)'
  },
  { 
    type: 'KC65', 
    dimensions: '490×592×650', 
    filterGrade: 'F5/F6 F7/F8', 
    efficiency: '40-95% 比色法/Dust-Spot',
    ratedAirFlow: '2800',
    initialResistance: '60-80',
    finalResistance: '300',
    pockets: '5',
    humidity: '100',
    fireproofClass: 'Class2 (UL-900-1996)'
  },
  { 
    type: 'KC65', 
    dimensions: '592×592×650', 
    filterGrade: 'F5/F6 F7/F8', 
    efficiency: '40-95% 比色法/Dust-Spot',
    ratedAirFlow: '3400',
    initialResistance: '60-80',
    finalResistance: '300',
    pockets: '6',
    humidity: '100',
    fireproofClass: 'Class2 (UL-900-1996)'
  }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '多级过滤',
    description: '满足不同需求',
    icon: '🎯'
  },
  {
    title: '过滤效率逐级提升',
    description: '从粗效到高效的全系列过滤等级',
    icon: '📈'
  },
  {
    title: '标准化设计',
    description: '通用性强',
    icon: '🔧'
  },
  {
    title: '维护更换方便',
    description: '便于日常维护和更换操作',
    icon: '🔄'
  }
])

// 应用场景
const applications = reactive([
  {
    title: '空调系统',
    description: '空调系统',
    icon: '❄️'
  },
  {
    title: '洁净室',
    description: '洁净室',
    icon: '🏥'
  },
  {
    title: '医疗设施',
    description: '医疗设施',
    icon: '🏥'
  },
  {
    title: '精密制造',
    description: '精密制造',
    icon: '🏭'
  }
])

// 技术优势
const technicalAdvantages = reactive([
  {
    title: '全系列产品',
    description: '涵盖初效、中效、高效全系列',
    icon: '📏'
  },
  {
    title: '标准化设计',
    description: '符合国际标准',
    icon: '🎯'
  },
  {
    title: '高性价比',
    description: '性能优异，价格合理',
    icon: '💰'
  },
  {
    title: '质量可靠',
    description: '严格质量控制',
    icon: '✅'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>初中效袋式过滤器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/chuzhongxiao.jpg" alt="初中效(袋式)过滤器" />
        </div>
        <div class="hero-content">
          <h2>初中效(袋式)过滤器</h2>
          <p class="product-subtitle">多级过滤器系列，满足不同精度的空气净化需求</p>
          <div class="product-intro">
            <p>初/中/高效过滤器系列产品涵盖了从粗效到高效的全系列过滤等级。</p>
            <p>初效过滤器用于预过滤，中效过滤器用于中级净化，高效过滤器用于精密过滤，可根据不同需求进行组合使用。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>初中效袋式过滤器通过多个过滤袋对空气中的颗粒物进行拦截和吸附。</p>
          <p>当含尘空气通过过滤器时，较大的颗粒被外层材料拦截，较小的颗粒被内层纤维捕获，清洁空气从过滤器出口排出，实现高效过滤。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>外形尺寸<br/>(L×W×H mm)</th>
                <th>过滤等级</th>
                <th>过滤效率</th>
                <th>额定风量<br/>(m³/h)</th>
                <th>初阻力<br/>(Pa)</th>
                <th>终阻力<br/>(Pa)</th>
                <th>袋数<br/>(个)</th>
                <th>耐湿度<br/>(%RH)</th>
                <th>防火等级</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.type + spec.dimensions">
                <td>{{ spec.type }}</td>
                <td class="dimensions-cell">{{ spec.dimensions }}</td>
                <td>{{ spec.filterGrade }}</td>
                <td class="efficiency-cell">{{ spec.efficiency }}</td>
                <td>{{ spec.ratedAirFlow }}</td>
                <td>{{ spec.initialResistance }}</td>
                <td>{{ spec.finalResistance }}</td>
                <td>{{ spec.pockets }}</td>
                <td>{{ spec.humidity }}</td>
                <td class="fireproof-cell">{{ spec.fireproofClass }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications-grid">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <div class="app-icon">{{ app.icon }}</div>
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          技术优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in technicalAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品展示区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 产品特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.feature-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.65rem;
}

.specs-table th {
  background: #f8fafc;
  padding: 0.5rem 0.25rem;
  text-align: center;
  font-weight: 600;
  color: #1e3470;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
  font-size: 0.6rem;
}

.specs-table td {
  padding: 0.5rem 0.25rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  white-space: nowrap;
  font-size: 0.65rem;
}

.specs-table tbody tr:hover {
  background: #f8fafc;
}

.dimensions-cell {
  font-size: 0.55rem;
}

.efficiency-cell {
  font-size: 0.55rem;
}

.fireproof-cell {
  font-size: 0.5rem;
}

/* 应用场景网格 */
.applications-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 技术优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .specs-table {
    font-size: 0.7rem;
  }

  .specs-table th {
    font-size: 0.65rem;
    padding: 0.75rem 0.5rem;
  }

  .specs-table td {
    font-size: 0.7rem;
    padding: 0.75rem 0.5rem;
  }

  .dimensions-cell {
    font-size: 0.6rem;
  }

  .efficiency-cell {
    font-size: 0.6rem;
  }

  .fireproof-cell {
    font-size: 0.55rem;
  }

  .applications-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .specs-table {
    font-size: 0.75rem;
  }

  .specs-table th {
    font-size: 0.7rem;
  }

  .specs-table td {
    font-size: 0.75rem;
  }

  .dimensions-cell {
    font-size: 0.65rem;
  }

  .efficiency-cell {
    font-size: 0.65rem;
  }

  .fireproof-cell {
    font-size: 0.6rem;
  }
}
</style>
