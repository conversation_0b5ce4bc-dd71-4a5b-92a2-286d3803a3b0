import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'starmapTechHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/LOGO.jpeg',
      title: '星图AI名片'
    }
  },
  {
    path: '/project-intro',
    name: 'starmapTechProjectIntro',
    component: () => import('./views/ProjectIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/LOGO.jpeg',
      title: '星图AI名片 - 项目介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'starmapTechAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/LOGO.jpeg',
      title: '星图AI名片 - AI宣传员'
    }
  },
  {
    path: '/case-center',
    name: 'starmapTechCaseCenter',
    component: () => import('./views/CaseCenterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/LOGO.jpeg',
      title: '星图AI名片 - 产品中心'
    }
  },
  {
    path: '/case-center/weilaichengzhang',
    name: 'starmapTechWeilaichengzhang',
    component: () => import('./views/WeilaichengzhangDetailView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/LOGO.jpeg',
      title: '星图AI名片 - 未来成长'
    }
  },
  {
    path: '/case-center/tiyanke',
    name: 'starmapTechTiyanke',
    component: () => import('./views/TiyankeDetailView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/LOGO.jpeg',
      title: '星图AI名片 - 体验课'
    }
  },
  {
    path: '/case-center/shiguanglili',
    name: 'starmapTechShiguanglili',
    component: () => import('./views/ShiguangliliDetailView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/LOGO.jpeg',
      title: '星图AI名片 - AI时光礼物'
    }
  }
]

export default routes
