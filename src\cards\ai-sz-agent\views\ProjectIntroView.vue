<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  QuestionFilled, 
  User, 
  Histogram, 
  Star, 
  Connection, 
  Service,
  Promotion,
  OfficeBuilding,
  DataAnalysis,
  ArrowLeftBold,
  ArrowRightBold
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'
import { ref, onMounted } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/ai-sz-agent')
}

// 轮播相关
const activeChallenge = ref(0)
const activeFirstStage = ref(0)
const activeSecondStage = ref(0)
const activeService = ref(0)

const challenges = [
  {
    id: 1,
    title: '信息传递与政策解读"效率低、覆盖窄、理解难"？',
    solution: 'AI智能讲解与互动问答，7x24小时、多渠道、标准化、通俗化地传递核心信息。',
    icon: 'QuestionFilled'
  },
  {
    id: 2,
    title: '对外宣传与形象塑造"方式传统、吸引力弱、成本高"？',
    solution: '打造科技感、亲和力的AI数智代言人作为创新名片，高效、低成本地生产和传播优质内容。',
    icon: 'Promotion'
  },
  {
    id: 3,
    title: '政务服务与公众互动"响应慢、体验差、压力大"？',
    solution: '作为智能客服或导办助手，7x24小时在线提供标准化咨询解答与办事引导。',
    icon: 'Service'
  }
]

const firstStageScenarios = [
  {
    id: 1,
    title: '政府招商/文旅部门负责人',
    description: '"AI数智推介官，用科技感十足的方式讲好\'城市故事\'，政策亮点、投资环境、文旅资源一目了然，还能智能互动。这才是新时代的推介利器！"',
    value: '核心价值： 创新展示 + 智能互动 + 专业形象 = 提升吸引力。',
    icon: 'Promotion'
  },
  {
    id: 2,
    title: '政府对外服务窗口/政策研究部门骨干',
    description: '"AI数智推介官，把复杂的政策变成易懂的\'智能问答\'和\'结构化信息\'，7x24小时在线服务。不仅减轻了我们的工作压力，也让信息传递更精准高效。"',
    value: '核心价值： 智能解读 + 高效服务 + 精准传递 = 优化体验。',
    icon: 'Service'
  }
]

const secondStageScenarios = [
  {
    id: 1,
    title: '市/区宣传部、市长办公室负责人',
    description: '"AI数智代言人，不仅是一个新颖的形象，更能主演城市宣传片、运营新媒体账号、出席虚拟发布会，用\'活\'的IP全方位塑造与传播城市魅力，这正是我们需要的创新引擎！"',
    value: '核心价值： 品牌焕新 + 全媒传播 + 智能交互 = 影响力跃升。',
    icon: 'Promotion'
  },
  {
    id: 2,
    title: '政务服务中心、大数据局负责人',
    description: '"AI数智代言人，可以作为7x24小时在线的\'全能服务员\'，统一入口提供跨部门业务咨询、办事引导，甚至未来能结合大数据主动推送服务。这能极大提升服务体验和治理效能。"',
    value: '核心价值： 体验升级 + 智能协同 + 数据赋能 = 服务增效。',
    icon: 'DataAnalysis'
  }
]

const services = [
  {
    id: 1,
    title: '基础构建-打造专属AI数智代言人形象',
    items: [
      '依据您的品牌服务特性和应用需求，定制化设计与创建形象。',
      '为数字人配置语音识别(ASR)、自然语言处理(NLP)、高品质语音合成(TTS)等，使其具备流畅自然的交互基础。'
    ],
    icon: 'User'
  },
  {
    id: 2,
    title: '内容生产服务-帮您更快、更好的制作内容',
    items: [
      'AI宣传与营销内容制作：产品介绍、品牌故事、营销活动等，快速转化为由数智代言人出演的专业视频、短片或图文。',
      '日常沟通与信息播报制作：快速生成用于内外沟通的通知、公告、政策解读等内容。',
      '多模态内容延伸制作：支持H5页面元素、播客、MV、短剧等多样化内容形式。'
    ],
    icon: 'Promotion'
  },
  {
    id: 3,
    title: '数智员工定制-根据政务场景定制专属数智员工',
    items: [
      'AI智能宣传员：基于您的业务数据和知识库，打造能7x24小时在线、深度咨询、引导转化的"数智客服"。',
      '定制化企业内部数智员工构建：根据您的内部需求，定制数智方案专家、营销文案助手等，提升内部运营效率。'
    ],
    icon: 'Service'
  },
  {
    id: 4,
    title: '综合应用与整合赋能',
    items: [
      '多场景部署与系统集成：将AI数智代言人无缝部署到您的网站、App、小程序、社交媒体、线下大屏等。',
      '高级定制开发与持续优化服务。',
      '可靠的平台授权与技术维护保障。'
    ],
    icon: 'Connection'
  }
]

// 定义轮播类型
type CarouselType = 'challenge' | 'firstStage' | 'secondStage' | 'service'

// 触摸滑动相关变量
const touchStartX = ref(0)
const touchEndX = ref(0)
const isSwiping = ref(false)
const swipeThreshold = 50 // 滑动阈值，超过这个值才触发切换

// 处理触摸开始事件
const handleTouchStart = (event: TouchEvent, carouselType: CarouselType): void => {
  touchStartX.value = event.touches[0].clientX
  isSwiping.value = true
  if (carouselType) {
    // 这里不需要实际操作，只是为了消除TypeScript警告
  }
}

// 处理触摸移动事件
const handleTouchMove = (event: TouchEvent): void => {
  if (!isSwiping.value) return
  touchEndX.value = event.touches[0].clientX
}

// 处理触摸结束事件
const handleTouchEnd = (_event: TouchEvent, carouselType: CarouselType): void => {
  if (!isSwiping.value) return
  
  const swipeDistance = touchEndX.value - touchStartX.value
  
  // 判断是左滑还是右滑，并且滑动距离超过阈值
  if (Math.abs(swipeDistance) > swipeThreshold) {
    if (swipeDistance > 0) {
      // 右滑，显示上一个
      switch(carouselType) {
        case 'challenge':
          activeChallenge.value = (activeChallenge.value - 1 + challenges.length) % challenges.length
          break
        case 'firstStage':
          activeFirstStage.value = (activeFirstStage.value - 1 + firstStageScenarios.length) % firstStageScenarios.length
          break
        case 'secondStage':
          activeSecondStage.value = (activeSecondStage.value - 1 + secondStageScenarios.length) % secondStageScenarios.length
          break
        case 'service':
          activeService.value = (activeService.value - 1 + services.length) % services.length
          break
      }
    } else {
      // 左滑，显示下一个
      switch(carouselType) {
        case 'challenge':
          activeChallenge.value = (activeChallenge.value + 1) % challenges.length
          break
        case 'firstStage':
          activeFirstStage.value = (activeFirstStage.value + 1) % firstStageScenarios.length
          break
        case 'secondStage':
          activeSecondStage.value = (activeSecondStage.value + 1) % secondStageScenarios.length
          break
        case 'service':
          activeService.value = (activeService.value + 1) % services.length
          break
      }
    }
  }
  
  // 重置触摸状态
  isSwiping.value = false
}

// 自动轮播
const startAutoSlide = () => {
  setInterval(() => {
    activeChallenge.value = (activeChallenge.value + 1) % challenges.length
  }, 5000)
  
  setInterval(() => {
    activeFirstStage.value = (activeFirstStage.value + 1) % firstStageScenarios.length
  }, 6000)
  
  setInterval(() => {
    activeSecondStage.value = (activeSecondStage.value + 1) % secondStageScenarios.length
  }, 6000)
  
  setInterval(() => {
    activeService.value = (activeService.value + 1) % services.length
  }, 4000)
}

// 动态获取图标组件
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    QuestionFilled,
    User,
    Histogram,
    Star,
    Connection,
    Service,
    Promotion,
    OfficeBuilding,
    DataAnalysis
  }
  return iconMap[iconName] || null
}

// 添加手动切换轮播的方法
const prevSlide = (carouselType: CarouselType) => {
  switch(carouselType) {
    case 'challenge':
      activeChallenge.value = (activeChallenge.value - 1 + challenges.length) % challenges.length
      break
    case 'firstStage':
      activeFirstStage.value = (activeFirstStage.value - 1 + firstStageScenarios.length) % firstStageScenarios.length
      break
    case 'secondStage':
      activeSecondStage.value = (activeSecondStage.value - 1 + secondStageScenarios.length) % secondStageScenarios.length
      break
    case 'service':
      activeService.value = (activeService.value - 1 + services.length) % services.length
      break
  }
}

const nextSlide = (carouselType: CarouselType) => {
  switch(carouselType) {
    case 'challenge':
      activeChallenge.value = (activeChallenge.value + 1) % challenges.length
      break
    case 'firstStage':
      activeFirstStage.value = (activeFirstStage.value + 1) % firstStageScenarios.length
      break
    case 'secondStage':
      activeSecondStage.value = (activeSecondStage.value + 1) % secondStageScenarios.length
      break
    case 'service':
      activeService.value = (activeService.value + 1) % services.length
      break
  }
}

onMounted(() => {
  startAutoSlide()
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>项目介绍</h1>
    </div>

    <div class="content">
      <div class="project-intro-container">
        <!-- 面临挑战 -->
        <section class="section">
          <h2 class="section-title">
            <el-icon class="section-icon"><QuestionFilled /></el-icon>
            面临挑战？
          </h2>
          <div class="carousel-container"
               @touchstart="(e) => handleTouchStart(e, 'challenge')"
               @touchmove="handleTouchMove"
               @touchend="(e) => handleTouchEnd(e, 'challenge')">
            <div class="carousel-track" :style="{ transform: `translateX(-${activeChallenge * 100}%)` }">
              <div v-for="challenge in challenges" :key="challenge.id" class="challenge-card">
                <div class="card-icon-wrapper">
                  <el-icon class="card-icon">
                    <component :is="getIconComponent(challenge.icon)" />
                  </el-icon>
                </div>
                <h3 class="challenge-title">{{ challenge.title }}</h3>
                <p class="challenge-solution">{{ challenge.solution }}</p>
              </div>
            </div>
            <div class="carousel-controls">
              <div class="carousel-arrow carousel-arrow-left" @click="prevSlide('challenge')">
                <el-icon><ArrowLeftBold /></el-icon>
              </div>
              <div class="carousel-indicators">
                <span 
                  v-for="(challenge, index) in challenges" 
                  :key="challenge.id" 
                  class="indicator"
                  :class="{ active: index === activeChallenge }"
                  @click="activeChallenge = index"
                ></span>
              </div>
              <div class="carousel-arrow carousel-arrow-right" @click="nextSlide('challenge')">
                <el-icon><ArrowRightBold /></el-icon>
              </div>
            </div>
          </div>
        </section>

        <!-- 我们是谁 -->
        <section class="section">
          <h2 class="section-title">
            <el-icon class="section-icon"><User /></el-icon>
            我们是谁？
          </h2>
          <div class="about-us">
            <p class="highlight-text">"AI数智代言人"为您而来！我们致力于通过专属AI数字人，赋能政务创新。</p>
            <p class="quote">我们的定位："打造您的专属数智代言人，一个形象，全场景赋能，持续提升政务服务智慧与城市影响力。"</p>
            <p>我们并非提供单一的AI工具，而是通过业界领先的多模态交互AI引擎与高保真数字人定制技术，为您构建统一的、可进化的AI数字人形象，成为您对外宣传、对内服务的"数字代言人"和"智能助手"。</p>
          </div>
        </section>

        <!-- 核心方案 -->
        <section class="section">
          <h2 class="section-title">
            <el-icon class="section-icon"><Histogram /></el-icon>
            核心方案
          </h2>
          
          <!-- 第一阶段 -->
          <div class="solution-stage">
            <h3 class="stage-title">
              <el-icon><Promotion /></el-icon>
              第一阶段：AI数智推介官
            </h3>
            <p class="stage-definition">
              <strong>定义：</strong> 一款针对特定高频政务场景（如招商推介、政策咨询）的标准化、轻量级AI应用，是您快速体验AI赋能政务的"敲门砖"。
            </p>
            
            <div class="stage-scenarios">
              <h4 class="scenarios-title">服务对象与场景</h4>
              <div class="carousel-container"
                   @touchstart="(e) => handleTouchStart(e, 'firstStage')"
                   @touchmove="handleTouchMove"
                   @touchend="(e) => handleTouchEnd(e, 'firstStage')">
                <div class="carousel-track" :style="{ transform: `translateX(-${activeFirstStage * 100}%)` }">
                  <div v-for="scenario in firstStageScenarios" :key="scenario.id" class="scenario-card">
                    <h5 class="scenario-title">{{ scenario.title }}</h5>
                    <p class="scenario-description">{{ scenario.description }}</p>
                    <p class="scenario-value">{{ scenario.value }}</p>
                  </div>
                </div>
                <div class="carousel-controls">
                  <div class="carousel-arrow carousel-arrow-left" @click="prevSlide('firstStage')">
                    <el-icon><ArrowLeftBold /></el-icon>
                  </div>
                  <div class="carousel-indicators">
                    <span 
                      v-for="(scenario, index) in firstStageScenarios" 
                      :key="scenario.id" 
                      class="indicator"
                      :class="{ active: index === activeFirstStage }"
                      @click="activeFirstStage = index"
                    ></span>
                  </div>
                  <div class="carousel-arrow carousel-arrow-right" @click="nextSlide('firstStage')">
                    <el-icon><ArrowRightBold /></el-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 第二阶段 -->
          <div class="solution-stage">
            <h3 class="stage-title">
              <el-icon><DataAnalysis /></el-icon>
              第二阶段：AI数智代言人
            </h3>
            <p class="stage-definition">
              <strong>定义：</strong> 为您深度定制的、可进化的、能够全场景应用的AI数字人IP和智能化解决方案，它不仅仅是一个虚拟形象，更是一个集专属IP、智慧内核、多元能力于一体的AI数字人综合解决方案。
            </p>
            
            <div class="stage-scenarios">
              <h4 class="scenarios-title">服务对象与场景</h4>
              <div class="carousel-container"
                   @touchstart="(e) => handleTouchStart(e, 'secondStage')"
                   @touchmove="handleTouchMove"
                   @touchend="(e) => handleTouchEnd(e, 'secondStage')">
                <div class="carousel-track" :style="{ transform: `translateX(-${activeSecondStage * 100}%)` }">
                  <div v-for="scenario in secondStageScenarios" :key="scenario.id" class="scenario-card">
                    <h5 class="scenario-title">{{ scenario.title }}</h5>
                    <p class="scenario-description">{{ scenario.description }}</p>
                    <p class="scenario-value">{{ scenario.value }}</p>
                  </div>
                </div>
                <div class="carousel-controls">
                  <div class="carousel-arrow carousel-arrow-left" @click="prevSlide('secondStage')">
                    <el-icon><ArrowLeftBold /></el-icon>
                  </div>
                  <div class="carousel-indicators">
                    <span 
                      v-for="(scenario, index) in secondStageScenarios" 
                      :key="scenario.id" 
                      class="indicator"
                      :class="{ active: index === activeSecondStage }"
                      @click="activeSecondStage = index"
                    ></span>
                  </div>
                  <div class="carousel-arrow carousel-arrow-right" @click="nextSlide('secondStage')">
                    <el-icon><ArrowRightBold /></el-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 核心价值 -->
        <section class="section">
          <h2 class="section-title">
            <el-icon class="section-icon"><Star /></el-icon>
            核心价值
          </h2>
          <div class="value-grid">
            <div class="value-card">
              <h4>创新宣传</h4>
              <p>革新政务宣传模式，提升品牌影响力。</p>
            </div>
            <div class="value-card">
              <h4>优化服务</h4>
              <p>升级对民对企服务体验，提升满意度。</p>
            </div>
            <div class="value-card">
              <h4>降本增效</h4>
              <p>降低运营成本，全面赋能政务工作。</p>
            </div>
            <div class="value-card">
              <h4>数字资产</h4>
              <p>打造可持续进化、长期受益的数字IP。</p>
            </div>
          </div>
          
          <div class="value-grid">
            <div class="value-card">
              <h4>深度定制</h4>
              <p>形象、声音、知识、能力均可按需打造。</p>
            </div>
            <div class="value-card">
              <h4>永不塌房</h4>
              <p>形象稳定可控，规避真人代言风险。</p>
            </div>
            <div class="value-card">
              <h4>全时在线</h4>
              <p>7x24小时不间断服务与内容输出。</p>
            </div>
            <div class="value-card">
              <h4>价值永续</h4>
              <p>持续学习进化，长期创造价值。</p>
            </div>
          </div>
        </section>

        <!-- AI数智代言人一形万用 -->
        <section class="section">
          <h2 class="section-title">
            <el-icon class="section-icon"><Connection /></el-icon>
            AI数智代言人一形万用
          </h2>
          <div class="usage-container">
            <div class="usage-category">
              <h3 class="category-title">
                <el-icon><Promotion /></el-icon>
                对外宣传与品牌
              </h3>
              <ul class="usage-list">
                <li>城市/文旅推广： AI大使讲述城市故事，展示文旅魅力。</li>
                <li>招商引资顾问： AI顾问精准推介投资环境与机遇。</li>
                <li>新闻权威发布： AI发言人高效播报重要资讯。</li>
                <li>文化IP打造： AI主演城市宣传片、主题短剧。</li>
                <li>新媒体智能运营： AI主播/运营官玩转官方账号。</li>
              </ul>
            </div>
            
            <div class="usage-category">
              <h3 class="category-title">
                <el-icon><Service /></el-icon>
                对民/企智能服务
              </h3>
              <ul class="usage-list">
                <li>AI政务导办： 线上线下提供智能咨询与办事指引。</li>
                <li>AI政策解读： 复杂政策通俗化，7x24小时智能问答。</li>
              </ul>
            </div>
            
            <div class="usage-category">
              <h3 class="category-title">
                <el-icon><OfficeBuilding /></el-icon>
                对内工作与知识
              </h3>
              <ul class="usage-list">
                <li>AI业务助手： 辅助信息处理、材料初拟。</li>
                <li>AI政策内训： 标准化、互动式内部培训与知识共享。</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- 可提供服务详细介绍 -->
        <section class="section">
          <h2 class="section-title">
            <el-icon class="section-icon"><Service /></el-icon>
            可提供服务详细介绍
          </h2>
          <div class="services-carousel"
               @touchstart="(e) => handleTouchStart(e, 'service')"
               @touchmove="handleTouchMove"
               @touchend="(e) => handleTouchEnd(e, 'service')">
            <div class="carousel-track" :style="{ transform: `translateX(-${activeService * 100}%)` }">
              <div v-for="service in services" :key="service.id" class="service-card">
                <div class="card-icon-wrapper">
                  <el-icon class="card-icon">
                    <component :is="getIconComponent(service.icon)" />
                  </el-icon>
                </div>
                <h3 class="service-title">{{ service.title }}</h3>
                <ul class="service-items">
                  <li v-for="(item, index) in service.items" :key="index">{{ item }}</li>
                </ul>
              </div>
            </div>
            <div class="carousel-controls">
              <div class="carousel-arrow carousel-arrow-left" @click="prevSlide('service')">
                <el-icon><ArrowLeftBold /></el-icon>
              </div>
              <div class="carousel-indicators">
                <span 
                  v-for="(service, index) in services" 
                  :key="service.id" 
                  class="indicator"
                  :class="{ active: index === activeService }"
                  @click="activeService = index"
                ></span>
              </div>
              <div class="carousel-arrow carousel-arrow-right" @click="nextSlide('service')">
                <el-icon><ArrowRightBold /></el-icon>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #409EFF, #64b5f6);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem; /* 为底部标签栏留出空间 */
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
  max-width: 1200px;
}

.project-intro-container {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

/* 通用部分样式 */
.section {
  margin-bottom: 2.5rem;
}

.section-title {
  font-size: 1.5rem;
  color: #409EFF;
  margin-bottom: 1.2rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e1eeff;
  position: relative;
  display: flex;
  align-items: center;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, #409EFF, #64b5f6);
}

.section-icon {
  margin-right: 0.5rem;
  font-size: 1.2rem;
  color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 轮播组件样式 */
.carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease;
}

.carousel-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  margin: 0 1rem;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #d0d9e6;
  margin: 0 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.indicator.active {
  background-color: #409EFF;
}

.carousel-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s;
}

.carousel-arrow:hover {
  background-color: #409EFF;
  color: white;
}

.carousel-arrow .el-icon {
  font-size: 14px;
}

/* 卡片通用样式 */
.card-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409EFF, #64b5f6);
}

.card-icon {
  font-size: 1.5rem;
  color: white;
}

/* 挑战卡片样式 */
.challenge-card {
  flex: 0 0 100%;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #edf2f7;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.challenge-title {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 1rem;
}

.challenge-solution {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* 关于我们部分样式 */
.about-us {
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  border-left: 4px solid #409EFF;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.highlight-text {
  font-size: 1.1rem;
  color: #409EFF;
  font-weight: 500;
}

.quote {
  font-style: italic;
  color: #555;
  padding: 0.5rem 0;
  border-left: 2px solid #64b5f6;
  padding-left: 1rem;
  margin: 1rem 0;
}

/* 核心方案部分样式 */
.solution-stage {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #edf2f7;
}

.stage-title {
  font-size: 1.2rem;
  color: #409EFF;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.stage-title .el-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stage-definition {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(64, 158, 255, 0.05);
  border-radius: 0.5rem;
  border-left: 2px solid #409EFF;
}

.scenarios-title {
  font-size: 1rem;
  color: #555;
  margin-bottom: 1rem;
}

.scenario-card {
  flex: 0 0 100%;
  padding: 1.5rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid #edf2f7;
}

.scenario-title {
  font-size: 1rem;
  color: #409EFF;
  margin-bottom: 0.5rem;
}

.scenario-description {
  font-size: 0.9rem;
  color: #555;
  margin-bottom: 1rem;
  font-style: italic;
}

.scenario-value {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
  background-color: rgba(64, 158, 255, 0.05);
  padding: 0.5rem;
  border-radius: 0.25rem;
  width: 100%;
}

/* 核心价值部分样式 */
.value-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.value-card {
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid #edf2f7;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.value-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.15);
}

.value-card h4 {
  font-size: 1rem;
  color: #409EFF;
  margin: 0 0 0.5rem 0;
}

.value-card p {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
}

/* 一形万用部分样式 */
.usage-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.usage-category {
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #edf2f7;
}

.category-title {
  font-size: 1.1rem;
  color: #409EFF;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.category-title .el-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.usage-list {
  padding-left: 1.2rem;
  margin: 0;
}

.usage-list li {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #555;
  padding: 0.25rem 0;
}

/* 服务详情部分样式 */
.services-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 0.75rem;
}

.service-card {
  flex: 0 0 100%;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid #edf2f7;
}

.service-title {
  font-size: 1.1rem;
  color: #409EFF;
  margin-bottom: 1rem;
}

.service-items {
  padding-left: 1.2rem;
  margin: 0;
  text-align: left;
  width: 100%;
}

.service-items li {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #555;
  padding: 0.25rem 0;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .project-intro-container {
    padding: 2rem;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
  
  .challenge-title {
    font-size: 1.2rem;
  }
  
  .value-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .usage-container {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .usage-category {
    flex: 1 0 calc(33.333% - 1rem);
  }
}

@media (max-width: 767px) {
  .project-intro-container {
    padding: 1rem;
  }
  
  .section {
    margin-bottom: 2rem;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .challenge-card, .scenario-card, .service-card {
    padding: 1rem;
  }
  
  .challenge-title, .stage-title, .category-title, .service-title {
    font-size: 1rem;
  }
  
  .challenge-solution, .scenario-description, .value-card p, .usage-list li, .service-items li {
    font-size: 0.85rem;
  }
  
  .highlight-text {
    font-size: 1rem;
  }

  .card-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .card-icon {
    font-size: 1.2rem;
  }

  .carousel-arrow {
    display: none;
  }
}

/* 触摸滑动相关样式 */
.carousel-container, .services-carousel {
  touch-action: pan-y; /* 允许垂直滚动，但水平滑动会被捕获 */
  user-select: none; /* 防止文本被选中影响滑动体验 */
}

.carousel-track {
  will-change: transform; /* 优化动画性能 */
}

/* 动画效果 */
.section-title, .stage-title, .category-title, .value-card, .usage-category {
  transition: all 0.3s ease;
}

.section-title:hover, .stage-title:hover, .category-title:hover {
  color: #1976D2;
}

.challenge-card, .scenario-card, .service-card, .usage-category {
  transition: transform 0.3s, box-shadow 0.3s;
}

.challenge-card:hover, .scenario-card:hover, .service-card:hover, .usage-category:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.15);
}
</style>