<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 产品技术参数
const technicalSpecs = reactive([
  { 
    model: 'FLCM-648', 
    filterArea: '648', 
    velocity: '1.0-2.0', 
    airflow: '23000-46000', 
    bagCount: '648', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '54',
    chambers: '9',
    dimensions: '11200×3300×11800'
  },
  { 
    model: 'FLCM-720', 
    filterArea: '720', 
    velocity: '1.0-2.0', 
    airflow: '26000-52000', 
    bagCount: '720', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '60',
    chambers: '10',
    dimensions: '12400×3300×11800'
  },
  { 
    model: 'FLCM-864', 
    filterArea: '864', 
    velocity: '1.0-2.0', 
    airflow: '31000-62000', 
    bagCount: '864', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '72',
    chambers: '12',
    dimensions: '14800×3300×11800'
  },
  { 
    model: 'FLCM-1152', 
    filterArea: '1152', 
    velocity: '1.0-2.0', 
    airflow: '41000-83000', 
    bagCount: '1152', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '96',
    chambers: '16',
    dimensions: '19600×3300×11800'
  },
  { 
    model: 'FLCM-1440', 
    filterArea: '1440', 
    velocity: '1.0-2.0', 
    airflow: '52000-104000', 
    bagCount: '1440', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '120',
    chambers: '20',
    dimensions: '24400×3300×11800'
  },
  { 
    model: 'FLCM-1584', 
    filterArea: '1584', 
    velocity: '1.0-2.0', 
    airflow: '57000-114000', 
    bagCount: '1584', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '132',
    chambers: '22',
    dimensions: '26800×3300×11800'
  },
  { 
    model: 'FLCM-2160', 
    filterArea: '2160', 
    velocity: '1.0-2.0', 
    airflow: '78000-156000', 
    bagCount: '2160', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '180',
    chambers: '30',
    dimensions: '36800×3300×11800'
  },
  { 
    model: 'FLCM-2880', 
    filterArea: '2880', 
    velocity: '1.0-2.0', 
    airflow: '104000-208000', 
    bagCount: '2880', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '240',
    chambers: '40',
    dimensions: '48800×3300×11800'
  },
  { 
    model: 'FLCM-3600', 
    filterArea: '3600', 
    velocity: '1.0-2.0', 
    airflow: '130000-260000', 
    bagCount: '3600', 
    bagLength: '6',
    bagDiameter: '130',
    pulseValves: '300',
    chambers: '50',
    dimensions: '60800×3300×11800'
  }
])

// 性能特点
const productFeatures = reactive([
  {
    title: '大型除尘器',
    description: '该系列除尘器是一类大型除尘器，与其他除尘器的主要区别是体积大、滤袋长，滤袋长度可达6-9米。'
  },
  {
    title: '处理风量大',
    description: '处理风量大、清灰效果好、除尘效率高、运行可靠、维护方便、占地面积相对较小的大型除尘设备。'
  },
  {
    title: '广泛应用',
    description: '可广泛运用于冶金行业炼钢高炉、原料喷煤制备工厂、建材行业、电力、化工行业、炭黑、沥青混凝土搅拌、锅炉、烟气除尘等行业。'
  },
  {
    title: '离线清灰',
    description: '采用离线清灰技术，能够在不停机的情况下进行清灰维护，确保连续稳定运行。'
  },
  {
    title: '高效除尘',
    description: '除尘效率高达99.9%以上，出口粉尘浓度可控制在30mg/m³以下，满足严格的环保要求。'
  },
  {
    title: '自动化控制',
    description: '配备先进的自动化控制系统，可实现远程监控和自动运行，降低人工操作成本。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>长袋离线脉冲袋式除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/changdailixian.jpg" alt="长袋离线脉冲袋式除尘器" />
        </div>
        <div class="hero-content">
          <h2>长袋离线脉冲袋式除尘器</h2>
          <p class="product-subtitle">大型离线式脉冲袋式除尘器，适用于大风量、高浓度粉尘处理</p>
          <div class="product-intro">
            <p>该系列类除尘器是一类大型除尘器，与其他除尘器的主要区别是体积大、滤袋长，滤袋长度可达6-9米，它是一种处理风量大、清灰效果好、除尘效率高、运行可靠、维护方便、占地面积相对较小的大型除尘设备。</p>
            <p>本系列产品可广泛运用于冶金行业炼钢高炉、原料喷煤制备工厂、建材行业、电力、化工行业、炭黑、沥青混凝土搅拌、锅炉、烟气除尘等行业的粉尘治理和物料回收。</p>
            <p>采用离线清灰技术，分室结构设计，可实现在线维护，确保设备连续稳定运行，是大型工业企业粉尘治理的理想选择。</p>
          </div>
        </div>
      </div>

      <!-- 性能特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          性能特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术参数
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>过滤面积<br/>(m²)</th>
                <th>过滤风速<br/>(m/min)</th>
                <th>处理风量<br/>(m³/h)</th>
                <th>滤袋数量<br/>(条)</th>
                <th>滤袋长度<br/>(m)</th>
                <th>滤袋直径<br/>(mm)</th>
                <th>脉冲阀数量<br/>(个)</th>
                <th>分室数<br/>(个)</th>
                <th>外形尺寸<br/>(长×宽×高mm)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.filterArea }}</td>
                <td>{{ spec.velocity }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.bagCount }}</td>
                <td>{{ spec.bagLength }}</td>
                <td>{{ spec.bagDiameter }}</td>
                <td>{{ spec.pulseValves }}</td>
                <td>{{ spec.chambers }}</td>
                <td>{{ spec.dimensions }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🌪️</div>
            <div class="indicator-content">
              <h4>除尘效率</h4>
              <div class="indicator-value">≥99.9%</div>
              <p>出口浓度≤30mg/m³</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">⚡</div>
            <div class="indicator-content">
              <h4>处理风量</h4>
              <div class="indicator-value">23000-260000</div>
              <p>m³/h超大风量</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📏</div>
            <div class="indicator-content">
              <h4>滤袋长度</h4>
              <div class="indicator-value">6-9米</div>
              <p>超长滤袋设计</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔧</div>
            <div class="indicator-content">
              <h4>离线清灰</h4>
              <div class="indicator-value">分室结构</div>
              <p>不停机维护</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📊</div>
            <div class="indicator-content">
              <h4>过滤面积</h4>
              <div class="indicator-value">648-3600m²</div>
              <p>大面积过滤</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🏗️</div>
            <div class="indicator-content">
              <h4>分室数量</h4>
              <div class="indicator-value">9-50室</div>
              <p>模块化设计</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🏗️</div>
            <div class="advantage-content">
              <h4>大型化设计</h4>
              <p>体积大、滤袋长，滤袋长度可达6-9米，是专为大风量工况设计的大型除尘设备，处理能力强，适应性广。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔄</div>
            <div class="advantage-content">
              <h4>离线清灰技术</h4>
              <p>采用分室离线清灰技术，可在不停机的情况下进行清灰维护，确保设备连续稳定运行，提高生产效率。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <div class="advantage-content">
              <h4>超大处理风量</h4>
              <p>处理风量范围23000-260000m³/h，能够满足大型工业企业的粉尘治理需求，适用于各种高浓度粉尘工况。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <div class="advantage-content">
              <h4>高效除尘性能</h4>
              <p>除尘效率高达99.9%以上，出口粉尘浓度可控制在30mg/m³以下，满足最严格的环保排放标准要求。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔧</div>
            <div class="advantage-content">
              <h4>维护方便</h4>
              <p>模块化分室设计，维护方便，可实现在线检修，降低维护成本，延长设备使用寿命。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📊</div>
            <div class="advantage-content">
              <h4>占地面积小</h4>
              <p>相对于处理风量，占地面积较小，结构紧凑，布局合理，适合各种工业厂房环境。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications">
          <div class="application-item">
            <div class="app-icon">⚒️</div>
            <h4>冶金行业</h4>
            <p>炼钢高炉、原料喷煤制备工厂粉尘治理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🏗️</div>
            <h4>建材行业</h4>
            <p>水泥厂、石灰厂等大型建材生产线粉尘处理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">⚡</div>
            <h4>电力行业</h4>
            <p>火力发电厂锅炉烟气除尘系统</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🧪</div>
            <h4>化工行业</h4>
            <p>化工原料生产、炭黑生产粉尘治理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🛣️</div>
            <h4>沥青混凝土</h4>
            <p>沥青混凝土搅拌站粉尘收集处理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🔥</div>
            <h4>锅炉烟气</h4>
            <p>各类工业锅炉烟气除尘净化</p>
          </div>
          <div class="application-item">
            <div class="app-icon">♻️</div>
            <h4>物料回收</h4>
            <p>各行业粉尘治理和物料回收系统</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🏭</div>
            <h4>大型工业</h4>
            <p>各类大型工业企业粉尘综合治理</p>
          </div>
        </div>
      </div>

      <!-- 工程案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          工程案例
        </h3>
        <div class="project-cases">
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/19-bktwk/20231221134457.jpg" alt="长袋离线脉冲袋式除尘器工程案例3" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/19-bktwk/20231221134411.jpg" alt="长袋离线脉冲袋式除尘器工程案例2" />
          </div>
           <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/19-bktwk/1716906003464.jpg" alt="长袋离线脉冲袋式除尘器工程案例1" />
          </div>
          <div class="case-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/19-bktwk/20240528221555.jpg" alt="长袋离线脉冲袋式除尘器工程案例4" />
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.3;
  font-size: 0.7rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.55rem;
  min-width: 1200px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.5rem 0.3rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.5rem;
  line-height: 1.1;
  border: 1px solid #2563eb;
}

.specs-table td {
  padding: 0.5rem 0.3rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.55rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 产品优势 */
.advantages {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 应用场景 */
.applications {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-item {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
}

.application-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 工程案例样式 */
.project-cases {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.case-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.case-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.case-item img {
  width: 100%;
  height: auto;
  min-height: 200px;
  object-fit: cover; /* 确保图片完全展示 */
  background-color: #f8fafc;
  transition: transform 0.3s ease;
}

.case-item:hover img {
  transform: scale(1.02);
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .feature-card p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .specs-table {
    font-size: 0.65rem;
  }

  .specs-table th {
    padding: 0.75rem 0.5rem;
    font-size: 0.6rem;
  }

  .specs-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.65rem;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .advantages {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .application-item {
    padding: 1.5rem;
  }

  .application-item h4 {
    font-size: 1.1rem;
  }

  .application-item p {
    font-size: 0.9rem;
  }

  /* 平板端工程案例样式 */
  .project-cases {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .case-item img {
    min-height: 250px;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .applications {
    grid-template-columns: repeat(4, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.7rem;
  }

  .specs-table th {
    font-size: 0.65rem;
  }

  .specs-table td {
    font-size: 0.7rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  /* 桌面端工程案例样式 */
  .project-cases {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .case-item img {
    min-height: 300px;
  }
}
</style>
