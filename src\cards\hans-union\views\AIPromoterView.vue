<script setup lang="ts">
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { 
  ArrowLeft, 
  Reading, 
  Timer, 
  User, 
  ArrowRight, 
  Collection,
  Phone,
  Location
} from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

// 返回首页
const goBack = () => {
  router.push('/card/hans-union')
}

// 与AI对话（暂时留空，后续可配置）
const chatWithAI = () => {
  // TODO: 配置汉氏联合AI对话链接
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=rge53qfzrjmgg0isyzwedtyp')
}

// 复制电话号码
const copyPhone = () => {
  navigator.clipboard.writeText('4009158877')
  alert('电话号码已复制到剪贴板')
}

// 拨打电话
const callPhone = () => {
  window.location.href = 'tel:4009158877'
}

// 打开地图导航
const openMap = () => {
  // 使用高德地图导航 - 只使用地址唤醒地图服务
  const address = '江西省上饶市经济技术开发区滨江商务中心·汉氏联合健康科技园'
  // 使用地址搜索而非精确坐标
  const url = `https://uri.amap.com/search?keyword=${encodeURIComponent(address)}&src=mypage`
  window.open(url, '_blank')
}

// 打字机效果相关变量
const welcomeText = ref('')
const fullText = '您好，我是汉氏联合的AI宣传员"小T"，很高兴为您服务。想了解我们的产品能力、成功案例，或是探讨项目合作？请直接输入您的问题，我会7x24小时为您解答。'
const typingSpeed = 50
let currentIndex = 0
let typingTimer: number | null = null

// 打字机效果实现
const typeText = () => {
  if (currentIndex < fullText.length) {
    welcomeText.value += fullText.charAt(currentIndex)
    currentIndex++
    typingTimer = setTimeout(typeText, typingSpeed) as unknown as number
  }
}

// 页面挂载时设置标题并启动打字机效果
onMounted(() => {
  document.title = '汉氏联合AI名片 - AI宣传员'
  setTimeout(() => {
    typeText()
  }, 500)
})

// 路由离开前清理定时器
onBeforeRouteLeave(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<template>
  <div class="view-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>AI宣传员：小T</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <div class="ai-promoter-container">
        <!-- AI宣传员卡片 - 左图右文布局 -->
        <div class="ai-card">
          <div class="ai-card-content">
            <!-- 左侧图片区域 - 使用img标签展示数字人上半身 -->
            <div class="ai-image-section">
              <img 
                src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/szr.png" 
                alt="汉氏联合AI宣传员小T" 
                class="digital-human-image"
              />
            </div>
            
            <!-- 右侧内容区域 -->
            <div class="ai-content">
              <h2>小T <span class="badge">AI 宣传员</span></h2>
              <div class="typing-container">
                <p class="welcome-text">
                  <template v-if="welcomeText">
                    <span v-for="(line, index) in welcomeText.split('\n')" :key="index">
                      {{ line }}<br v-if="index < welcomeText.split('\n').length - 1">
                    </span>
                  </template>
                  <span class="cursor" v-if="welcomeText.length < fullText.length">|</span>
                </p>
              </div>
              <div class="action-container">
                <el-button type="primary" class="chat-btn" @click="chatWithAI">
                  立即对话
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 能力特色展示 -->
        <div class="features-section">
          <h3>我的能力</h3>
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <h4>细胞技术专家</h4>
              <p>掌握汉氏联合全面的细胞存储与治疗技术知识</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon speed-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <h4>7x24小时在线</h4>
              <p>随时解答您的健康咨询，无需等待</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <h4>精准专业解答</h4>
              <p>为您提供详细、专业的细胞技术相关信息</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon personalized-icon">
                <el-icon><User /></el-icon>
              </div>
              <h4>个性化服务</h4>
              <p>根据您的需求提供定制化健康解决方案</p>
            </div>
          </div>
        </div>

        <!-- 联系方式 -->
        <div class="contact-section">
          <h3>联系我们</h3>
          <div class="contact-simple">
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Phone /></el-icon>
                <span>客服热线：</span>
              </div>
              <div class="contact-simple-value">
                <span class="phone-number">4009158877</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="copyPhone">复制</button>
                  <button class="btn-simple" @click="callPhone">拨打</button>
                </div>
              </div>
            </div>
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Location /></el-icon>
                <span>中心地址：</span>
              </div>
              <div class="contact-simple-value">
                <span class="address">江西省上饶市经济技术开发区滨江商务中心·汉氏联合健康科技园</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="openMap">导航</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<style scoped>
/* 主容器样式 */
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

/* 顶部导航栏样式 */
.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

/* 返回按钮样式 */
.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

/* 标题样式 */
.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

/* 内容区域 */
.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem; /* 为底部标签栏留出空间 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* AI宣传员容器 */
.ai-promoter-container {
  padding: 1rem;
}

/* AI卡片样式 */
.ai-card {
  background: linear-gradient(135deg, rgba(15, 157, 168, 0.05), rgba(31, 181, 196, 0.05));
  border-radius: 1rem;
  padding: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(15, 157, 168, 0.1);
  border: 1px solid rgba(15, 157, 168, 0.2);
  overflow: hidden;
}

/* 左图右文布局容器 */
.ai-card-content {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  min-height: 200px;
}

/* 左侧图片区域 - 参考福运环保的设计 */
.ai-image-section {
  flex: 0 0 40%;
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  margin-right: 1rem;
  /* 增加高度，更好地展示数字人上半身 */
  height: 300px;
}

/* 数字人图片样式 - 参考福运环保的设计 */
.digital-human-image {
  width: 100%;
  height: 100%;
  /* 调整图片缩放和位置，更聚焦于上半身，参考福运环保的设计 */
  object-fit: cover;
  object-position: center 20%;
  border-radius: 0.5rem;
  transition: transform 0.3s ease;
}

.digital-human-image:hover {
  transform: scale(1.05);
}

/* AI内容区域 */
.ai-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0.5rem;
}

.ai-content h2 {
  color: #0f9da8;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  text-align: left;
}

/* 徽章样式 */
.badge {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.7rem;
  margin-left: 0.5rem;
}

/* 打字机容器 */
.typing-container {
  margin-bottom: 1.5rem;
}

/* 欢迎文本样式 */
.welcome-text {
  color: #666;
  line-height: 1.6;
  font-size: 0.9rem;
  text-align: left;
  margin: 0;
}

/* 光标样式 */
.cursor {
  color: #0f9da8;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 操作按钮容器 */
.action-container {
  text-align: left;
  margin-top: auto;
}

/* 对话按钮样式 */
.chat-btn {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  border: none;
  padding: 0.6rem 1.5rem;
  font-size: 0.9rem;
  border-radius: 2rem;
  box-shadow: 0 4px 15px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(15, 157, 168, 0.4);
}

/* 特色功能区域 */
.features-section {
  margin-bottom: 2rem;
}

.features-section h3 {
  color: #0f9da8;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

/* 联系方式 */
.contact-section {
  margin-bottom: 2rem;
}

.contact-section h3 {
  color: #0f9da8;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

/* 移动端响应式样式 */
@media (max-width: 767px) {
  .ai-card-content {
    flex-direction: column;
  }
  
  .ai-image-section {
    flex: 0 0 250px;
    margin-right: 0;
    margin-bottom: 1rem;
    /* 确保移动端高度足够 */
    height: 250px;
  }
  
  .digital-human-image {
    /* 移动端上调整图片位置，更聚焦于上半身 */
    object-position: center 10%;
  }
  
  .ai-content h2 {
    text-align: center;
    margin-top: 0.5rem;
  }
  
  .welcome-text {
    text-align: center;
  }
  
  .action-container {
    text-align: center;
    margin-top: 1rem;
  }
}

/* 功能网格布局 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

/* 功能项样式 */
.feature-item {
  background: white;
  padding: 1rem;
  border-radius: 0.8rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(15, 157, 168, 0.1);
}

/* 功能图标样式 */
.feature-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.5rem;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
}

.feature-item h4 {
  color: #0f9da8;
  font-size: 0.9rem;
  margin: 0.5rem 0;
}

.feature-item p {
  color: #666;
  font-size: 0.8rem;
  line-height: 1.4;
  margin: 0;
}

/* 联系方式区域 */
.contact-section {
  background: white;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(15, 157, 168, 0.1);
}

.contact-section h3 {
  color: #0f9da8;
  text-align: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

/* 联系信息样式 - 简洁版 */
.contact-simple {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-simple-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.contact-simple-row:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.contact-simple-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
  color: #374151;
  min-width: 120px;
}

.contact-simple-icon {
  font-size: 1.25rem;
  color: #0f9da8;
}

.contact-simple-value {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  justify-content: space-between;
}

.phone-number,
.address,
.website-url {
  color: #0f9da8;
  font-weight: 500;
  font-size: 0.95rem;
}

.contact-simple-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-simple {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-simple:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(15, 157, 168, 0.3);
}

/* 移动端响应式设计 */
@media (max-width: 767px) {
  .content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .contact-simple-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .contact-simple-value {
    width: 100%;
    justify-content: space-between;
  }

  .contact-simple-label {
    min-width: auto;
  }

  .address {
    font-size: 0.85rem;
    line-height: 1.4;
  }
}

/* 桌面端响应式设计 */
@media (min-width: 768px) {
  .content {
    padding: 5rem 2rem 5rem;
  }
  
  .ai-card {
    padding: 2rem;
  }
  
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>