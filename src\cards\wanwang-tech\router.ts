import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'wanWangKeJiHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片'
    }
  },
  {
    path: '/company-intro',
    name: 'wanWangKeJiCompanyIntro',
    component: () => import('./views/CompanyIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 企业介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'wanWangKeJiAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - AI宣传员'
    }
  },
  {
    path: '/case-center',
    name: 'wanWangKeJiCaseCenter',
    component: () => import('./views/CaseCenterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 产品中心'
    }
  },
  // 重点人员管控平台专门页面（必须放在动态路由之前）
  {
    path: '/zhongdian-renyuan-guankong-detail',
    name: 'wanWangKeJiZhongdianRenyuanGuankong',
    component: () => import('./views/ZhongdianRenyuanGuankongView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 涉诈人员动态管控平台'
    }
  },
  // 智慧党建云平台专门页面
  {
    path: '/zhihui-dangjian-yun-detail',
    name: 'wanWangKeJiZhihuiDangjianYun',
    component: () => import('./views/ZhihuiDangjianYunView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 智慧党建云平台'
    }
  },
  // 智慧工会服务云平台专门页面
  {
    path: '/zhihui-gonghui-detail',
    name: 'wanWangKeJiZhihuiGonghui',
    component: () => import('./views/ZhihuiGonghuiView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 智慧工会服务云平台'
    }
  },
  // "敲门嫂"钢城红信息化平台专门页面
  {
    path: '/qiaomensao-detail',
    name: 'wanWangKeJiQiaomenSao',
    component: () => import('./views/QiaomenSaoView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - "敲门嫂"钢城红信息化平台'
    }
  },
  // 反诈研判系统专门页面
  {
    path: '/fanzha-yanpan-detail',
    name: 'wanWangKeJiFanzhaYanpan',
    component: () => import('./views/FanzhaYanpanView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 反诈研判系统'
    }
  },
  // 疫情防控实战平台专门页面
  {
    path: '/yiqing-fangkong-detail',
    name: 'wanWangKeJiYiqingFangkong',
    component: () => import('./views/YiqingFangkongView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 疫情防控实战平台'
    }
  },
  // 消防安全管理系统专门页面
  {
    path: '/xiaofang-anquan-detail',
    name: 'wanWangKeJiXiaofangAnquan',
    component: () => import('./views/XiaofangAnquanView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 消防安全管理系统'
    }
  },
  // 舆情督办系统专门页面
  {
    path: '/yuqing-duban-detail',
    name: 'wanWangKeJiYuqingDuban',
    component: () => import('./views/YuqingDubanView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 舆情督办系统'
    }
  },
  // 阳光招采平台专门页面
  {
    path: '/yangguang-zhaocai-detail',
    name: 'wanWangKeJiYangguangZhaocai',
    component: () => import('./views/YangguangZhaocaiView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 阳光招采平台'
    }
  },
  // 平安义警大数据管理平台专门页面
  {
    path: '/pingan-yijing-detail',
    name: 'wanWangKeJiPinganYijing',
    component: () => import('./views/PinganYijingView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 平安义警大数据管理平台'
    }
  },
  // 全域旅游智慧地图管理系统专门页面
  {
    path: '/quanyu-lvyou-detail',
    name: 'wanWangKeJiQuanyuLvyou',
    component: () => import('./views/QuanyuLvyouView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 全域旅游智慧地图管理系统'
    }
  },
  // 三重一大信息监管平台专门页面
  {
    path: '/sanzhong-yida-detail',
    name: 'wanWangKeJiSanzhongYida',
    component: () => import('./views/SanzhongYidaView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - "三重一大"信息监管平台'
    }
  },
  // 三甲医院门户网站管理系统专门页面
  {
    path: '/sanjia-yiyuan-detail',
    name: 'wanWangKeJiSanjiaYiyuan',
    component: () => import('./views/SanjiaYiyuanView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 三甲医院门户网站管理系统'
    }
  },
  // 上饶市信州区人民政府官网专门页面
  {
    path: '/shangrao-xinzhou-zhengfu-detail',
    name: 'wanWangKeJiShangRaoXinZhouZhengFu',
    component: () => import('./views/ShangRaoXinZhouZhengFuView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 上饶市信州区人民政府官网'
    }
  },
  // 广丰区人民政府官网专门页面
  {
    path: '/guangfeng-zhengfu-detail',
    name: 'wanWangKeJiGuangFengZhengFu',
    component: () => import('./views/GuangFengZhengFuView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 广丰区人民政府官网'
    }
  },
  // 上饶国控投资集团有限公司官网专门页面
  {
    path: '/guokong-touzi-detail',
    name: 'wanWangKeJiGuoKongTouZi',
    component: () => import('./views/GuoKongTouZiView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 上饶国控投资集团官网'
    }
  },
  // 上饶文旅集团官网专门页面
  {
    path: '/shangrao-wenlv-detail',
    name: 'wanWangKeJiShangRaoWenLv',
    component: () => import('./views/ShangRaoWenLvView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 上饶文旅集团官网'
    }
  },
  // 广信工投集团官网专门页面
  {
    path: '/guangxin-gongtou-detail',
    name: 'wanWangKeJiGuangXinGongTou',
    component: () => import('./views/GuangXinGongTouView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 广信工投集团官网'
    }
  },
  // 上饶市人民医院官网专门页面
  {
    path: '/shangrao-renmin-yiyuan-detail',
    name: 'wanWangKeJiShangRaoRenMinYiYuan',
    component: () => import('./views/ShangRaoRenMinYiYuanView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 上饶市人民医院官网'
    }
  },
  // 上饶中学官网专门页面
  {
    path: '/shangrao-zhongxue-detail',
    name: 'wanWangKeJiShangRaoZhongXue',
    component: () => import('./views/ShangRaoZhongXueView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 上饶中学官网'
    }
  },
  // 上饶卫生学校官网专门页面
  {
    path: '/shangrao-weisheng-xuexiao-detail',
    name: 'wanWangKeJiShangRaoWeiShengXueXiao',
    component: () => import('./views/ShangRaoWeiShengXueXiaoView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 上饶卫生学校官网'
    }
  },
  // 产品介绍页面路由（动态路由放在最后）

]

export default routes
