import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'fuyunEnvHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片'
    }
  },
  {
    path: '/company-intro',
    name: 'fuyunEnvCompanyIntro',
    component: () => import('./views/CompanyIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 公司介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'fuyunEnvAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - AI宣传员'
    }
  },
  {
    path: '/product-center',
    name: 'fuyunEnvProductCenter',
    component: () => import('./views/ProductCenterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 产品中心'
    }
  },
  {
    path: '/product/electric-shovel-dust-collector',
    name: 'fuyunEnvElectricShovelDustCollector',
    component: () => import('./views/ElectricShovelDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 电铲除尘器'
    }
  },
  {
    path: '/product/drilling-rig-dust-collector',
    name: 'fuyunEnvDrillingRigDustCollector',
    component: () => import('./views/DrillingRigDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 钻机除尘器'
    }
  },
  {
    path: '/product/self-cleaning-filter-dust-collector',
    name: 'fuyunEnvSelfCleaningFilterDustCollector',
    component: () => import('./views/SelfCleaningFilterDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 自洁式滤筒除尘器'
    }
  },
  {
    path: '/product/integrated-welding-fume-dust-collector',
    name: 'fuyunEnvIntegratedWeldingFumeDustCollector',
    component: () => import('./views/IntegratedWeldingFumeDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 集成式焊接烟尘除尘器'
    }
  },
  {
    path: '/product/cnc-cutting-dust-collector',
    name: 'fuyunEnvCNCCuttingDustCollector',
    component: () => import('./views/CNCCuttingDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 数控(激光、等离子)切割除尘器'
    }
  },
  {
    path: '/product/welding-fume-purifier',
    name: 'fuyunEnvWeldingFumePurifier',
    component: () => import('./views/WeldingFumePurifierView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 焊接烟尘净化器'
    }
  },
  {
    path: '/product/pulse-bag-dust-collector',
    name: 'fuyunEnvPulseBagDustCollector',
    component: () => import('./views/PulseBagDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 脉冲单机布袋除尘器'
    }
  },
  {
    path: '/product/long-bag-offline-dust-collector',
    name: 'fuyunEnvLongBagOfflineDustCollector',
    component: () => import('./views/LongBagOfflineDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 长袋离线脉冲袋式除尘器'
    }
  },
  {
    path: '/product/air-conditioning-purifier',
    name: 'fuyunEnvAirConditioningPurifier',
    component: () => import('./views/AirConditioningPurifierView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 组合式空调机组净化器'
    }
  },
  {
    path: '/product/grinding-dust-collector',
    name: 'fuyunEnvGrindingDustCollector',
    component: () => import('./views/GrindingDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 打磨除尘器'
    }
  },
  {
    path: '/product/cyclone-dust-collector',
    name: 'fuyunEnvCycloneDustCollector',
    component: () => import('./views/CycloneDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 旋风除尘器'
    }
  },
  {
    path: '/product/woodworking-dust-collector',
    name: 'fuyunEnvWoodworkingDustCollector',
    component: () => import('./views/WoodworkingDustCollectorView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 木工除尘器'
    }
  },
  {
    path: '/product/vocs-catalytic-combustion',
    name: 'fuyunEnvVocsCatalyticCombustion',
    component: () => import('./views/VocsCatalyticCombustionView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - VOCs催化燃烧系统'
    }
  },
  {
    path: '/product/activated-carbon-uv-purifier',
    name: 'fuyunEnvActivatedCarbonUvPurifier',
    component: () => import('./views/ActivatedCarbonUvPurifierView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 活性炭-UV光氧废气净化器'
    }
  },
  {
    path: '/product/polyester-fiber-filter-cartridge',
    name: 'fuyunEnvPolyesterFiberFilterCartridge',
    component: () => import('./views/PolyesterFiberFilterCartridgeView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 聚酯纤维除尘滤筒'
    }
  },
  {
    path: '/product/wood-pulp-fiber-filter-cartridge',
    name: 'fuyunEnvWoodPulpFiberFilterCartridge',
    component: () => import('./views/WoodPulpFiberFilterCartridgeView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 木浆纤维除尘滤筒'
    }
  },
  {
    path: '/product/primary-medium-efficiency-bag-filter',
    name: 'fuyunEnvPrimaryMediumEfficiencyBagFilter',
    component: () => import('./views/PrimaryMediumEfficiencyBagFilterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 初中效(袋式)过滤器'
    }
  },
  {
    path: '/product/high-efficiency-filter',
    name: 'fuyunEnvHighEfficiencyFilter',
    component: () => import('./views/HighEfficiencyFilterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 高效过滤器'
    }
  },
  {
    path: '/product/plate-primary-filter',
    name: 'fuyunEnvPlatePrimaryFilter',
    component: () => import('./views/PlatePrimaryFilterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 板式初效过滤器'
    }
  },
  {
    path: '/product/dust-filter-bag',
    name: 'fuyunEnvDustFilterBag',
    component: () => import('./views/DustFilterBagView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 除尘布袋'
    }
  },
  {
    path: '/product/dust-bag-cage',
    name: 'fuyunEnvDustBagCage',
    component: () => import('./views/DustBagCageView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 除尘布袋骨架'
    }
  },
  {
    path: '/service/environmental-operation-maintenance',
    name: 'fuyunEnvEnvironmentalOperationMaintenance',
    component: () => import('./views/EnvironmentalOperationMaintenanceView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 环保项目运维'
    }
  },
  {
    path: '/service/environmental-engineering-installation',
    name: 'fuyunEnvEnvironmentalEngineeringInstallation',
    component: () => import('./views/EnvironmentalEngineeringInstallationView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 环保工程安装'
    }
  },
  {
    path: '/service/dust-waste-gas-treatment-design',
    name: 'fuyunEnvDustWasteGasTreatmentDesign',
    component: () => import('./views/DustWasteGasTreatmentDesignView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/logo.png',
      title: '福运环保AI名片 - 粉尘与废气综合治理设计方案'
    }
  }
]

export default routes
