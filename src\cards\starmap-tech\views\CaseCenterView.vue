<script setup lang="ts">
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar.vue'
import { ArrowLeft } from '@element-plus/icons-vue'
const router = useRouter()

const goBack = () => {
  router.push('/card/starmap-tech')
}
const goToProductDetail = (productId: string) => {
  router.push(`/card/starmap-tech/case-center/${productId}`)
}
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品中心</h1>
    </div>

    <div class="content">
          <!-- 产品列表 -->
      <div class="section">
        <div class="card">
          <h2>我们的产品</h2>
          <div class="products-grid">
            <!-- 星图AI未来成长线下中心 -->
            <div class="product-card" @click="goToProductDetail('weilaichengzhang')">
              <div class="product-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/chengzhangzhongxinfengmian.jpeg" alt="星图AI未来成长线下中心s" />
              </div>
              <div class="product-info">
                <h3>星图AI未来成长线下中心</h3>
                <p>在真实的玩乐中驾驭AI，创造孩子的无限可能。</p>
                <div class="product-action">
                </div>
              </div>
            </div>

            <!-- 《你好，我的AI新朋友！》AI魔法体验课 -->
            <div class="product-card" @click="goToProductDetail('tiyanke')">
              <div class="product-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/AIxinpengyoufengmian.jpeg" alt="《你好，我的AI新朋友！》AI魔法体验课" />
              </div>
              <div class="product-info">
                <h3>《你好，我的AI新朋友！》</h3>
                <p>亲手创造，与AI成为可以拥抱的好朋友</p>
                <div class="product-action">
                </div>
              </div>
            </div>

            <!-- 《AI时光礼物》情感创作工坊 -->
            <div class="product-card" @click="goToProductDetail('shiguanglili')">
              <div class="product-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/KePuHuoDong/ganenhuodongfenxiangtu.jpg" alt="《AI时光礼物》情感创作工坊" />
              </div>
              <div class="product-info">
                <h3>《AI时光礼物》情感创作工坊</h3>
                <p>用AI的魔力，把孩子心中的爱变成看得见的礼物。</p>
                <div class="product-action">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #316abc, #4a8bc2);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  padding-top: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  margin-bottom: 1rem;
}

.card {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card h2 {
  color: #4a8bc2;
  margin: 0 0 1rem 0;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
}

.products-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.product-card {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.1);
  border-color: #4a8bc2;
}

.product-image {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  overflow: hidden;
  margin: 1rem;
  border-radius: 0.5rem;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-info {
  flex: 1;
  padding: 1rem 1rem 1rem 0;
}

.product-info h3 {
  color: #4a8bc2;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.product-info p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 0.75rem 0;
}

.product-action {
  display: flex;
  justify-content: flex-end;
}

.view-detail {
  color: #4a8bc2;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .card {
    padding: 1.5rem;
  }

  .card h2 {
    font-size: 1.3rem;
    margin: 0 0 1.5rem 0;
  }

  .product-image {
    width: 150px;
    height: 150px;
    margin: 1.5rem;
  }

  .product-info {
    padding: 1.5rem 1.5rem 1.5rem 0;
  }

  .product-info h3 {
    font-size: 1.2rem;
  }

  .product-info p {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
</style>
