<template>
  <div class="page">
    <!-- 顶部导航 -->
    <div class="header">
      <button @click="goBack" class="back-btn">‹ 返回</button>
      <h1>产品中心</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 产品列表 -->
      <div class="section">
        <div class="card">
          <h2>我们的产品</h2>
          <div class="products-grid">
            <!-- 一码通上饶 -->
            <div class="product-card" @click="goToProductDetail('yimatong')">
              <div class="product-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/yimatongshangrao.jpg" alt="一码通上饶" />
              </div>
              <div class="product-info">
                <h3>一码通上饶</h3>
                <p>聚合全城优惠，点亮上饶生活。</p>
                <div class="product-action">
                </div>
              </div>
            </div>

            <!-- 信州春大舞台 -->
            <div class="product-card" @click="goToProductDetail('xinzhouchun')">
              <div class="product-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xinzhouchun.png" alt="信州春大舞台" />
              </div>
              <div class="product-info">
                <h3>信州春大舞台</h3>
                <p>点亮夜经济，成就百姓梦。</p>
                <div class="product-action">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/gesi-union')
}

const goToProductDetail = (productId: string) => {
  router.push(`/card/gesi-union/product-center/${productId}`)
}
</script>

<style scoped>
.page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 70px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.2s;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  flex: 1;
  text-align: center;
  padding-right: 3rem;
}

.content {
  padding-top: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  margin-bottom: 1rem;
}

.card {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card h2 {
  color: #c41b21;
  margin: 0 0 1rem 0;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
}

.products-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.product-card {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.product-image {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  overflow: hidden;
  margin: 1rem;
  border-radius: 0.5rem;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-info {
  flex: 1;
  padding: 1rem 1rem 1rem 0;
}

.product-info h3 {
  color: #c41b21;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.product-info p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 0.75rem 0;
}

.product-action {
  display: flex;
  justify-content: flex-end;
}

.view-detail {
  color: #c41b21;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .card {
    padding: 1.5rem;
  }

  .card h2 {
    font-size: 1.3rem;
    margin: 0 0 1.5rem 0;
  }

  .product-image {
    width: 150px;
    height: 150px;
    margin: 1.5rem;
  }

  .product-info {
    padding: 1.5rem 1.5rem 1.5rem 0;
  }

  .product-info h3 {
    font-size: 1.2rem;
  }

  .product-info p {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}


</style>
