import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'aiSzAgentHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
      title: 'AI数智代言人'
    }
  },
  {
    path: '/project-intro',
    name: 'aiSzAgentProjectIntro',
    component: () => import('./views/ProjectIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
      title: 'AI数智代言人 - 项目介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'aiSzAgentAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
      title: 'AI数智代言人 - AI宣传员'
    }
  },
  {
    path: '/case-center',
    name: 'aiSzAgentCaseCenter',
    component: () => import('./views/CaseCenterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
      title: 'AI数智代言人 - 案例中心'
    }
  }
]

export default routes 