# 直接使用内置的 dict 类型
Args = dict
Output = dict

async def main(args: Args) -> Output:
    params = args.get('params', {})
    title = params.get('title', '')
    desc = params.get('desc', '')
    url = params.get('url', '')
    nickname = params.get('nickname', '')
    likedCount = params.get('likedCount', 0)  # ✅ 添加默认值 0
    videoUrl = params.get('videoUrl', '')
    collectedCount = params.get('collectedCount', 0)  # ✅ 添加默认值 0
    imageList = params.get('imageList', [])

    # 处理图片地址
    image_urls = []
    for item in imageList:
        if isinstance(item, dict) and 'urlDefault' in item:
            image_urls.append(item['urlDefault'])
    
    # ✅ 如果需要使用图片URL字符串，可以这样：
    # image_url_str = '\n'.join(image_urls)

    records = [{"fields": {
        "笔记链接": url,
        "标题": title,
        "内容": desc,
        "作者": nickname,
        "点赞数": likedCount,
        "收藏数": collectedCount,
        "图片地址": imageList,  # 或者使用 image_urls 如果只要URL列表
        "视频地址": videoUrl,
    }}]

    # 构建输出对象
    ret = {
        "records": records
    }
    return ret
