# 函数定义的参数 dict 类型
args = dict
Output = dict

# async def main(args: args) -> Output:
async def main(args: args) -> Output:
    params = args.get('params', {})
    title = params.get('title', '')
    desc = params.get('desc', '')
    url = params.get('url', '')
    nickname = params.get('nickname', '')
    likedCount = params.get('likedCount', '')
    videoUrl = params.get('videoUrl', '')
    collectedCount = params.get('collectedCount', '')
    imageList = params.get('imageList', [])

    # 处理图片列表
    image_urls = []
    for item in imageList:
        if isinstance(item, dict) and 'urlDefault' in item:
            image_urls.append(item['urlDefault'])
    
    image_url_str = '\n'.join(image_urls)

    # 构建返回数据
    records = [{"fields": {
        "视频链接": url,
        "标题": title,
        "内容": desc,
        "作者": nickname,
        "点赞数": likedCount,
        "收藏数": collectedCount,
        "图片链接": imageList,
        "视频地址": videoUrl,
    }}]

    # 构建返回结果
    ret = {
        "records": records
    }
    
    return ret
