<template>
  <div class="page">
    <!-- 顶部导航 -->
    <div class="header">
      <button @click="goBack" class="back-btn">‹ 返回</button>
      <h1>协会介绍</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 欢迎页 -->
      <div class="section">
        <div class="welcome-card">
          <h1>上饶市民营（个私）经济协会</h1>
          <p>政府的助手、会员的帮手、饶商的"温暖之家"</p>
        </div>
      </div>

      <!-- 宣传视频 -->
      <div class="section">
        <div class="card">
          <h2>协会宣传视频</h2>
          <div class="video-container">
            <video controls loop playsinline preload="metadata"  poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/shipinfengmian.png" class="intro-video">
              <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/GeSiXieHui/xcsp.mp4" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </div>
        </div>
      </div>

      <!-- 关于我们 -->
      <div class="section">
        <div class="card">
          <h2>关于我们</h2>
          <div class="about-content">
            <p class="about-text">上饶市民营（个私）经济协会成立于2001年11月，前身为上饶市个体私营经济协会。2022年11月，协会实现脱钩换届，成为由全市民营个私企业自愿组成的非营利性社会团体组织。登记管理机关为上饶市民政局，业务主管单位为上饶市市场监督管理局。</p>

            <!-- 核心数据 -->
            <div class="core-data">
              <h3>核心数据</h3>
              <div class="data-grid">
                <div class="data-item">
                  <div class="data-number">近40万户</div>
                  <div class="data-label">服务企业</div>
                </div>
                <div class="data-item">
                  <div class="data-number">90000+家</div>
                  <div class="data-label">活跃会员</div>
                </div>
                <div class="data-item">
                  <div class="data-number">近30万个</div>
                  <div class="data-label">创造岗位</div>
                </div>
                <div class="data-item">
                  <div class="data-number">1500+亿元</div>
                  <div class="data-label">年交易额</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心服务 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Grid /></el-icon>
            六大服务中心 全方位赋能企业发展
          </h2>
          <p class="service-intro">协会构建了六大服务中心和九大服务项目，旨在打造"企业生命周期一站式服务中心"，为会员提供保姆级服务，让会员省钱、省时、省心！</p>

          <!-- 六大服务中心 -->
          <div class="service-centers">
            <h3>六大服务中心</h3>
            <div class="service-grid">
              <div class="service-item">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/jinrongfuwuzhongxin.jpg" alt="金融服务中心" />
                <h4>
                  <el-icon><Money /></el-icon>
                  金融服务中心
                </h4>
              </div>
              <div class="service-item">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/fawufuwuzhongxin.jpg" alt="法务服务中心" />
                <h4>
                  <el-icon><Management /></el-icon>
                  法务服务中心
                </h4>
              </div>
              <div class="service-item">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/caishuifuwuzhongxin.jpg" alt="财税服务中心" />
                <h4>
                  <el-icon><Document /></el-icon>
                  财税服务中心
                </h4>
              </div>
              <div class="service-item">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/zhishichanquanpinpaifuwuzhongxin.jpg" alt="知识产权品牌服务中心" />
                <h4>
                  <el-icon><Trophy /></el-icon>
                  知识产权品牌服务中心
                </h4>
              </div>
              <div class="service-item">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/rencaifuwuzhongxin.jpg" alt="人才服务中心" />
                <h4>
                  <el-icon><User /></el-icon>
                  人才服务中心
                </h4>
              </div>
              <div class="service-item">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/peixunfuwuzhongxin.jpg" alt="培训服务中心" />
                <h4>
                  <el-icon><Reading /></el-icon>
                  培训服务中心
                </h4>
              </div>
            </div>
          </div>

          <!-- 九大服务项目 -->
          <div class="service-projects">
            <h3>九大服务项目</h3>
            <div class="projects-grid">
              <div class="project-category">
                <h4>金融服务</h4>
                <div class="project-items">
                  <div class="project-item">
                    银行贷款：匹配合适银行与产品，推荐信用贷款最高100万，协助申请创业担保贴息贷款。
                  </div>
                  <div class="project-item">
                    保险服务：>为个私企业量身定制保险，提高抗风险能力。
                  </div>
                  <div class="project-item">
                   创业贴息：免费协助会员申请创业担保贴息贷款，利息低至2%。
                  </div>
                </div>
              </div>

              <div class="project-category">
                <h4>法务服务</h4>
                <div class="project-items">
                  <div class="project-item">
                    与品牌律师事务所合作，全年为会员提供免费法律咨询，法律顾问费用享5折优惠。
                  </div>
                </div>
              </div>

              <div class="project-category">
                <h4>财税服务</h4>
                <div class="project-items">
                  <div class="project-item">
                    与品牌财税公司合作，代理记账享5折优惠，全年提供免费财税咨询服务。
                  </div>
                </div>
              </div>

              <div class="project-category">
                <h4>品牌服务</h4>
                <div class="project-items">
                  <div class="project-item">
                    知识产权：协助进行商标注册、专利发明，申请专精特新、小巨人等。
                  </div>
                  <div class="project-item">
                    评优评选：协助申请诚信企业、先进商户等各项荣誉。
                  </div>
                </div>
              </div>

              <div class="project-category">
                <h4>人才服务</h4>
                <div class="project-items">
                  <div class="project-item">
                    学习培训：组织免费自媒体、直播、短视频技能培训及国内外学习考察。
                  </div>
                  <div class="project-item">
                    管理提升：每年举办企业家高管专题班。
                  </div>
                </div>
              </div>

               <div class="project-category">
                <h4>政务服务</h4>
                <div class="project-items">
                  <div class="project-item">
                    免费提供市管局、人社局、税务局、商务局相关政策对接服务。
                  </div>
                </div>
              </div>

               <div class="project-category">
                <h4>推广宣传</h4>
                <div class="project-items">
                  <div class="project-item">
                   免费为会员企业做一次宣传推荐，并在协会官方平台（公众号、视频号、抖音）发布。
                  </div>
                </div>
              </div>

               <div class="project-category">
                <h4>代办服务</h4>
                <div class="project-items">
                  <div class="project-item">
                    免费为会员企业提供工商年检、税务零申报代办服务。
                  </div>
                </div>
              </div>

               <div class="project-category">
                <h4>业务撮合</h4>
                <div class="project-items">
                  <div class="project-item">
                    免费为会员企业进行资源对接、项目匹配。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 协会文化 -->
      <div class="section">
        <div class="card">
          <h2>我们的使命与愿景</h2>

          <div class="culture-content">
            <div class="culture-item">
              <h3>🎯 宗旨</h3>
              <p>政治建会、党建促会、服务兴会</p>
            </div>

            <div class="culture-item">
              <h3>💡 理念</h3>
              <p>致力于发挥桥梁纽带作用，努力为政府当好助手，为会员当好帮手，成为上饶商人的"温暖之家"。</p>
            </div>

            <div class="culture-item">
              <h3>🎯 目标</h3>
              <p>实现全市个私协会"一座城，一条心，一盘棋"的联动格局，推动服务标准化、规范化，走在江西前列，形成全国影响。</p>
            </div>

            <div class="culture-item">
              <h3>🏛️ 党建引领</h3>
              <p>协会党总支坚持政治引领，全面贯彻落实党的二十大精神，深入推进基层党建"三化"建设。我们定期开展主题党日活动，加强与业务主管单位的联系，确保协会发展始终沿着正确方向前进。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 发展历程 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Clock /></el-icon>
            我们的足迹
          </h2>
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-date">1987.10</div>
              <div class="timeline-content">成立江西省个体劳动者协会上饶地区工作委员会，地区工商行政管理局局长刘书田兼主任，胡家财为副主任，工作人员4名。</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">1990.5</div>
              <div class="timeline-content">成立江西省个体私营经济协会上饶地区工作委员会，属副县级单位，定编8名，下设办公室。同时撤销原个体劳动者协会上饶地区工作委员会。</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">1991.3</div>
              <div class="timeline-content">徐启享任江西省个体私营经济协会上饶地区工作委员会主任。</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">2001.11</div>
              <div class="timeline-content">成立上饶市个体私营经济协会，上饶市工商管理局局长江训金担任会长。</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">2017.11</div>
              <div class="timeline-content">丁永红任上饶市个体私营经济协会办公室主任。</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">2022.11</div>
              <div class="timeline-content">换届并脱钩，傅利平当选上饶市个体私营经济协会会长。</div>
            </div>
            <div class="timeline-item highlight">
              <div class="timeline-date">2023.3</div>
              <div class="timeline-content">更名为上饶市民营（个私）经济协会。</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 入会指南 -->
      <div class="section">
        <div class="card">
          <h2>加入我们 共创未来</h2>

          <div class="membership-guide">
            <!-- 入会条件 -->
            <div class="guide-section">
              <h3>📋 入会条件</h3>
              <div class="conditions-list">
                <div class="condition-item">✓ 上饶市范围内民营个私企业</div>
                <div class="condition-item">✓ 承认协会章程和发展理念</div>
                <div class="condition-item">✓ 合规合法经营，有固定经营场所</div>
                <div class="condition-item">✓ 法人无犯罪记录且无法院执行记录</div>
                <div class="condition-item">✓ 年龄18-60周岁</div>
              </div>
            </div>

            <!-- 会费标准 -->
            <div class="guide-section">
              <h3>💰 会费标准</h3>
              <div class="fees-compact">
                <div class="fee-row">
                  <span class="fee-label">会员单位</span>
                  <span class="fee-value">365元/年</span>
                </div>
                <div class="fee-row">
                  <span class="fee-label">理事单位</span>
                  <span class="fee-value">1000元/年</span>
                </div>
                <div class="fee-row">
                  <span class="fee-label">副会长单位</span>
                  <span class="fee-value">3000元/年</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


     
       

      <!-- 联系我们 -->
      <div class="section">
        <div class="card">
          <h2>联系我们</h2>
          <div class="contact-simple">
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <span class="contact-simple-icon">📞</span>
                <span>秘书处电话</span>
              </div>
              <div class="contact-simple-value">
                <span class="phone-number">188 2772 6669</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="copyPhone">复制</button>
                  <button class="btn-simple" @click="callPhone">拨打</button>
                </div>
              </div>
            </div>
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <span class="contact-simple-icon">�</span>
                <span>联系地址</span>
              </div>
              <div class="contact-simple-value">江西省上饶市信州区带湖路60号</div>
            </div>
            <div style="text-align: center; margin-top: 1rem;">
              <button @click="goToAI" class="ai-btn">💬 一键咨询</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar.vue'
import { Clock } from '@element-plus/icons-vue'
import { Grid, Money, Management, Document, Trophy, User, Reading } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/gesi-union')
}

const goToAI = () => {
  router.push('/card/gesi-union/ai-promoter')
}

const copyPhone = () => {
  navigator.clipboard.writeText('18827726669')
  alert('电话号码已复制')
}

const callPhone = () => {
  window.open('tel:18827726669')
}
</script>

<style scoped>
.page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 70px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.2s;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  flex: 1;
  text-align: center;
  padding-right: 3rem; /* 平衡左侧按钮 */
}

.content {
  padding-top: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  margin-bottom: 1rem;
}

.card {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.welcome-card {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  text-align: center;
  border-radius: 0.75rem;
  padding: 1.5rem 1rem;
  box-shadow: 0 8px 30px rgba(196, 27, 33, 0.3);
}

.welcome-card h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  line-height: 1.3;
}

.welcome-card p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 400;
}

.card h2 {
  color: #c41b21;
  margin: 0 0 0.75rem 0;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
}

.card video {
  display: block;
  margin: 0 auto;
}

/* 视频容器样式 */
.video-container {
  text-align: center;
  margin: 1rem 0;
  position: relative;
  z-index: 1;
}

.intro-video {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.services {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 0.5rem;
}

.service {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.5rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #c41b21, #e53e3e);
}

.service:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.service h3 {
  color: #c41b21;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.service p {
  color: #666;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.service ul {
  margin: 0;
  padding-left: 1rem;
}

.service li {
  color: #555;
  line-height: 1.5;
  margin-bottom: 0.3rem;
  font-size: 0.85rem;
}

.service li:last-child {
  margin-bottom: 0;
}

/* 入会指南紧凑布局 */
.membership-compact {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 0.5rem;
}

.membership-item {
  background: #fafafa;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #f0f0f0;
}

.membership-item h3 {
  color: #c41b21;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.membership-item p {
  color: #555;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.fees-compact {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  margin-top: 0.5rem;
}

.fee-row {
  background: #fef5f5;
  padding: 0.5rem 0.75rem;
  border-radius: 0.3rem;
  border: 1px solid #fed7d7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fee-label {
  color: #555;
  font-size: 0.9rem;
  font-weight: 500;
}

.fee-value {
  color: #c41b21;
  font-weight: 600;
  font-size: 0.9rem;
}

/* 联系我们简洁布局 */
.contact-simple {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
  margin-top: 0.5rem;
}

.contact-simple-row {
  background: #fef5f5;
  padding: 0.6rem 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #fed7d7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 2rem;
}

.contact-simple-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #c41b21;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 4rem;
}

.contact-simple-icon {
  font-size: 1rem;
}

.contact-simple-value {
  flex: 1;
  text-align: right;
  color: #555;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.phone-number {
  font-weight: 500;
}

.contact-simple-actions {
  display: flex;
  gap: 0.4rem;
  flex-shrink: 0;
}

.btn-simple {
  background: #c41b21;
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 0.4rem;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 3rem;
  text-align: center;
  white-space: nowrap;
}

.btn-simple:hover {
  background: #a01419;
  transform: translateY(-1px);
}

.btn {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  margin-left: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn:hover {
  background: linear-gradient(135deg, #a01419, #c41b21);
  transform: translateY(-1px);
}

.ai-btn {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 1.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(196, 27, 33, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
}

.ai-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(196, 27, 33, 0.4);
}

.timeline {
  position: relative;
  padding-left: 1.5rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #c41b21, #e53e3e);
}

.timeline-item {
  position: relative;
  margin-bottom: 1rem;
  padding-left: 1rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -0.25rem;
  top: 0.25rem;
  width: 0.5rem;
  height: 0.5rem;
  background: #c41b21;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-item.highlight::before {
  background: #e53e3e;
  box-shadow: 0 0 0 3px rgba(196, 27, 33, 0.2);
}

.timeline-date {
  color: #c41b21;
  font-weight: 600;
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
}

.timeline-content {
  color: #555;
  font-size: 0.8rem;
  line-height: 1.4;
}

@media (min-width: 768px) {
  .timeline {
    padding-left: 3rem;
  }
  .timeline::before {
    left: 1rem;
  }
  .timeline-item::before {
    left: 0.125rem;
  }
}


/* 核心数据样式 */
.core-data {
  margin-top: 1rem;
  padding: 1rem;
  background: #fef5f5;
  border-radius: 0.5rem;
  border: 1px solid #fed7d7;
}

.core-data h3 {
  color: #c41b21;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  text-align: center;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.data-item {
  background: white;
  padding: 0.75rem;
  border-radius: 0.5rem;
  text-align: center;
  border: 1px solid #f0f0f0;
}

.data-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: #c41b21;
  margin-bottom: 0.25rem;
}

.data-label {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

/* 服务介绍样式 */
.service-intro {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  text-align: center;
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.service-centers h3,
.service-projects h3 {
  color: #c41b21;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  text-align: center;
}

/* 九大服务项目样式 */
.service-projects {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.project-category {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.project-category h4 {
  color: #c41b21;
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.project-item {
  background: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  border: 1px solid #f5f5f5;
  font-size: 0.8rem;
  line-height: 1.4;
  color: #555;
}

.project-item strong {
  color: #c41b21;
}

/* 协会文化样式 */
.culture-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.culture-item {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.culture-item h3 {
  color: #c41b21;
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.culture-item p {
  color: #555;
  font-size: 0.85rem;
  line-height: 1.5;
  margin: 0;
}

/* 入会指南样式 */
.membership-guide {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.guide-section h3 {
  color: #c41b21;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.conditions-list {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.condition-item {
  background: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  border: 1px solid #f0f0f0;
  color: #555;
  font-size: 0.8rem;
}

/* 移动端特殊样式 */
@media (max-width: 480px) {
  .content {
    padding-top: 4.5rem;
  }

  .video-container {
    margin: 0.5rem 0;
  }

  .intro-video {
    max-width: 100%;
    border-radius: 0.25rem;
  }

  .contact-simple-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .contact-simple-label {
    min-width: auto;
    width: 100%;
  }

  .contact-simple-value {
    width: 100%;
    justify-content: space-between;
    text-align: left;
  }

  .phone-number {
    flex: 1;
  }

  .contact-simple-actions {
    gap: 0.5rem;
  }

  .btn-simple {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    min-width: 3.5rem;
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .welcome-card {
    padding: 2rem 1.5rem;
  }

  .welcome-card h1 {
    font-size: 1.9rem;
    margin: 0 0 0.75rem 0;
  }

  .welcome-card p {
    font-size: 1.1rem;
  }

  .card {
    padding: 1.5rem;
  }

  .card h2 {
    font-size: 1.3rem;
    margin: 0 0 1rem 0;
  }

  .section {
    margin-bottom: 1.75rem;
  }

  .data-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  .data-item {
    padding: 1rem;
  }

  .data-number {
    font-size: 1.5rem;
  }

  .data-label {
    font-size: 0.9rem;
  }

  .core-data {
    margin-top: 2rem;
    padding: 1.5rem;
  }

  .core-data h3 {
    font-size: 1.2rem;
    margin: 0 0 1rem 0;
  }

  .service-intro {
    font-size: 1rem;
    margin: 0 0 2rem 0;
    padding: 1rem;
  }

  .service-centers h3,
  .service-projects h3 {
    font-size: 1.3rem;
    margin: 0 0 1.5rem 0;
  }

  .service-projects {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #f0f0f0;
  }

  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .project-category {
    padding: 1.5rem;
  }

  .project-category h4 {
    font-size: 1.1rem;
    margin: 0 0 1rem 0;
  }

  .project-items {
    gap: 0.75rem;
  }

  .project-item {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .culture-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .culture-item {
    padding: 1.5rem;
  }

  .culture-item h3 {
    font-size: 1.1rem;
    margin: 0 0 0.75rem 0;
  }

  .culture-item p {
    font-size: 0.95rem;
  }

  .timeline {
    padding-left: 3rem;
  }

  .timeline::before {
    left: 1rem;
  }

  .timeline-item {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
  }

  .timeline-item::before {
    left: 0.125rem;
    width: 0.75rem;
    height: 0.75rem;
  }

  .timeline-item.highlight::before {
    box-shadow: 0 0 0 4px rgba(196, 27, 33, 0.2);
  }

  .timeline-date {
    font-size: 0.9rem;
  }

  .timeline-content {
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .membership-guide {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .guide-section h3 {
    font-size: 1.2rem;
    margin: 0 0 1rem 0;
  }

  .conditions-list {
    gap: 0.5rem;
  }

  .condition-item {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .services {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .service-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .service-item {
    padding: 1.5rem;
  }

  .service-item img {
    max-width: 220px;
    margin-bottom: 1rem;
  }

  .service-item h4 {
    font-size: 1.2rem;
    margin: 0 0 0.5rem 0;
  }

  .service-item .el-icon {
    font-size: 1.5rem;
  }

  .membership-compact {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .contact-simple-row {
    padding: 1rem 1.25rem;
    flex-direction: row;
    align-items: center;
    min-height: 2.5rem;
  }

  .contact-simple-label {
    min-width: 5rem;
  }

  .contact-simple-value {
    justify-content: flex-end;
    text-align: right;
    gap: 0.75rem;
    flex-wrap: nowrap;
  }

  .fees-compact {
    gap: 0.6rem;
  }

  .fee-row {
    padding: 0.6rem 0.8rem;
  }

  .ai-btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }

  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .services {
    grid-template-columns: repeat(3, 1fr);
  }

  .service-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .projects-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .welcome-card h1 {
    font-size: 2.1rem;
  }

  .welcome-card p {
    font-size: 1.2rem;
  }

  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* 最新活动样式 */
.activity-item {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.activity-item:last-child {
  margin-bottom: 0;
}

.activity-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.activity-item h3 {
  color: #c41b21;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.activity-intro {
  color: #555;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
  text-align: justify;
}

.activity-section {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #f5f5f5;
}

.activity-section:last-child {
  margin-bottom: 0;
}

.activity-section h4 {
  color: #c41b21;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.activity-section h5 {
  color: #c41b21;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.activity-section p {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0 0 1rem 0;
  text-align: justify;
}

.activity-image {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.activity-conclusion {
  background: #fef5f5;
  border: 1px solid #fed7d7;
  border-radius: 0.5rem;
  padding: 1rem;
  color: #c41b21;
  font-weight: 500;
  margin: 1rem 0 0 0;
  text-align: center;
}

.stage-goals {
  margin-top: 1rem;
}

.stage-goals h4 {
  color: #c41b21;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.goal-item {
  background: white;
  border: 1px solid #f5f5f5;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.goal-item:last-child {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .activity-item h3 {
    font-size: 1.4rem;
  }

  .activity-intro {
    font-size: 1.05rem;
  }

  .activity-section {
    padding: 1.25rem;
  }

  .activity-section h4 {
    font-size: 1.2rem;
  }

  .activity-section p {
    font-size: 1rem;
  }
}
</style>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 欢迎页样式 */
.welcome-section {
  margin-bottom: 2rem;
}

.welcome-card {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  color: white;
  box-shadow: 0 8px 30px rgba(196, 27, 33, 0.3);
}

.association-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.association-subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

/* 通用卡片样式 */
.section-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  color: #c41b21;
  margin: 0 0 1.5rem 0;
  text-align: center;
  font-weight: 600;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #666;
  text-align: center;
  margin: 0 0 2rem 0;
}



/* 关于我们样式 */
.about-text {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
  text-align: justify;
}

/* 六大服务中心网格布局 */
.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-top: 1rem;
}

@media (min-width: 600px) {
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

.service-item {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.5rem;
  padding: 0.75rem;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  min-height: 0;
  box-sizing: border-box;
}

.service-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.service-item img {
  width: 100%;
  max-width: 120px;
  height: auto;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.service-item h4 {
  font-size: 0.8rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  line-height: 1.2;
}

.service-item .el-icon {
  font-size: 1rem;
  color: #c41b21;
}

/* 入会指南样式 */
.membership-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.membership-item h3 {
  font-size: 1.2rem;
  color: #c41b21;
  margin: 0 0 1rem 0;
  font-weight: 600;
}

.membership-item p {
  font-size: 1rem;
  color: #555;
  margin: 0;
  line-height: 1.6;
}

.fee-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #fef5f5;
  border-radius: 0.5rem;
  border: 1px solid #fed7d7;
}

.fee-type {
  font-weight: 500;
  color: #333;
}

.fee-amount {
  font-weight: 600;
  color: #c41b21;
  font-size: 1.1rem;
}

/* 联系我们样式 */
.contact-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1.25rem;
  background: #fef5f5;
  border-radius: 0.75rem;
  border: 1px solid #fed7d7;
}

.contact-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #c41b21;
}

.contact-label .el-icon {
  font-size: 1.2rem;
}

.contact-value {
  color: #555;
  font-size: 1rem;
  line-height: 1.5;
}

.contact-value-with-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border: none;
  color: white;
}

.action-btn:hover {
  background: linear-gradient(135deg, #a01419, #c41b21);
}

.consultation-section {
  text-align: center;
  margin-top: 1rem;
}

.consultation-btn {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 2rem;
  box-shadow: 0 4px 15px rgba(196, 27, 33, 0.3);
  transition: all 0.3s ease;
}

.consultation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(196, 27, 33, 0.4);
}

/* 响应式设计 */
@media (min-width: 768px) {
  .association-title {
    font-size: 2.2rem;
  }

  .association-subtitle {
    font-size: 1.3rem;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .membership-content {
    flex-direction: row;
    gap: 3rem;
  }

  .membership-item {
    flex: 1;
  }

  .contact-item {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .contact-value-with-actions {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }

  .fee-list {
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .association-title {
    font-size: 2.5rem;
  }

  .association-subtitle {
    font-size: 1.4rem;
  }
}
</style>
