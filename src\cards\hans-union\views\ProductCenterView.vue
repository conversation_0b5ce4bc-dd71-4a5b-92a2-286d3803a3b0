<template>
  <div class="product-center-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品中心</h1>
    </div>

    <!-- 产品列表 -->
    <div class="products-section">
      <div class="products-list">
        <div 
          v-for="product in products" 
          :key="product.id" 
          class="product-card"
          @click="navigateToDetail(product.id)"
        >
          <div class="product-image">
            <img :src="product.image" :alt="product.title">
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.title }}</h3>
            <p class="product-desc">{{ product.description }}</p>
            <div class="product-action">
              <el-button type="primary" size="small" @click.stop="navigateToDetail(product.id)">
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeft 
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}

// 产品数据
const products = ref([
  {
    id: 'health-center',
    title: '汉氏医学健康体检中心',
    description: '您身边的高端健康管家',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/jiankangtijianzhongxin.png'
  },
  {
    id: 'medical-lab',
    title: '汉氏医学检验实验室',
    description: '创建百姓信赖的医学实验室',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/jianyanshiyanshi.png'
  },
  {
    id: 'hospital',
    title: '上饶经开汉氏联合医院（一级医院）',
    description: '环境舒适，满足公共医疗卫生需求',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/lianheyiyuan.png'
  },
  {
    id: 'third-hospital',
    title: '汉氏联合三级医院(筹)',
    description: '医以济世，术贵乎精',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/sanjiyiyuan.png'
  },
  {
    id: 'medical-beauty',
    title: '汉氏医美抗衰中心',
    description: '科技赋活，焕颜新机',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/yimei.png'
  },
  {
    id: 'medical-tourism',
    title: '汉氏医疗旅游中心',
    description: '细胞科技，健康人生',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/yiliaolvyou.png'
  },
  {
    id: 'cell-valley',
    title: '国际细胞谷',
    description: '细胞承载希望，科技实现梦想',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/xibaogu.png'
  },
  {
    id: 'cloud-medical',
    title: '汉氏云医 (线上医疗平台)',
    description: '数字化+云医疗，您的线上专家',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/hanshiyunyi.png'
  }
])

// 导航到产品详情页
const navigateToDetail = (productId: string) => {
  router.push(`/card/hans-union/product-center/${productId}`)
}
</script>

<style scoped>
/* 容器样式 */
.product-center-container {
  min-height: 100vh;

  padding-top: 3rem; /* 为固定头部留出空间 */
  padding-bottom: 80px; /* 为底部导航栏留出空间 */
}

/* 顶部导航栏样式 */
.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

/* 返回按钮样式 */
.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

/* 标题样式 */
.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

/* 页面副标题区域 */
.page-subtitle-section {
  text-align: center;
  padding: 20px 20px 10px;
  background: white;
  margin-bottom: 20px;
  margin-top: 1rem; /* 为固定头部留出额外空间 */
}

.page-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  padding: 0 20px;
  margin-bottom: 20px;
  gap: 12px;
  overflow-x: auto;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: white;
  border-radius: 20px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-tab:hover {
  color: #0f9da8;
  transform: translateY(-1px);
}

.category-tab.active {
  background: linear-gradient(135deg, #0f9da8 0%, #0d8691 100%);
  color: white;
}

/* 产品区域 */
.products-section {
  padding: 20px;
  margin-top: 1rem;
  max-width: 1200px; /* 添加最大宽度限制，与其他页面保持一致 */
  margin-left: auto;
  margin-right: auto;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

/* 产品卡片 - 左图右字布局 */
.product-card {
  display: flex;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 140px; /* 固定高度 */
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
  flex: 0 0 140px; /* 固定宽度 */
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-info {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.product-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 auto 0;
  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-action {
  margin-top: auto;
  text-align: right;
}

.product-action .el-button {
  background: linear-gradient(135deg, #0f9da8 0%, #0d8691 100%);
  border: none;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #ddd;
}

.empty-text {
  font-size: 18px;
  margin: 0 0 8px 0;
}

.empty-desc {
  font-size: 14px;
  margin: 0;
}

/* 联系横幅 */
.contact-banner {
  background: linear-gradient(135deg, #0f9da8 0%, #0d8691 100%);
  margin: 0 20px;
  border-radius: 16px;
  padding: 30px 20px;
  text-align: center;
  color: white;
}

.banner-content h3 {
  font-size: 20px;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.banner-content p {
  font-size: 14px;
  margin: 0 0 20px 0;
  opacity: 0.9;
}

.banner-content .el-button {
  background: white;
  color: #0f9da8;
  border: none;
  border-radius: 20px;
  padding: 12px 24px;
  font-weight: 600;
}

.banner-content .el-button:hover {
  background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .header h1 {
    font-size: 16px;
  }
  
  .category-tabs {
    padding: 0 16px;
  }
  
  .products-section {
    padding: 0 16px;
  }
  
  .contact-banner {
    margin: 0 16px;
  }
}

</style>