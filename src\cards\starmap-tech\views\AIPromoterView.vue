<script setup lang="ts">
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { 
  ArrowLeft, 
  Reading, 
  Timer, 
  User, 
  ArrowRight, 
  Message, 
  Collection
} from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/starmap-tech')
}

const chatWithAI = () => {
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=rucxu60brc0yve5fxoa2ovll', '_blank')
}

// 添加打字机效果
const welcomeText = ref('')
const fullText = '您好！我是星图AI未来成长中心的专属AI顾问，星图。从教育理念到活动安排，从课程体系到会员权益，任何关于星图AI的问题，我都能为您提供7x24小时的专业解答。您可以直接输入您关心的问题，也可以点击下方的常见问题，快速了解哦！'
const typingSpeed = 50
let currentIndex = 0
let typingTimer: number | null = null

const typeText = () => {
  if (currentIndex < fullText.length) {
    welcomeText.value += fullText.charAt(currentIndex)
    currentIndex++
    typingTimer = setTimeout(typeText, typingSpeed) as unknown as number
  }
}

onMounted(() => {
  document.title = '星图AI - AI宣传员'
  setTimeout(() => {
    typeText()
  }, 500)
})

onBeforeRouteLeave(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>AI宣传员</h1>
    </div>

    <div class="content">
      <div class="ai-promoter-container">
        <div class="ai-card">
          <div class="ai-image-container">
            <div class="ai-image"></div>
            <div class="image-overlay"></div>
          </div>
          <div class="ai-content">
            <h2>星宝 <span class="badge">AI 宣传员</span></h2>
            <div class="typing-container">
              <p class="welcome-text">
                <template v-if="welcomeText">
                  <span v-for="(line, index) in welcomeText.split('\n')" :key="index">
                    {{ line }}<br v-if="index < welcomeText.split('\n').length - 1">
                  </span>
                </template>
                <span class="cursor" v-if="welcomeText.length < fullText.length">|</span>
              </p>
            </div>
            <div class="action-container">
              <el-button type="primary" class="chat-btn" @click="chatWithAI">
                开始对话
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        
        <div class="features-section">
          <h3>我的服务能力</h3>
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <h4>产品能力咨询</h4>
              <p>详细介绍星图AI的核心产品功能和技术优势</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon speed-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <h4>7x24小时在线</h4>
              <p>随时为您解答疑问，无需等待</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <h4>成功案例分享</h4>
              <p>分享真实的客户案例和应用场景</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon personalized-icon">
                <el-icon><User /></el-icon>
              </div>
              <h4>项目合作探讨</h4>
              <p>为您提供专业的合作建议和解决方案</p>
            </div>
          </div>
        </div>

        <div class="service-promise-section">
          <h3>服务承诺</h3>
          <div class="promise-grid">
            <div class="promise-item">
              <div class="promise-icon smart-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <h4>智能响应</h4>
              <p>基于先进的AI技术，为您提供准确、专业的回答</p>
            </div>
            <div class="promise-item">
              <div class="promise-icon instant-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <h4>即时服务</h4>
              <p>7x24小时在线，随时响应您的咨询需求</p>
            </div>
            <div class="promise-item">
              <div class="promise-icon precise-icon">
                <el-icon><User /></el-icon>
              </div>
              <h4>精准匹配</h4>
              <p>根据您的具体需求，提供个性化的解决方案</p>
            </div>
            <div class="promise-item">
              <div class="promise-icon security-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <h4>信息安全</h4>
              <p>严格保护您的隐私信息和商业机密</p>
            </div>
          </div>
        </div>

        <div class="contact-section">
          <h3>联系方式</h3>
          <div class="contact-info">
            <div class="contact-item">
              <el-icon class="contact-icon"><Message /></el-icon>
              <span>13319313313（吴）</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #409EFF, #64b5f6);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem; /* 为底部标签栏留出空间 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.ai-promoter-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.ai-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
}

.ai-image-container {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.ai-image {
  width: 100%;
  height: 100%;
  background: url('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZLSZR.png') center top/cover;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to top, white, transparent);
}

.ai-content {
  padding: 1.5rem;
}

.ai-content h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.badge {
  font-size: 0.8rem;
  background: linear-gradient(135deg, #409EFF, #64b5f6);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-weight: 500;
}

.typing-container {
  margin-bottom: 1.5rem;
  min-height: 6rem;
}

.welcome-text {
  font-size: 1rem;
  line-height: 1.5;
  color: #555;
  margin: 0;
}

.cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: #409EFF;
  animation: blink 0.7s infinite;
  vertical-align: middle;
  margin-left: 2px;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.action-container {
  display: flex;
  justify-content: center;
}

.chat-btn {
  background: linear-gradient(135deg, #409EFF, #64b5f6);
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 2rem;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

.features-section, .service-promise-section, .contact-section {
  background: white;
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.features-section h3, .service-promise-section h3, .contact-section h3 {
  margin: 0 0 1.25rem 0;
  font-size: 1.2rem;
  color: #333;
  text-align: center;
}

.features-grid, .promise-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.feature-item, .promise-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0.75rem;
  border-radius: 0.75rem;
  background-color: #f8fbff;
  border: 1px solid #e1eeff;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-item:hover, .promise-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.15);
}

.feature-icon, .promise-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  color: white;
  font-size: 1.25rem;
}

.knowledge-icon {
  background: linear-gradient(135deg, #409EFF, #64b5f6);
}

.speed-icon {
  background: linear-gradient(135deg, #409EFF, #81d4fa);
}

.personalized-icon {
  background: linear-gradient(135deg, #409EFF, #64b5f6);
}

.smart-icon {
  background: linear-gradient(135deg, #409EFF, #64b5f6);
}

.instant-icon {
  background: linear-gradient(135deg, #409EFF, #81d4fa);
}

.precise-icon {
  background: linear-gradient(135deg, #409EFF, #64b5f6);
}

.security-icon {
  background: linear-gradient(135deg, #409EFF, #64b5f6);
}

.feature-item h4, .promise-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.feature-item p, .promise-item p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #f8fbff;
  border-radius: 0.75rem;
  border: 1px solid #e1eeff;
}

.contact-icon {
  font-size: 1.2rem;
  color: #409EFF;
}

@media (min-width: 768px) {
  .features-grid, .promise-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .ai-card {
    flex-direction: row;
    align-items: stretch;
  }
  
  .ai-image-container {
    width: 40%;
    height: auto;
  }
  
  .ai-content {
    width: 60%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .image-overlay {
    display: none;
  }
  
  .ai-image {
    height: 100%;
  }

  .contact-info {
    flex-direction: row;
    justify-content: center;
  }

  .contact-item {
    flex: 1;
    max-width: 300px;
    justify-content: center;
  }
}

@media (min-width: 992px) {
  .features-grid, .promise-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style> 