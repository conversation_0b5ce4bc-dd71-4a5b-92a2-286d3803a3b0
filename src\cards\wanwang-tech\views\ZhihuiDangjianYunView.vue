<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('ZhihuiDangjianYunView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>智慧党建云平台</h1>
    </div>

    <div class="content">
      <!-- 产品概述 -->
      <div class="section overview-section">
        <div class="section-header">
          <h2>产品概述</h2>
        </div>
        <div class="overview-content">
          <div class="overview-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiDangJianYun.png" alt="智慧党建云平台" />
          </div>
          <div class="overview-text">
            <p>"互联网+党建"和智慧党建是在信息化发展新阶段，党建信息化、数字化的时代性称谓。它是运用信息化新技术，整合各方资源，更有效地加强组织管理，提高服务群众水平，扩大党在网络世界存在感和数字化影响力，提高党的执政能力，巩固党的执政基础的新平台、新模式、新形态。</p>
            <p>"互联网+党建"智慧党建是党建数字化、在线化和智能化形态。它将党建工作通过一体化网络平台集成，将纸质内容电子化，线下党建工作及服务群众工作流程化、线上化、数字化，以解决党员、群众与党组织的时空限制，更迅速及时地获取权威的相关信息，更便捷地办理相关组织业务、参加组织活动为基础，通过数据收集整理分析，实时获取党员和群众对党的看法、需求及趋向，党员和党组织的活动状况，及时调整党的工作内容和重点，并从网上获取党员群众对调整的意见反馈，循环往复、实时协同，从而更加有效地改进自身建设、服务群众，使党的建设更具时代性、开放性、互动性、民主性，最大限度地激发网络空间的正能量。</p>
          </div>
        </div>
      </div>

      <!-- 主要功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>主要功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🏛️</div>
            <h3>党务工作中心</h3>
            <p>统一管理党务工作流程，实现党务工作的数字化管理</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>服务活动中心</h3>
            <p>组织各类党建活动，提供活动策划、组织、执行的全流程服务</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📚</div>
            <h3>学习教育系统</h3>
            <p>提供在线学习和考试功能，丰富党员教育形式</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3>党建数据分析</h3>
            <p>运用大数据技术进行统计分析，为决策提供数据支撑</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">👥</div>
            <h3>党组织管理</h3>
            <p>完善的组织架构和人员管理体系</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💬</div>
            <h3>互动交流中心</h3>
            <p>搭建党员群众互动交流平台，增强组织凝聚力</p>
          </div>
        </div>
      </div>

      <!-- 多端互通 -->
      <div class="section platforms-section">
        <div class="section-header">
          <h2>多端互通</h2>
        </div>
        
        <!-- 党组织PC管理平台 -->
        <div class="platform-item">
          <div class="platform-header">
            <div class="platform-icon">💻</div>
            <h3>党组织PC管理平台</h3>
          </div>
          <div class="platform-content">
            <div class="platform-text">
              <p>PC管理平台是党务工作者进行党建工作管理的平台，它通过系统基础数据的设置、信息的维护、数据的留存和快速调取，有效帮助管理者实现智能化监管。管理者通过PC管理平台可以对党组织、党员管理以及其他党建相关工作内容进行统一的管理操作，实现线上工作、监管、交流等功能。</p>
            </div>
            <div class="platform-images">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yuqingduban66.jpg" alt="PC管理平台界面1" />

            </div>
          </div>
        </div>

        <!-- 党员手机应用平台 -->
        <div class="platform-item">
          <div class="platform-header">
            <div class="platform-icon">📱</div>
            <h3>党员手机应用平台</h3>
          </div>
          <div class="platform-content">
            <div class="platform-text">
              <p>党员手机应用平台，充分考虑到应用功能和系统实用性，实现系统功能在移动端的延伸，方便党员日常使用及操作。党员可通过移动端随时接收通知及学习、会议、活动等各类消息提醒，查阅最新的党政资讯，获取组织生活计划安排、按时参加并完成现场打卡。智慧党建移动端实现了在线教育学习，同时提供工作任务执行、动态调研、心得体会、建言献策、发展党员资料填报、日报记录上报、日程安排等功能，使党员能够打破时间地域限制，随时随地参与到党建工作中，是党员的移动工作台。</p>
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yuqingduban67.jpg" alt="PC管理平台界面2" />
            </div>
          </div>
        </div>

        <!-- 活动中心触控平台 -->
        <div class="platform-item">
          <div class="platform-header">
            <div class="platform-icon">🖥️</div>
            <h3>活动中心触控平台</h3>
          </div>
          <div class="platform-content">
            <div class="platform-text">
              <p>智慧党建触摸屏适用于政府机关、企事业单位办公大厅以及党群服务中心等场景。将智慧党建触摸屏作为展示党建成果的窗口，方便党员干部随时调取查阅，从多个维度清晰展示党委、党总支、党支部的树状结构层级关系，构建党员各类信息直观的统计图表，特色党建工作成果，党建知识问答等。与传统的党建展板、宣传栏、宣传册相比，信息更新更及时，维护更简单。</p>
            </div>
            <div class="platform-images">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yuqingduban68.jpg" alt="触控平台界面1" />
              
            </div>
          </div>
        </div>

        <!-- 监控中心大数据平台 -->
        <div class="platform-item">
          <div class="platform-header">
            <div class="platform-icon">📈</div>
            <h3>监控中心大数据平台</h3>
          </div>
          <div class="platform-content">
            <div class="platform-text">
              <p>将大数据技术运用于海量党建数据，实现"让数据自己说话"，将各党组织党建工作情况通过统计图表直观展现，数据实时更新，支持党建工作的线上督促、检查、催办。大数据平台可以帮助管理者在工作执行的过程中及时发现问题、纠正问题，减少问题，有效加强监管力度，促进党建工作落实。帮助决策者超越局部事实和经验判断作出正确的形势评估，使事物发展的趋势一目了然，为党组织决策提供数据参考。大数据平台显著提高了党在复杂形势下的全局掌控能力，组织引领能力，保持党的先进性。</p>
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yuqingduban69.jpg" alt="触控平台界面2" />
            </div>
          </div>
        </div>

        <!-- 群众宣教互动平台 -->
        <div class="platform-item">
          <div class="platform-header">
            <div class="platform-icon">🎪</div>
            <h3>群众宣教互动平台</h3>
          </div>
          <div class="platform-content">
            <div class="platform-text">
              <p>互动平台可用于乡镇街道、社区/村党建电子宣传栏，不仅可以进行党建信息宣传学习、党建知识趣味互动答题，还可进行民情收集、诉求上达，有效加强党组织与群众之间的联系。互动平台充分体现了党组织对群众的关爱和帮扶，有效破解基层社会治理发展难题，实现了基层党建工作重点向群众转移，扩大了党在城市基层的影响力和覆盖面。</p>
            </div>
            <div class="platform-images">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yuqingduban70.jpg" alt="群众宣教互动平台" />
            </div>
          </div>
        </div>
      </div>

      <!-- 平台价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>平台价值</h2>
        </div>
        <div class="value-content">
          <p>智慧党建云平台通过技术创新实现了党建工作的全面数字化转型，显著提升了党组织的管理效率和服务水平。平台打破了传统党建工作的时空限制，让党员能够随时随地参与党建活动，增强了党组织的凝聚力和影响力。</p>
          <p>通过大数据分析和智能化管理，平台为党建决策提供了科学依据，有效提升了党的执政能力和治理水平，为新时代党建工作注入了强大的科技动力。平台实现了党建工作的数字化、在线化和智能化，使党的建设更具时代性、开放性、互动性、民主性，最大限度地激发网络空间的正能量。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  min-height: 100vh;
  background: #1693d2;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 产品概述样式 */
.overview-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.overview-image {
  width: 320px;
  height: 200px;
  flex-shrink: 0;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.overview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overview-text {
  flex: 1;
}

.overview-text p {
  margin: 0 0 1rem 0;
  color: #555;
  line-height: 1.8;
  font-size: 1.1rem;
  text-align: justify;
}

.overview-text p:last-child {
  margin-bottom: 0;
}

/* 核心功能样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(22, 147, 210, 0.15);
  border-color: #1693d2;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1693d2;
}

.feature-card p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  text-align: justify;
}

/* 系统架构样式 */
.platform-item {
  margin-bottom: 3rem;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  overflow: hidden;
}

.platform-item:last-child {
  margin-bottom: 0;
}

.platform-header {
  background: #f8fafc;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.platform-icon {
  font-size: 2rem;
}

.platform-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #1693d2;
}

.platform-content {
  padding: 2rem;
}

.platform-text {
  margin-bottom: 2rem;
}

.platform-text p {
  margin: 0;
  color: #555;
  line-height: 1.8;
  font-size: 1.1rem;
  text-align: justify;
}

.platform-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.platform-images img {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.platform-images img:hover {
  transform: scale(1.02);
}

/* 平台价值样式 */
.value-content {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  padding: 2rem;
}

.value-content p {
  margin: 0 0 1.5rem 0;
  color: #555;
  line-height: 1.8;
  font-size: 1.1rem;
  text-align: justify;
}

.value-content p:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-content {
    flex-direction: column;
    text-align: center;
  }

  .overview-image {
    width: 150px;
    height: 150px;
    margin: 0 auto;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .platform-images {
    grid-template-columns: 1fr;
  }

  .platform-images img {
    max-height: 250px;
    max-width: 100%;
    width: auto;
    height: auto;
    margin: 0 auto;
    display: block;
    object-fit: contain;
  }

  /* 移动端文本中的图片适配 */
  .platform-text img {
    max-width: 100%;
    max-height: 250px;
    width: auto;
    height: auto;
    margin: 1rem auto;
    display: block;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .platform-header {
    padding: 1rem 1.5rem;
  }

  .platform-content {
    padding: 1.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
</style>
