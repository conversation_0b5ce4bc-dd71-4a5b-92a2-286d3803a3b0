<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft,  
  Service, 
  Monitor, 
  VideoCamera, 
  Promotion, 
  OfficeBuilding, 
  School,
  Connection,
  DocumentCopy,
  ChatDotRound
} from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'


const router = useRouter()

const goBack = () => {
  router.push('/card/ai-sz-guide')
}

const showContactQRCode = ref(false)
const contactQRCodeUrl = 'https://pic.sdtaa.com/ZhiLian/Picture/Project/contact.png'

const openAIChatUrl = () => {
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=9ho7aq84tu1pq7vhymum9fng', '_blank')
}

onMounted(() => {
  document.title = 'AI数智推介官 - 产品介绍'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品介绍</h1>
    </div>

    <div class="content">
      <div class="content-wrapper">
        <!-- 一、宣传视频 -->
        <section class="section video-section">
          <p class="guide-text">点击观看，快速了解AI数智推介官</p>
          <div class="video-container">
            <video 
              src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/AIShuZhiTuiJianGuan/XuanChuanShiPing.mp4" 
              controls 
              poster=""
              class="promo-video"
            ></video>
          </div>
        </section>
        
        <!-- 二、核心价值主张 -->
        <section class="section slogan-section">
          <h2 class="slogan">为您的单位，配备一位永不离线的AI数智推介官</h2>
        </section>
        
        <!-- 三、客户画像及痛点 -->
        <section class="section customer-section">
          <!-- 政府部门 -->
          <div class="customer-card">
            <div class="customer-header">
              <el-icon class="customer-icon government"><School /></el-icon>
              <div class="customer-title">
                <h3>我是政府部门</h3>
                <p class="customer-desc">创新招商文旅 | 提升服务效率 | 打造工作亮点</p>
              </div>
            </div>
            
            <div class="pain-points">
              <h4>告别三大工作难题</h4>
              <div class="pain-point">
                <div class="pain">
                  <div class="dot"></div>
                  <p>招商手段陈旧，缺乏吸引力？</p>
                </div>
                <div class="solution">
                  <span class="solution-arrow">→</span>
                  <p>AI赋能，让每一次推介都耳目一新。</p>
                </div>
              </div>
              
              <div class="pain-point">
                <div class="pain">
                  <div class="dot"></div>
                  <p>政策咨询量大，服务效率低？</p>
                </div>
                <div class="solution">
                  <span class="solution-arrow">→</span>
                  <p>7x24h AI顾问，解放80%重复工作。</p>
                </div>
              </div>
              
              <div class="pain-point">
                <div class="pain">
                  <div class="dot"></div>
                  <p>数字化工作，缺少创新亮点？</p>
                </div>
                <div class="solution">
                  <span class="solution-arrow">→</span>
                  <p>打造看得见的智慧政务标杆。</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 国有企业 -->
          <div class="customer-card">
            <div class="customer-header">
              <el-icon class="customer-icon enterprise"><OfficeBuilding /></el-icon>
              <div class="customer-title">
                <h3>我是国有企业</h3>
                <p class="customer-desc">赋能市场业务 | 统一品牌形象 | 创新对上汇报</p>
              </div>
            </div>
            
            <div class="pain-points">
              <h4>解决两大核心诉求</h4>
              <div class="pain-point">
                <div class="pain">
                  <div class="dot"></div>
                  <p>业务与党建，展示难以兼顾？</p>
                </div>
                <div class="solution">
                  <span class="solution-arrow">→</span>
                  <p>一个平台，完美融合市场与汇报需求。</p>
                </div>
              </div>
              
              <div class="pain-point">
                <div class="pain">
                  <div class="dot"></div>
                  <p>对外沟通繁杂，介绍效率低？</p>
                </div>
                <div class="solution">
                  <span class="solution-arrow">→</span>
                  <p>AI名片+推介官，让信息传递更高效。</p>
                </div>
              </div>
              
              <div class="pain-point">
                <div class="pain">
                  <div class="dot"></div>
                  <p>企业形象固化，缺乏科技感？</p>
                </div>
                <div class="solution">
                  <span class="solution-arrow">→</span>
                  <p>创新展示，塑造现代化国企新形象。</p>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- 四、核心功能模块 -->
        <section class="section features-section">
          <h2 class="section-title">我们通过四大核心能力实现</h2>
          
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon-container">
                <el-icon class="feature-icon"><Service /></el-icon>
              </div>
              <h3>AI数智推介官</h3>
              <p>您的7x24小时AI政策顾问与金牌讲解员。</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon-container">
                <el-icon class="feature-icon"><Monitor /></el-icon>
              </div>
              <h3>智慧推介门户</h3>
              <p>一个可随身携带、永不落幕的官方云展厅。</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon-container">
                <el-icon class="feature-icon"><VideoCamera /></el-icon>
              </div>
              <h3>AI宣传视频</h3>
              <p>告别高成本，快速生成专业、科技感的宣传视频。</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon-container">
                <el-icon class="feature-icon"><Promotion /></el-icon>
              </div>
              <h3>个人推介名片</h3>
              <p>让每一位员工，都成为单位形象的移动宣传窗口。</p>
            </div>
          </div>
        </section>
        
        <!-- 五、使用场景 -->
        <section class="section scenarios-section">
          <h2 class="section-title">使用场景</h2>
          
          <div class="scenarios-list">
            <div class="scenario-item">
              <div class="scenario-badge">招商推介</div>
              <p>替代传统PPT与宣传册，用科技感给投资者留下第一印象。</p>
            </div>
            
            <div class="scenario-item">
              <div class="scenario-badge">政策宣讲</div>
              <p>引导群众向AI提问，实现7x24小时标准答疑，服务不打烊。</p>
            </div>
            
            <div class="scenario-item">
              <div class="scenario-badge">会议展会</div>
              <p>现场扫码，将所有资料装进访客手机，彻底解决会后失联。</p>
            </div>
            
            <div class="scenario-item">
              <div class="scenario-badge">工作汇报</div>
              <p>向上级直观展示数字化成果，成为拿得出手的年度创新亮点。</p>
            </div>
            
            <div class="scenario-item">
              <div class="scenario-badge">全员推广</div>
              <p>为每位员工配备专属AI名片，统一对外口径，让每次社交都成为官方推介。</p>
            </div>
          </div>
        </section>
        
        <!-- 六、两步开启 -->
        <section class="section steps-section">
          <h2 class="section-title">两步开启，后顾无忧</h2>
          
          <div class="steps-container">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h3>您提供现有资料</h3>
                <p>只需把您平时用的宣传册、PPT、产品介绍等打包给我们</p>
              </div>
            </div>
            
            <div class="step-arrow">
              <el-icon><Connection /></el-icon>
            </div>
            
            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h3>我们为您打造一切</h3>
                <p>从策划、设计、视频制作到AI训练，我们全流程服务，您只需验收</p>
              </div>
            </div>
          </div>
          
          <div class="promise">
            <p>"把麻烦留给我们，把简单留给您。"</p>
          </div>
        </section>
        
        <!-- 七、立即行动 -->
        <section class="section cta-section">
          <h2 class="section-title">即刻体验，开启智慧推介新篇章</h2>
          
          <div class="cta-buttons">
            <el-button type="primary" size="large" class="cta-button" @click="showContactQRCode = true">
              <el-icon><DocumentCopy /></el-icon>
              预约专属方案演示
            </el-button>
            
            <el-button size="large" class="cta-button secondary" @click="openAIChatUrl">
              <el-icon><ChatDotRound /></el-icon>
              与我的AI宣传员聊聊
            </el-button>
          </div>
        </section>
      </div>
    </div>
    
    <!-- 联系二维码弹窗 -->
    <el-dialog v-model="showContactQRCode" title="扫码联系我们" width="80%" class="qrcode-dialog">
      <div class="qrcode-container">
        <img :src="contactQRCodeUrl" alt="联系二维码" class="contact-qrcode">
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #409EFF, #64b5f6);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem; /* 为底部标签栏留出空间 */
  width: 100%;
  box-sizing: border-box;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 通用部分 */
.section {
  margin-bottom: 1.5rem;
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 1.5rem;
  color: #333;
  margin-top: 0;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 600;
}

/* 视频部分 */
.video-section {
  text-align: center;
}

.guide-text {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.video-container {
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.promo-video {
  width: 100%;
  display: block;
  border-radius: 0.5rem;
}

/* 标语部分 */
.slogan-section {
  text-align: center;
  background: linear-gradient(135deg, #4a90e2, #64b5f6);
  color: white;
}

.slogan {
  font-size: 1.5rem;
  line-height: 1.4;
  font-weight: 600;
  margin: 0;
}

/* 客户画像部分 */
.customer-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.customer-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.customer-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
}

.customer-icon {
  font-size: 2rem;
  padding: 0.75rem;
  border-radius: 50%;
  color: white;
}

.government {
  background: linear-gradient(135deg, #4a90e2, #64b5f6);
}

.enterprise {
  background: linear-gradient(135deg, #3b7ddb, #5094e3);
}

.customer-title h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  color: #333;
}

.customer-desc {
  margin: 0;
  font-size: 0.85rem;
  color: #666;
}

.pain-points h4 {
  font-size: 1.1rem;
  color: #333;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.pain-point {
  margin-bottom: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.pain {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.dot {
  width: 0.5rem;
  height: 0.5rem;
  background: #4a90e2;
  border-radius: 50%;
  margin-top: 0.5rem;
}

.pain p, .solution p {
  margin: 0;
  font-size: 0.95rem;
}

.pain p {
  color: #444;
}

.solution {
  display: flex;
  margin-left: 1rem;
  gap: 0.5rem;
}

.solution-arrow {
  color: #4a90e2;
  font-weight: bold;
}

.solution p {
  color: #4a90e2;
  font-weight: 500;
}

/* 功能模块部分 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.feature-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.feature-icon-container {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2, #64b5f6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.feature-icon {
  font-size: 1.75rem;
  color: white;
}

.feature-card h3 {
  font-size: 1.1rem;
  color: #333;
  margin: 0 0 0.5rem;
}

.feature-card p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* 使用场景部分 */
.scenarios-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.scenario-item {
  background: #f8fbff;
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  border: 1px solid #e1eeff;
}

.scenario-badge {
  background: linear-gradient(135deg, #4a90e2, #64b5f6);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  align-self: flex-start;
}

.scenario-item p {
  margin: 0;
  font-size: 0.95rem;
  color: #555;
}

/* 两步开启部分 */
.steps-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

.step {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  width: 100%;
  background: #f8fbff;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid #e1eeff;
}

.step-number {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2, #64b5f6);
  color: white;
  font-size: 1.25rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.step-content {
  flex-grow: 1;
}

.step-content h3 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
  color: #333;
}

.step-content p {
  margin: 0;
  font-size: 0.95rem;
  color: #555;
}

.step-arrow {
  color: #4a90e2;
  font-size: 2rem;
  transform: rotate(90deg);
  margin: 0.5rem 0;
}

.promise {
  text-align: center;
  margin-top: 1.5rem;
}

.promise p {
  font-size: 1.1rem;
  font-style: italic;
  color: #4a90e2;
  font-weight: 500;
  margin: 0;
}

/* 立即行动部分 */
.cta-section {
  text-align: center;
}

.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 300px;
  margin: 0 auto;
}

/* 覆盖Element Plus的默认按钮间距 */
.cta-buttons .el-button + .el-button {
  margin-left: 0;
}

.cta-button {
  width: 100%;
  font-size: 1rem;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.cta-button.secondary {
  color: #4a90e2;
  border-color: #4a90e2;
}

/* 联系二维码弹窗 */
.qrcode-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.contact-qrcode {
  max-width: 100%;
  max-height: 300px;
}

/* 媒体查询 - 平板和桌面端 */
@media (min-width: 768px) {
  .slogan {
    font-size: 2rem;
  }
  
  .customer-section {
    flex-direction: row;
  }
  
  .customer-card {
    flex: 1;
  }
  
  .scenarios-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  
  .steps-container {
    flex-direction: row;
    align-items: center;
  }
  
  .step-arrow {
    transform: rotate(0);
  }
  
  .cta-buttons {
    flex-direction: row;
    max-width: 600px;
  }
  
  /* 桌面端可以恢复按钮间距 */
  .cta-buttons .el-button + .el-button {
    margin-left: 12px;
  }
}

@media (min-width: 992px) {
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .scenarios-list {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style> 