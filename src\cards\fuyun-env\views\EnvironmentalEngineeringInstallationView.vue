<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}



// 安装环节
const installationStages = reactive([
  {
    title: '设备选择',
    description: '根据工程需求和现场条件，选择最适合的环保设备，确保设备性能满足环保标准和工艺要求。',
    icon: '🔧'
  },
  {
    title: '布局设计',
    description: '根据现场环境进行专业的布局设计，确保工艺流程顺畅，设备配置合理，便于操作和维护。',
    icon: '📐'
  },
  {
    title: '施工安装',
    description: '严格按照设计方案和安全规范进行施工安装，确保每一个安装环节都符合环保标准和质量要求。',
    icon: '🏗️'
  },
  {
    title: '调试验收',
    description: '安装完成后进行设备调试与性能检测，确保设备正常运行并达到预定的环保效果。',
    icon: '✅'
  }
])

// 服务优势
const serviceAdvantages = reactive([
  {
    title: '专业团队',
    description: '拥有经验丰富的环保工程师和技术人员，提供专业的安装服务。',
    icon: '👥'
  },
  {
    title: '质量保证',
    description: '严格按照国家标准和行业规范进行安装，确保工程质量。',
    icon: '🏆'
  },
  {
    title: '快速响应',
    description: '提供快速响应服务，及时解决安装过程中的各种问题。',
    icon: '⚡'
  },
  {
    title: '售后保障',
    description: '提供完善的售后服务，确保设备长期稳定运行。',
    icon: '🛡️'
  }
])

// 安装案例
const installationCases = reactive([
  {
    title: '大型钢铁厂除尘系统安装',
    description: '为某大型钢铁厂安装了完整的除尘系统，包括布袋除尘器、管道系统等，有效控制了粉尘排放。',
    image: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/bb887e2d-4853-4baa-aa28-537a1fd5f903.jpg'
  },
  {
    title: '化工企业废气处理设备安装',
    description: '为化工企业安装了VOCs处理设备，采用催化燃烧技术，有效处理有机废气。',
    image: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/6036fe1c-816f-4b86-a941-c8aa24ac901f.jpg'
  },
  {
    title: '矿山除尘设备安装',
    description: '为露天矿山安装了钻机除尘器和电铲除尘器，显著改善了作业环境。',
    image: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/9f366c9a-2bb2-40c8-aecb-1bafc724aade.jpg'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>环保工程安装</h1>
    </div>

    <div class="content">
      <!-- 服务主图和基本信息 -->
      <div class="service-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/590ef2a5-edf6-4edd-a78d-5c447c97e4a2.jpg" alt="环保工程安装" />
        </div>
        <div class="hero-content">
          <h2>环保工程安装</h2>
          <p class="service-subtitle">专业的环保设备安装服务，确保工程质量和环保效果</p>
          <div class="service-intro">
            <p>我们提供全方位的环保工程安装服务，从设备选择、布局设计到施工安装、调试验收，每个环节都严格按照国家标准和行业规范执行。</p>
            <p>拥有专业的技术团队和丰富的工程经验，为客户提供高质量、高效率的环保工程安装解决方案。</p>
          </div>
        </div>
      </div>

      <!-- 安装流程 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔄</span>
          安装流程
        </h3>
        <div class="stages-grid">
          <div
            v-for="(stage, index) in installationStages"
            :key="index"
            class="stage-card"
          >
            <div class="stage-number">{{ index + 1 }}</div>
            <div class="stage-icon">{{ stage.icon }}</div>
            <h4>{{ stage.title }}</h4>
            <p>{{ stage.description }}</p>
          </div>
        </div>
      </div>

      <!-- 服务优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          服务优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in serviceAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

      <!-- 安装案例 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📸</span>
          安装案例
        </h3>
        <div class="cases-grid">
          <div
            v-for="(case_item, index) in installationCases"
            :key="index"
            class="case-card"
          >
            <div class="case-image">
              <img :src="case_item.image" :alt="case_item.title" />
            </div>
            <div class="case-content">
              <h4>{{ case_item.title }}</h4>
              <p>{{ case_item.description }}</p>
            </div>
          </div>
        </div>
      </div>

      

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 服务展示区域 */
.service-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.service-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.service-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.service-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 安装流程 */
.stages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.stage-card {
  background: #f8fafc;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  position: relative;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.stage-card:hover {
  border-color: #3b82f6;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.stage-number {
  position: absolute;
  top: -10px;
  left: 1.5rem;
  background: #3b82f6;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.stage-icon {
  font-size: 2.5rem;
  margin: 1rem 0;
}

.stage-card h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
}

.stage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.9rem;
}

/* 服务优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 安装案例 */
.cases-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.case-card {
  background: #f8fafc;
  border-radius: 1rem;
  overflow: hidden;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.case-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.case-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.case-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.case-content {
  padding: 1.25rem;
}

.case-content h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.case-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 联系我们部分 */
.contact-section {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #3b82f6;
}

.contact-content {
  text-align: center;
}

.contact-text {
  margin: 0 0 1.5rem 0;
  color: #4b5563;
  font-size: 1rem;
  line-height: 1.5;
}

.contact-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.contact-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.call-btn {
  background: #3b82f6;
  color: white;
}

.call-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.copy-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.copy-btn:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.phone-number {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .service-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .service-subtitle {
    font-size: 1.1rem;
  }

  .service-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .stages-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .cases-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-buttons {
    gap: 1.5rem;
  }

  .contact-btn {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .stages-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .cases-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
