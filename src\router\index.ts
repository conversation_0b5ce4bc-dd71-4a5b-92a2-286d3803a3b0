import { createRouter, createWebHistory } from 'vue-router'
import aiSzAgentRoutes from '../cards/ai-sz-agent/router'
import gesiXiehuiRoutes from '../cards/gesi-union/router'
import lintianTechRoutes from '../cards/lintian-tech/router'
import wanWangKeJiRoutes from '../cards/wanwang-tech/router'
import aiSzGuideRoutes from '../cards/ai-sz-guide/router'
import starmapTechRoutes from '../cards/starmap-tech/router'
import fuyunEnvRoutes from '../cards/fuyun-env/router'
import hansUnionRoutes from '../cards/hans-union/router'



const router = createRouter({
  history: createWebHistory(),
  routes: [
    // 主页路由 - 项目选择页面
    {
      path: '/',
      name: 'projectSelection',
      component: () => import('../views/ProjectSelectionView.vue'),
      meta: {
        title: '项目选择 - 项目名片中心'
      }
    },
    // 将AI数智代言人的路由添加到 /card/ai-sz-agent 前缀下
    ...aiSzAgentRoutes.map(route => ({
      ...route,
      path: `/card/ai-sz-agent${route.path}`
    })),
    // 将个私协会的路由添加到 /card/gesi-union 前缀下
    ...gesiXiehuiRoutes.map(route => ({
      ...route,
      path: `/card/gesi-union${route.path}`
    })),

    // 将霖天科技的路由添加到 /card/lintian-tech 前缀下
    ...lintianTechRoutes.map(route => ({
      ...route,
      path: `/card/lintian-tech${route.path}`
    })),
    // 将万网科技的路由添加到 /card/wanwang-tech 前缀下
    ...wanWangKeJiRoutes.map(route => ({
      ...route,
      path: `/card/wanwang-tech${route.path}`
    })),
   // 将AI数智推介官的路由添加到 /card/ai-sz-guide 前缀下
    ...aiSzGuideRoutes.map(route => ({
      ...route,
      path: `/card/ai-sz-guide${route.path}`
    })),
    // 将星图AI的路由添加到 /card/starmap-tech 前缀下
    ...starmapTechRoutes.map(route => ({
      ...route,
      path: `/card/starmap-tech${route.path}`
    })),

    // 将福运环保的路由添加到 /card/fuyun-env 前缀下
    ...fuyunEnvRoutes.map(route => ({
      ...route,
      path: `/card/fuyun-env${route.path}`
    })),

    // 将汉氏联合的路由添加到 /card/hans-union 前缀下
    ...hansUnionRoutes.map(route => ({
      ...route,
      path: `/card/hans-union${route.path}`
    })),



  ],
  // 添加滚动行为控制
  scrollBehavior(_to, _from, _savedPosition) {
    // 始终滚动到顶部
    return { top: 0 }
  }
})

export default router