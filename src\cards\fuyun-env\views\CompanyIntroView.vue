<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLef<PERSON>,
  Trophy,
  Setting,
  Star,
  ArrowLeftBold,
  ArrowRightBold
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

import ImageViewer from '../../../components/ImageViewer.vue'

const router = useRouter()

// 图片查看器功能
const imageViewerVisible = ref(false)
const currentImage = ref('')

const showImageViewer = (imageUrl: string) => {
  currentImage.value = imageUrl
  imageViewerVisible.value = true
}

// 荣誉资质轮播图数据
const honorsData = [
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/26259849-b912-4b71-afab-91a0ddff40d8.jpg',
    title: '专利1：一种垂直度校正仪'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/9a8a57fe-6206-4d64-834b-65ffbb5f5add.jpg',
    title: '专利2：一种液压滤纸挤压机'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/3da30734-db47-494d-9d53-a3e71c2930a6.jpg',
    title: '专利3: 一种电铲过滤装置'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/fa688008-2df8-4131-b816-627e520097ed.jpg',
    title: '专利4: 一种液压油滤芯'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/e1eee430-9406-4ab2-96a3-fe101b414473.jpg',
    title: '专利5: 一种移动钻机过滤装置'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/e199cc9a-c079-4234-8116-e5861e87122d.jpg',
    title: '专利6: 一种除尘滤芯支撑架'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/d49db8c6-cce5-4a63-96c0-5de2a848b175.jpg',
    title: '专利7: 一种旋风式初级滤芯'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/b8ebd30f-6bad-487a-adc1-75658a9ad02f.jpg',
    title: '专利8: 一种焊接烟尘净化装置'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/fc860190-b537-4847-920c-0b5ac4aaa65c.jpg',
    title: '专利9：一种集成式焊烟净化系统装置'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/6fd63880-e048-4302-b41b-6e45a0af5ab8.jpg',
    title: '专利10：一种矿山钻机除尘装置'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/a0c99b7c-cd94-4c95-a7fa-149171b9f240.jpg',
    title: '专利11：一种高效多管旋风除尘器'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/b9e1cf58-1b60-4d79-ba15-f414d17486ce.jpg',
    title: '江西省环境污染治理工程总承包能力评价证书'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/6f821e77-f12e-463e-b6ef-82234decca5c.jpg',
    title: '江西省环境污染治理工程设计能力评价证书'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/5fa66519-68f9-49ff-8531-6eb9a586958e.jpg',
    title: '质量管理体系'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/022402da-239f-4b13-818d-47560c6a0c06.jpg',
    title: '职业健康管理体系'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/be7fdda3-5c26-45c8-9872-2ff004bebfec.jpg',
    title: '环境管理体系'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/3b38adf6-5cbb-461c-9551-fd659fdfb2ae.jpg',
    title: '江西省著名商标'
  },
  {
    url: 'https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/0fe08e9b-c775-4ed7-a143-42a056bad2c0.jpg',
    title: '2017年江西省专精特新中小企业'
  }
]

// 轮播图功能
const currentHonorIndex = ref(0)

const nextHonor = () => {
  currentHonorIndex.value = (currentHonorIndex.value + 1) % honorsData.length
}

const prevHonor = () => {
  currentHonorIndex.value = currentHonorIndex.value === 0 ? honorsData.length - 1 : currentHonorIndex.value - 1
}

const goToHonor = (index: number) => {
  currentHonorIndex.value = index
}

// 手风琴展开状态
const activeAccordion = ref(['mining'])

const goBack = () => {
  router.push('/card/fuyun-env')
}






</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>公司介绍</h1>
    </div>

    <div class="content">
      <!-- 英雄区域 -->
      <div class="hero-section">
        <div class="hero-background">
          <div class="hero-overlay"></div>
          <div class="hero-content">
            <div class="company-badge">
             
            </div>
            <h2 class="hero-title">江西福运环保科技有限公司</h2>
            <p class="hero-subtitle">优质除尘设备系统解决方案专家</p>
            <div class="hero-highlights">
              <div class="highlight-item">
                <div class="highlight-number">2002年</div>
                <div class="highlight-label">公司成立时间</div>
              </div>
              <div class="highlight-item">
                <div class="highlight-number">20余载</div>
                <div class="highlight-label">专注行业</div>
              </div>
              <div class="highlight-item">
                <div class="highlight-number">300+</div>
                <div class="highlight-label">知识产权</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 宣传视频 -->
      <div class="video-section">
        <div class="section-header-modern">
          <div class="header-line"></div>
          <h3 class="section-title-modern">企业宣传片</h3>
          <div class="header-line"></div>
        </div>
        <div class="video-container">
           <video controls playsinline preload="metadata" poster="" style="width: 100%; border-radius: 8px;">
              <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/FuYunHuanBao/xcsp.mp4" type="video/mp4">
              您的浏览器不支持视频播放
            
            您的浏览器不支持视频播放
          </video>
         
        </div>
      </div>

      <!-- 公司介绍 -->
      <div class="intro-section">
        <div class="section-header-modern">
          <div class="header-line"></div>
          <h3 class="section-title-modern">关于福运</h3>
          <div class="header-line"></div>
        </div>
        <div class="intro-content">
          <div class="intro-main">
            <div class="intro-highlight">
              <span class="highlight-tag">国企背景</span>
              <span class="highlight-tag">技术领先</span>
              <span class="highlight-tag">品质保证</span>
            </div>
            <p class="intro-text">
              江西福运环保科技成立于2002年，由江西福事特液压和江西铜业集团联合创立，深耕工业环保领域20余年。我们专注于为金属冶炼、矿山开采、机械制造等行业提供高效可靠的环保过滤解决方案，产品已成功应用于江西铜业等数百家大型企业，覆盖全国市场。
            </p>
          </div>
          
            
        </div>
      </div>

      
      <!-- 核心产品与服务 -->
      <div class="products-section">
        <div class="section-header-modern">
          <div class="header-line"></div>
          <h3 class="section-title-modern">核心产品与服务</h3>
          <div class="header-line"></div>
        </div>

        <div class="product-cards">
          <div class="product-card">
            <div class="product-image">
              <div class="product-icon">🏭</div>
           
            </div>
            <div class="product-content">
              <h4>工业除尘系统</h4>
              <p>专业为矿山机械（钻机、电铲等）、冶金设备配套高效除尘解决方案</p>
              <div class="product-features">
                <span class="feature-tag">钻机除尘</span>
                <span class="feature-tag">电铲除尘</span>
              </div>
            </div>
          </div>

          <div class="product-card">
            <div class="product-image">
              <div class="product-icon">🏢</div>
            </div>
            <div class="product-content">
              <h4>洁净厂房过滤系统</h4>
              <p>提供各类空气过滤设备，满足不同洁净度要求</p>
              <div class="product-features">
                <span class="feature-tag">空气过滤</span>
                <span class="feature-tag">洁净标准</span>
              </div>
            </div>
          </div>

          <div class="product-card">
            <div class="product-image">
              <div class="product-icon">🔧</div>
              
            </div>
            <div class="product-content">
              <h4>环保工程总包服务</h4>
              <p>承接各类工业环保工程项目，提供设计、施工、运维全流程服务</p>
              <div class="product-features">
                <span class="feature-tag">工程设计</span>
                <span class="feature-tag">运维服务</span>
              </div>
            </div>
          </div>
        </div>
      </div>
<!-- 核心竞争力 -->
      <div class="competitiveness-section">
        <div class="section-header-modern">
          <div class="header-line"></div>
          <h3 class="section-title-modern">核心竞争力</h3>
          <div class="header-line"></div>
        </div>

        <div class="advantage-cards">
          <div class="advantage-card featured">
            <div class="card-glow"></div>
            <div class="advantage-header">
              <div class="advantage-icon-wrapper">
                <el-icon class="advantage-icon"><Setting /></el-icon>
              </div>
              <h4>技术研发领先</h4>
            </div>
            <div class="advantage-content">
              <div class="achievement-badge">首创技术</div>
              <ul class="advantage-list">
                <li>
                  <span class="achievement-year">2008年</span>
                  拥有独立产品检测实验室
                </li>

                <li>
                  <span class="achievement-year">2013年</span>
                  江西福运新厂房投产使用自主研发国内第一台电铲除尘设备
                </li>

                <li>
                  <span class="achievement-year">2015年</span>
                  自主研发国内首台钻机除尘设备
                </li>

                <li>
                  <span class="achievement-year">2017年</span>
                  获评"江西省专精特新企业"
                </li>
              </ul>
            </div>
          </div>

          <div class="advantage-card">
            <div class="advantage-header">
              <div class="advantage-icon-wrapper">
                <el-icon class="advantage-icon"><Star /></el-icon>
              </div>
              <h4>全产业链服务</h4>
            </div>
            <div class="advantage-content">
              <div class="service-flow">
                <div class="flow-item">咨询</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">设计</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">制造</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">维护</div>
              </div>
              <p class="advantage-desc">
                提供从前期咨询、方案设计到设备制造、安装调试、售后维护的一站式服务，真正实现"交钥匙工程"。
              </p>
            </div>
          </div>

          <div class="advantage-card">
            <div class="advantage-header">
              <div class="advantage-icon-wrapper">
                <el-icon class="advantage-icon"><Trophy /></el-icon>
              </div>
              <h4>质量管控体系</h4>
            </div>
            <div class="advantage-content">
              <div class="certification-grid">
                <div class="cert-item">
                  <div class="cert-icon">🏆</div>
                  <div class="cert-text">ISO9001认证</div>
                </div>
                <div class="cert-item">
                  <div class="cert-icon">⭐</div>
                  <div class="cert-text">著名商标</div>
                </div>
                <div class="cert-item">
                  <div class="cert-icon">✅</div>
                  <div class="cert-text">诚信单位</div>
                </div>
                <div class="cert-item">
                  <div class="cert-icon">🔍</div>
                  <div class="cert-text">严格检测</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择福运的5大理由 -->
      <div class="reasons-section">
        <div class="section-header-modern">
          <div class="header-line"></div>
          <h3 class="section-title-modern">选择福运的5大理由</h3>
          <div class="header-line"></div>
        </div>

        <div class="reasons-grid">
          <div class="reason-card">
            <div class="reason-header">
              <div class="reason-number">1</div>
              <div class="reason-icon"></div>
            </div>
            <div class="reason-content">
              <h4>国企背景</h4>
              <p>江西铜业集团参股，实力保障，信誉可靠</p>
            </div>
          </div>

          <div class="reason-card">
            <div class="reason-header">
              <div class="reason-number">2</div>
              <div class="reason-icon"></div>
            </div>
            <div class="reason-content">
              <h4>技术专利</h4>
              <p>多项自主研发的核心技术，行业领先</p>
            </div>
          </div>

          <div class="reason-card">
            <div class="reason-header">
              <div class="reason-number">3</div>
              <div class="reason-icon"></div>
            </div>
            <div class="reason-content">
              <h4>品质保证</h4>
              <p>20年行业经验，产品稳定可靠</p>
            </div>
          </div>

          <div class="reason-card">
            <div class="reason-header">
              <div class="reason-number">4</div>
              <div class="reason-icon"></div>
            </div>
            <div class="reason-content">
              <h4>服务网络</h4>
              <p>全国服务网点，快速响应客户需求</p>
            </div>
          </div>

          <div class="reason-card">
            <div class="reason-header">
              <div class="reason-number">5</div>
              <div class="reason-icon"></div>
            </div>
            <div class="reason-content">
              <h4>定制能力</h4>
              <p>可根据客户需求提供个性化解决方案</p>
            </div>
          </div>
        </div>
      </div>

       <!-- 工程案例 -->
      <div class="project-cases-section">
        <div class="section-header-modern">
          <div class="header-line"></div>
          <h3 class="section-title-modern">工程案例</h3>
          <div class="header-line"></div>
        </div>

        <el-collapse v-model="activeAccordion" class="case-accordion">
          <!-- 矿山领域 -->
          <el-collapse-item title="矿山领域" name="mining">
            <div class="case-images-grid">
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K1.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K1.jpg" alt="矿山案例1" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K2.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K2.jpg" alt="矿山案例2" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K3.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K3.jpg" alt="矿山案例3" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K4.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K4.jpg" alt="矿山案例4" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K5.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K5.jpg" alt="矿山案例5" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K6.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/K6.jpg" alt="矿山案例6" />
              </div>
            </div>
          </el-collapse-item>

          <!-- 冶金行业 -->
          <el-collapse-item title="冶金行业" name="metallurgy">
            <div class="case-images-grid">
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/y1.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/y1.jpg" alt="冶金案例1" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/y2.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/y2.jpg" alt="冶金案例2" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/y3.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/y3.jpg" alt="冶金案例3" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/y4.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/y4.jpg" alt="冶金案例4" />
              </div>
            </div>
          </el-collapse-item>

          <!-- 工业制造 -->
          <el-collapse-item title="工业制造" name="manufacturing">
            <div class="case-images-grid">
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g1.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g1.jpg" alt="工业制造案例1" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g2.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g2.jpg" alt="工业制造案例2" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g3.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g3.jpg" alt="工业制造案例3" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g4.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g4.jpg" alt="工业制造案例4" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g5.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/g5.jpg" alt="工业制造案例5" />
              </div>
            </div>
          </el-collapse-item>

          <!-- 电力化工 -->
          <el-collapse-item title="电力化工" name="power">
            <div class="case-images-grid">
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/d1.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/d1.jpg" alt="电力化工案例1" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/d2.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/d2.jpg" alt="电力化工案例2" />
              </div>
            </div>
          </el-collapse-item>

          <!-- 能源建材 -->
          <el-collapse-item title="能源建材" name="energy">
            <div class="case-images-grid">
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/n1.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/n1.jpg" alt="能源建材案例1" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/n2.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/n2.jpg" alt="能源建材案例2" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/n3.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/n3.jpg" alt="能源建材案例3" />
              </div>
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/n4.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/n4.jpg" alt="能源建材案例4" />
              </div>
            </div>
          </el-collapse-item>

          <!-- 电子制药 -->
          <el-collapse-item title="电子制药" name="electronics">
            <div class="case-images-grid">
              <div class="case-image-item" @click="showImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/z1.jpg')">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/gongchenganli/gcal-fl/z1.jpg" alt="电子制药案例1" />
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

       <!-- 荣誉资质 -->
      <div class="honors-section">
        <div class="section-header-modern">
          <div class="header-line"></div>
          <h3 class="section-title-modern">荣誉资质</h3>
          <div class="header-line"></div>
        </div>

        <div class="honors-carousel">
          <!-- 主图显示区域 -->
          <div class="carousel-main">
            <div class="carousel-image-container" @click="showImageViewer(honorsData[currentHonorIndex].url)">
              <img
                :src="honorsData[currentHonorIndex].url"
                :alt="honorsData[currentHonorIndex].title"
                class="carousel-main-image"
              />
              <div class="image-overlay">
                <div class="click-hint">点击查看大图</div>
              </div>
            </div>

            <!-- 导航按钮 -->
            <button class="carousel-nav prev" @click="prevHonor" :disabled="honorsData.length <= 1">
              <el-icon><ArrowLeftBold /></el-icon>
            </button>
            <button class="carousel-nav next" @click="nextHonor" :disabled="honorsData.length <= 1">
              <el-icon><ArrowRightBold /></el-icon>
            </button>
          </div>

          <!-- 图片标题 -->
          <div class="image-title-display">
            <h4>{{ honorsData[currentHonorIndex].title }}</h4>
          </div>

          <!-- 指示器 -->
          <div class="carousel-indicators">
            <span
              v-for="(_, index) in honorsData"
              :key="index"
              class="indicator"
              :class="{ active: index === currentHonorIndex }"
              @click="goToHonor(index)"
            ></span>
          </div>
        </div>

      </div>

      </div>

    <TabBar />
  </div>

  
  <!-- 图片查看器 -->
  <ImageViewer
    v-model:visible="imageViewerVisible"
    :image-url="currentImage"
  />
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(30, 52, 112, 0.2);
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
  transition: transform 0.2s;
}

.back-button:hover {
  transform: scale(1.1);
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 英雄区域 */
.hero-section {
  margin-bottom: 2rem;
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(30, 52, 112, 0.15);
}

.hero-background {
  background: linear-gradient(135deg, #1e3470 0%, #2d4a8a 50%, #3b5998 100%);
  position: relative;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
}

.hero-content {
  position: relative;
  z-index: 2;
  padding: 3rem 2rem;
  text-align: center;
  color: white;
}

.company-badge {
  display: inline-block;
  margin-bottom: 1rem;
  animation: slideInDown 0.8s ease-out 0.2s both;
}

@keyframes slideInDown {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

.badge-text {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  line-height: 1.2;
  animation: slideInUp 0.8s ease-out 0.4s both;
}

@keyframes slideInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.hero-subtitle {
  font-size: 1.2rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  animation: slideInUp 0.8s ease-out 0.6s both;
}

.hero-highlights {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  animation: slideInUp 0.8s ease-out 0.8s both;
}

.highlight-item {
  text-align: center;
  min-width: 80px;
}

.highlight-number {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: #ffd700;
}

.highlight-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* 现代化section标题 */
.section-header-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.header-line {
  flex: 1;
  height: 2px;

  max-width: 100px;
}

.section-title-modern {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
  margin: 0;
  text-align: center;
  position: relative;
  padding: 0 1rem;
}

.section-title-modern::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;

  border-radius: 2px;
}

/* 视频部分 */
.video-section {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(30, 52, 112, 0.1);
  animation: slideInUp 0.6s ease-out;
}

.video-container {
  position: relative;
  width: 100%;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.promo-video {
  width: 100%;
  height: auto;
  display: block;
}

.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  opacity: 1;
  transition: all 0.3s ease;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
}

.video-container:hover .video-play-overlay {
  background: rgba(0, 0, 0, 0.5);
}

.play-button {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border: 3px solid rgba(255, 255, 255, 0.8);
}

.play-button:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.play-icon {
  font-size: 2rem;
  color: #1e3470;
  margin-left: 3px; /* 视觉上居中播放图标 */
}

/* 通用section样式 */
.intro-section,
.competitiveness-section,
.products-section,
.reasons-section,
.contact-section {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(30, 52, 112, 0.08);
  animation: slideInUp 0.6s ease-out;
  transition: transform 0.3s, box-shadow 0.3s;
}

/* 移动端section间距优化 */
@media (max-width: 767px) {
  .intro-section,
  .competitiveness-section,
  .products-section,
  .reasons-section,
  .contact-section {
    border-radius: 1.25rem;
  }
}

.intro-section:hover,
.competitiveness-section:hover,
.products-section:hover,
.reasons-section:hover,
.contact-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(30, 52, 112, 0.12);
}

/* 介绍部分 */
.intro-content {
  display: grid;
  gap: 2rem;
}

.intro-main {
  order: 2;
}

.intro-highlight {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.highlight-tag {
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(30, 52, 112, 0.3);
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin: 0;
}

.intro-stats {
  display: flex;
  gap: 1.5rem;
  order: 1;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #f8fbff, #e8f4fd);
  padding: 1.5rem;
  border-radius: 1rem;
  flex: 1;
  border: 1px solid #e1eeff;
}

.stat-icon {
  font-size: 2rem;
}

.stat-content {
  text-align: center;
}

.stat-number {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e3470;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* 优势卡片 */
.advantage-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-card {
  background: linear-gradient(135deg, #ffffff, #ced8e6);
  border: 2px solid #e5e7eb;
  border-radius: 1rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
}

.advantage-card.featured {
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  border-color: #1e3470;
}

.advantage-card.featured .advantage-icon {
  color: #ffd700;
}

.advantage-card.featured .achievement-year {
  color: #ffd700;
}

.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
  animation: rotate 10s linear infinite;
  pointer-events: none;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.advantage-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(30, 52, 112, 0.2);
}

.advantage-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 1.5rem;
}

.advantage-icon-wrapper {
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, #e8f4fd, #f8fbff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 15px rgba(30, 52, 112, 0.1);
}

.advantage-card.featured .advantage-icon-wrapper {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.advantage-icon {
  font-size: 1.25rem;
  color: #1e3470;
}

.advantage-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
  margin: 0;
  line-height: 1.3;
}

.advantage-card.featured .advantage-header h4 {
  color: white;
}

.advantage-content {
  position: relative;
  z-index: 2;
  margin-top: 0.75rem;
}

.achievement-badge {
  display: inline-block;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #1e3470;
  padding: 0.4rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.advantage-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.advantage-list li {
  font-size: 0.9rem;
  color: #555;
  margin-bottom: 0.5rem;
  padding-left: 1.25rem;
  position: relative;
  line-height: 1.4;
}

.advantage-card.featured .advantage-list li {
  color: rgba(255, 255, 255, 0.9);
}

.advantage-list li::before {
  content: '✓';
  color: #1e3470;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.advantage-card.featured .advantage-list li::before {
  color: #ffd700;
}

.achievement-year {
  display: inline-block;
  background: rgba(30, 52, 112, 0.1);
  color: #1e3470;
  padding: 0.2rem 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 0.5rem;
}

.service-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.flow-item {
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(30, 52, 112, 0.2);
}

.flow-arrow {
  color: #1e3470;
  font-weight: bold;
  font-size: 1.2rem;
}

.certification-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.cert-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(30, 52, 112, 0.05);
  padding: 1rem;
  border-radius: 0.75rem;
  border: 1px solid rgba(30, 52, 112, 0.1);
}

.cert-icon {
  font-size: 1.5rem;
}

.cert-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #1e3470;
}

.advantage-desc {
  font-size: 1rem;
  color: #555;
  line-height: 1.6;
  margin: 0;
}

/* 产品卡片 */
.product-cards {
  display: grid;
  gap: 1.5rem;
}

.product-card {
  background: white;
  border-radius: 1.25rem;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.1);
  transition: all 0.4s ease;
  position: relative;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(30, 52, 112, 0.2);
}

.product-image {
  position: relative;
  height: 120px;
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
}

.product-icon {
  font-size: 3rem;
  position: relative;
  z-index: 2;
}

.product-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 3;
}

.product-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.product-content {
  padding: 1.5rem;
}

.product-content h4 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e3470;
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.product-content p {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.product-features {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.feature-tag {
  background: linear-gradient(135deg, #e8f4fd, #f0f8ff);
  color: #1e3470;
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #e1eeff;
}

/* 选择理由 - 网格布局 */
.reasons-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.reason-card {
  background: linear-gradient(135deg, #ffffff, #f8fbff);
  border-radius: 1.5rem;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  border: 1px solid #e1eeff;
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.1);
}

.reason-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;

  border-radius: 1.5rem 1.5rem 0 0;
}

.reason-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(30, 52, 112, 0.2);
}

.reason-card:nth-child(1) { animation-delay: 0.1s; }
.reason-card:nth-child(2) { animation-delay: 0.2s; }
.reason-card:nth-child(3) { animation-delay: 0.3s; }
.reason-card:nth-child(4) { animation-delay: 0.4s; }
.reason-card:nth-child(5) { animation-delay: 0.5s; }

.reason-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.reason-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.3rem;
  box-shadow: 0 6px 20px rgba(30, 52, 112, 0.3);
  position: relative;
}

.reason-number::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s;
}

.reason-card:hover .reason-number::after {
  opacity: 1;
}

.reason-icon {
  font-size: 2.5rem;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.reason-card:hover .reason-icon {
  transform: scale(1.1);
  opacity: 1;
}

.reason-content h4 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e3470;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.reason-content p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.reason-highlight {
  display: inline-block;
  background: linear-gradient(135deg, #e8f4fd, #f0f8ff);
  color: #1e3470;
  padding: 0.5rem 1rem;
  border-radius: 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid #e1eeff;
  transition: all 0.3s ease;
}

.reason-card:hover .reason-highlight {
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  transform: scale(1.05);
}

/* 联系我们 */
.company-slogan {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1eeff;
}

.company-slogan p {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  color: #333;
  line-height: 1.4;
}

.slogan-text {
  font-weight: 600;
  color: #1e3470 !important;
}

.contact-info {
  display: grid;
  gap: 1rem;
}

.contact-item {
  background: #ffffff;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.contact-item:hover {
  border-color: #1e3470;
  box-shadow: 0 2px 8px rgba(30, 52, 112, 0.1);
}

.contact-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.contact-icon {
  font-size: 1.2rem;
  color: #1e3470;
  margin-right: 0.75rem;
}

.contact-label {
  font-weight: 650;
  color: #374151;
  font-size: 0.95rem;
}

.contact-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.contact-value {
  color: #555;
  font-size: 1rem;
  line-height: 1.5;
  flex: 1;
  word-break: break-all;
}

.contact-link {
  color: #1e3470;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.contact-link:hover {
  color: #2d4a8a;
  text-decoration: underline;
}

.contact-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.contact-actions .el-button {
  border-radius: 0.375rem;
}

/* 特殊样式 */
.address-item {
  grid-column: 1 / -1;
}

/* 响应式设计 */
@media (max-width: 767px) {
  /* 减少整体间距，让布局更紧凑 */
  .content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-bottom: 3.5rem;
  }

  /* 减少各section之间的间距 */
  .hero-section,
  .video-section,
  .intro-section,
  .competitiveness-section,
  .products-section,
  .reasons-section,
  .contact-section {
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    border-radius: 0.75rem;
  }

  /* 英雄区域优化 */
  .hero-content {
    padding: 1rem 0.75rem;
  }

  .hero-title {
    font-size: 1.25rem;
    line-height: 1.2;
    margin-bottom: 0.4rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.3;
  }

  .hero-highlights {
    gap: 0.5rem;
    justify-content: space-around;
  }

  .highlight-item {
    min-width: 60px;
  }

  .highlight-number {
    font-size: 1.1rem;
    margin-bottom: 0.1rem;
  }

  .highlight-label {
    font-size: 0.7rem;
    line-height: 1.1;
  }

  /* section标题优化 */
  .section-title-modern {
    font-size: 1.1rem;
    line-height: 1.2;
  }

  .section-header-modern {
    margin-bottom: 0.75rem;
  }

  /* 全局文字行距优化 */
  p {
    line-height: 1.4;
  }

  h4 {
    line-height: 1.2;
  }

  /* 介绍部分优化 - 改为两列布局 */
  .intro-content {
    gap: 0.75rem;
  }

  .intro-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .stat-item {
    padding: 0.5rem;
    flex-direction: column;
    text-align: center;
    gap: 0.3rem;
  }

  .stat-icon {
    font-size: 1.3rem;
  }

  .stat-number {
    font-size: 0.9rem;
    margin-bottom: 0.1rem;
  }

  .stat-label {
    font-size: 0.75rem;
    line-height: 1.1;
  }

  .intro-text {
    font-size: 0.9rem;
    line-height: 1.3;
  }

  /* 高亮标签优化 */
  .intro-highlight {
    gap: 0.3rem;
    margin-bottom: 0.75rem;
  }

  .highlight-tag {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    line-height: 1.1;
  }

  /* 特色标签优化 */
  .product-features {
    gap: 0.25rem;
  }

  .feature-tag {
    padding: 0.15rem 0.4rem;
    font-size: 0.65rem;
    line-height: 1.1;
  }

  /* 优势卡片优化 - 移动端保持单列布局 */
  .advantage-cards {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .advantage-card {
    padding: 0.75rem;
  }

  .advantage-card.featured {
    grid-column: 1 / -1;
    padding: 1rem;
  }

  .advantage-header {
    margin-bottom: 0.5rem;
  }

  .advantage-header h4 {
    font-size: 1rem;
    line-height: 1.2;
  }

  .advantage-icon-wrapper {
    width: 32px;
    height: 32px;
    margin-right: 0.4rem;
  }

  .advantage-icon {
    font-size: 1rem;
  }

  .advantage-list li {
    font-size: 0.85rem;
    margin-bottom: 0.4rem;
    line-height: 1.3;
  }

  .advantage-desc {
    font-size: 0.85rem;
    line-height: 1.3;
  }

  /* 产品卡片优化 - 改为单列布局 */
  .product-cards {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .product-card {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0.75rem;
    min-height: 85px;
  }

  .product-image {
    position: relative;
    width: 65px;
    height: 65px;
    flex-shrink: 0;
    margin-right: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1e3470, #2d4a8a);
    border-radius: 0.75rem;
    overflow: visible;
  }

  .product-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
    border-radius: 1rem;
  }

  .product-icon {
    font-size: 2rem;
    position: relative;
    z-index: 2;
  }

  .product-overlay {
    position: absolute;
    top: -5px;
    right: -5px;
    z-index: 3;
  }

  .product-tag {
    background: rgba(255, 255, 255, 0.9);
    color: #1e3470;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(30, 52, 112, 0.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    font-weight: 500;
  }

  .product-content {
    flex: 1;
    padding: 0;
  }

  .product-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
    line-height: 1.2;
    color: #1e3470;
    font-weight: 600;
  }

  .product-content p {
    font-size: 0.85rem;
    margin-bottom: 0;
    line-height: 1.3;
    color: #666;
  }

  /* 选择理由优化 - 美观卡片布局 */
  .reasons-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0;
  }

  .reason-card {
    position: relative;
    padding: 1.25rem;
    border-radius: 1rem;
    background: linear-gradient(135deg, #ffffff, #f8faff);
    border: 1px solid #e1eeff;
    box-shadow: 0 4px 20px rgba(30, 52, 112, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .reason-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e3470, #2d4a8a, #4a6fa5);
    border-radius: 1rem 1rem 0 0;
  }

  /* 每个卡片不同的顶部颜色条 */
  .reason-card:nth-child(1)::before {
    background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
  }

  .reason-card:nth-child(2)::before {
    background: linear-gradient(90deg, #4ecdc4, #7fdbda);
  }

  .reason-card:nth-child(3)::before {
    background: linear-gradient(90deg, #45b7d1, #74c7e3);
  }

  .reason-card:nth-child(4)::before {
    background: linear-gradient(90deg, #96ceb4, #b8dcc6);
  }

  .reason-card:nth-child(5)::before {
    background: linear-gradient(90deg, #feca57, #fed976);
  }

  .reason-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(30, 52, 112, 0.15);
  }

  .reason-card {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .reason-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    gap: 0.3rem;
  }

  .reason-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    font-weight: 700;
    color: white;
    background: linear-gradient(135deg, #1e3470, #2d4a8a);
    box-shadow: 0 4px 15px rgba(30, 52, 112, 0.3);
  }

  .reason-icon {
    display: none;
  }

  .reason-content {
    flex: 1;
    text-align: left;
  }

  .reason-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.4rem;
    line-height: 1.3;
    color: #1e3470;
    font-weight: 600;
  }

  .reason-content p {
    font-size: 0.9rem;
    margin-bottom: 0;
    line-height: 1.4;
    color: #666;
  }

  /* 每个数字不同的渐变色 */
  .reason-card:nth-child(1) .reason-number {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  }

  .reason-card:nth-child(2) .reason-number {
    background: linear-gradient(135deg, #4ecdc4, #7fdbda);
  }

  .reason-card:nth-child(3) .reason-number {
    background: linear-gradient(135deg, #45b7d1, #74c7e3);
  }

  .reason-card:nth-child(4) .reason-number {
    background: linear-gradient(135deg, #96ceb4, #b8dcc6);
  }

  .reason-card:nth-child(5) .reason-number {
    background: linear-gradient(135deg, #feca57, #fed976);
  }

  /* 添加进入动画 */
  .reason-card {
    animation: slideInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .reason-card:nth-child(1) { animation-delay: 0.1s; }
  .reason-card:nth-child(2) { animation-delay: 0.2s; }
  .reason-card:nth-child(3) { animation-delay: 0.3s; }
  .reason-card:nth-child(4) { animation-delay: 0.4s; }
  .reason-card:nth-child(5) { animation-delay: 0.5s; }

  @keyframes slideInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .reason-highlight {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  /* 认证网格优化 - 改为两列布局 */
  .certification-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .cert-item {
    padding: 0.5rem;
    flex-direction: column;
    text-align: center;
    gap: 0.3rem;
  }

  .cert-text {
    font-size: 0.8rem;
    line-height: 1.2;
  }

  /* 服务流程优化 - 紧凑横向布局 */
  .service-flow {
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 0.4rem;
    flex-wrap: wrap;
  }

  .flow-arrow {
    font-size: 0.8rem;
    color: #1e3470;
  }

  .flow-item {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
  }

  /* 联系信息优化 - 改为两列布局 */
  .contact-info {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .contact-item {
    padding: 0.75rem;
  }

  .address-item {
    grid-column: 1 / -1;
  }

  .website-item {
    grid-column: 1 / -1;
  }

  .contact-header {
    margin-bottom: 0.5rem;
  }

  .contact-icon {
    font-size: 1.1rem;
    margin-right: 0.5rem;
  }

  .contact-label {
    font-size: 1rem;
    line-height: 1.2;
  }

  .contact-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .contact-value {
    font-size: 0.9rem;
    line-height: 1.3;
  }

  .contact-actions {
    align-self: flex-end;
  }

  .contact-link {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  /* 视频容器优化 */
  .video-section {
    padding: 1rem;
  }

  .video-container {
    border-radius: 0.75rem;
  }

  /* 公司标语优化 */
  .company-slogan {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
  }

  .company-slogan p {
    font-size: 0.9rem;
    line-height: 1.2;
    margin-bottom: 0.2rem;
  }
}

/* 小屏幕移动端进一步优化 */
@media (max-width: 480px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .hero-section,
  .video-section,
  .intro-section,
  .competitiveness-section,
  .products-section,
  .reasons-section,
  .contact-section {
    margin-bottom: 0.4rem;
    padding: 0.6rem;
  }

  .hero-content {
    padding: 1rem 0.6rem;
  }

  .hero-title {
    font-size: 1.1rem;
  }

  .section-title-modern {
    font-size: 1rem;
  }

  .highlight-item {
    min-width: 50px;
  }

  .highlight-number {
    font-size: 1rem;
  }

  .advantage-card,
  .reason-card {
    padding: 0.6rem;
  }

  .product-card {
    padding: 0.6rem;
    min-height: 75px;
  }

  .product-image {
    position: relative;
    width: 55px;
    height: 55px;
    margin-right: 0.6rem;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1e3470, #2d4a8a);
    border-radius: 0.6rem;
    overflow: visible;
  }

  .product-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
    border-radius: 0.75rem;
  }

  .product-icon {
    font-size: 1.75rem;
    position: relative;
    z-index: 2;
  }

  .product-overlay {
    position: absolute;
    top: -3px;
    right: -3px;
    z-index: 3;
  }

  .product-tag {
    background: rgba(255, 255, 255, 0.9);
    color: #1e3470;
    font-size: 0.6rem;
    padding: 0.15rem 0.4rem;
    border-radius: 0.75rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(30, 52, 112, 0.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    font-weight: 500;
  }

  .product-content h4 {
    font-size: 1rem;
    margin-bottom: 0.3rem;
  }

  .product-content p {
    font-size: 0.85rem;
    line-height: 1.3;
  }

  /* 五大理由在小屏幕上的进一步优化 */
  .reasons-grid {
    gap: 0.75rem;
  }

  .reason-card {
    padding: 1rem;
    gap: 0.75rem;
  }

  .reason-number {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .reason-icon {
    display: none;
  }

  .reason-content h4 {
    font-size: 1rem;
    margin-bottom: 0.3rem;
    line-height: 1.2;
  }

  .reason-content p {
    font-size: 0.85rem;
    margin-bottom: 0;
    line-height: 1.3;
  }

  .reason-highlight {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  .stat-item {
    padding: 0.5rem;
  }

  .stat-icon {
    font-size: 1.2rem;
  }

  .advantage-icon-wrapper {
    width: 30px;
    height: 30px;
  }

  .advantage-icon {
    font-size: 1rem;
  }

  /* 移动端首创技术标签居中 */
  .achievement-badge {
    display: block;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }

  .contact-item {
    padding: 0.75rem;
  }

  .contact-icon {
    font-size: 1rem;
    margin-right: 0.4rem;
  }

  .contact-label {
    font-size: 0.9rem;
  }

  .contact-value {
    font-size: 0.8rem;
  }

  .contact-link {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
  }

  .contact-actions .el-button {
    padding: 0.3rem 0.5rem;
  }
}

@media (min-width: 768px) {
  .hero-content {
    padding: 4rem 3rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.4rem;
  }

  .intro-content {
    grid-template-columns: 1.5fr 1fr;
    gap: 2rem;
    align-items: start;
  }

  .intro-main {
    order: 1;
  }

  .intro-stats {
    order: 2;
    flex-direction: column;
  }

  /* 桌面端intro-section padding优化 */
  .intro-section {
    padding: 1.5rem 2rem;
  }

  /* 桌面端intro-text优化 */
  .intro-text {
    font-size: 1rem;
    line-height: 1.6;
  }

  /* 桌面端intro-highlight优化 */
  .intro-highlight {
    margin-bottom: 1rem;
  }

  /* 桌面端stat-item优化 */
  .stat-item {
    padding: 1.25rem;
  }

  .stat-icon {
    font-size: 1.75rem;
  }

  .advantage-cards {
    grid-template-columns: 1fr;
  }

  .product-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 992px) {
  .advantage-cards {
    grid-template-columns: repeat(3, 1fr);
  }

  .product-cards {
    grid-template-columns: repeat(3, 1fr);
  }

  .contact-info {
    grid-template-columns: repeat(2, 1fr);
  }

  .reasons-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
/* 荣誉资质轮播图部分 */
.honors-section {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(30, 52, 112, 0.08);
  animation: slideInUp 0.6s ease-out;
  transition: transform 0.3s, box-shadow 0.3s;
}

.honors-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(30, 52, 112, 0.12);
}

.honors-carousel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 主图显示区域 */
.carousel-main {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.carousel-image-container {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(30, 52, 112, 0.25);
}

.carousel-main-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8fafc;
  transition: transform 0.3s ease;
}

.carousel-image-container:hover .carousel-main-image {
  transform: scale(1.02);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 1rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-image-container:hover .image-overlay {
  transform: translateY(0);
}

.click-hint {
  font-size: 0.9rem;
  opacity: 0.9;
  text-align: center;
}

/* 图片标题显示区域 */
.image-title-display {
  text-align: center;
  margin-top: 1rem;
  padding: 0 1rem;
}

.image-title-display h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
  margin: 0;
  line-height: 1.4;
  min-height: 2.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 导航按钮 */
.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.carousel-nav:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.carousel-nav:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: translateY(-50%);
}

.carousel-nav.prev {
  left: -25px;
}

.carousel-nav.next {
  right: -25px;
}

.carousel-nav .el-icon {
  font-size: 1.2rem;
  color: #1e3470;
}

/* 指示器 */
.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #cbd5e1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator:hover {
  background: #94a3b8;
  transform: scale(1.2);
}

.indicator.active {
  background: #1e3470;
  transform: scale(1.3);
}

/* 工程案例样式 */
.project-cases-section {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(30, 52, 112, 0.1);
}

/* 手风琴样式 */
.case-accordion {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.case-accordion :deep(.el-collapse-item) {
  border-bottom: 1px solid #f0f2f5;
}

.case-accordion :deep(.el-collapse-item:last-child) {
  border-bottom: none;
}

.case-accordion :deep(.el-collapse-item__header) {
  height: 60px;
  line-height: 60px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: none;
  font-size: 18px;
  font-weight: 600;
  color: #1e3470;
  padding: 0 24px;
  transition: all 0.3s ease;
}

.case-accordion :deep(.el-collapse-item__header:hover) {
  background: linear-gradient(135deg, #1e3470 0%, #3b82f6 100%);
  color: #ffffff;
}

.case-accordion :deep(.el-collapse-item__header.is-active) {
  background: linear-gradient(135deg, #1e3470 0%, #3b82f6 100%);
  color: #ffffff;
}

.case-accordion :deep(.el-collapse-item__arrow) {
  color: inherit;
  font-size: 16px;
  transition: all 0.3s ease;
}

.case-accordion :deep(.el-collapse-item__content) {
  padding: 24px;
  background: #ffffff;
  border: none;
}

.case-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 0;
}

.case-image-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.case-image-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.case-image-item img {
  width: 100%;
  height: 180px;
  object-fit: contain; /* 确保图片完全展示 */
  background-color: #f8fafc;
  transition: transform 0.3s ease;
}

.case-image-item:hover img {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .project-cases-section {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 1rem;
  }

  .case-accordion :deep(.el-collapse-item__header) {
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    padding: 0 16px;
  }

  .case-accordion :deep(.el-collapse-item__content) {
    padding: 16px;
  }

  .case-images-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .case-image-item img {
    height: 120px;
  }

  .honors-section {
    margin-bottom: 0.75rem;
    padding: 1rem;
    border-radius: 1rem;
  }

  .carousel-image-container {
    height: 300px;
  }

  .carousel-nav {
    width: 40px;
    height: 40px;
  }

  .carousel-nav.prev {
    left: -20px;
  }

  .carousel-nav.next {
    right: -20px;
  }

  .carousel-nav .el-icon {
    font-size: 1rem;
  }

  .click-hint {
    font-size: 0.8rem;
  }

  .image-title-display h4 {
    font-size: 1rem;
    min-height: 2.4rem;
  }
}

@media (max-width: 480px) {
  .project-cases-section {
    margin-bottom: 0.75rem;
    padding: 0.75rem;
  }

  .case-category {
    margin-bottom: 1rem;
  }

  .category-title {
    font-size: 0.9rem;
  }

  .case-images-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .case-image-item img {
    height: 200px;
  }

  .honors-section {
    margin-bottom: 0.5rem;
    padding: 0.75rem;
  }

  .carousel-image-container {
    height: 250px;
  }

  .carousel-nav {
    width: 35px;
    height: 35px;
  }

  .carousel-nav.prev {
    left: -15px;
  }

  .carousel-nav.next {
    right: -15px;
  }

  .carousel-nav .el-icon {
    font-size: 0.9rem;
  }

  .click-hint {
    font-size: 0.75rem;
  }

  .image-overlay {
    padding: 0.75rem;
  }

  .image-title-display h4 {
    font-size: 0.9rem;
    min-height: 2rem;
  }
}
</style>
