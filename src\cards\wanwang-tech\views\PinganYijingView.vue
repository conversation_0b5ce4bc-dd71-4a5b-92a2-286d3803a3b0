<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

// 轮播图数据
const carouselImages = [
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing7.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing1.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing2.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing3.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing4.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing5.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing6.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing8.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/pinganyijing9.jpg'
]

const currentImageIndex = ref(0)

const nextImage = () => {
  currentImageIndex.value = (currentImageIndex.value + 1) % carouselImages.length
}

const prevImage = () => {
  currentImageIndex.value = currentImageIndex.value === 0 ? carouselImages.length - 1 : currentImageIndex.value - 1
}

const goToImage = (index: number) => {
  currentImageIndex.value = index
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('PinganYijingView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>平安义警大数据管理平台</h1>
    </div>

    <div class="content">
      <!-- 项目介绍 -->
      <div class="section intro-section">
        <div class="intro-card">
          <div class="intro-content">
            <div class="intro-title">
              <h3>平安义警大数据管理平台</h3>
              <div class="title-decoration"></div>
            </div>
            <div class="intro-description">
              <div class="description-item">
                <div class="item-icon">🛡️</div>
                <p>平安义警大数据管理平台是充分利用大数据、云计算、移动互联网等新一代信息技术助力构建共建共治共享格局、打造"义警+大数据"互联互通互融工作体系，打通社会化治理和人民服务"最后一公里"。为义警队伍与管理者提供了一个安全、高效、便利、智慧化的管理平台。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">📱</div>
                <p>平安义警大数据管理平台是基于"移动互联网+"技术，通过分析义警协会工作现状，实现了"统一招募、统一标准、统一培训、统一管理、统一考核"等管理模式，同时还满足"爱心激励、爱心传递"等机制，量身定制的信息化解决方案。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">🌐</div>
                <p>具有多端口、跨屏幕、移动化等特性，可以支持义警协会总队，下辖各级中队随时随地开展义警自愿服务工作。产品将传统义警管理模式升级为"开放式、民主化"组织结构模式，以"互联网+队伍管理"模式来破解义警管理难题、推进义警创新发展、为地方特色的市域社会治理提供了实用可行的解决方案。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统截图展示 -->
      <div class="section gallery-section">
        <div class="section-header">
          <h2>系统截图展示</h2>
        </div>
        <div class="carousel-container">
          <div class="carousel-wrapper">
            <div class="carousel-main">
              <img 
                :src="carouselImages[currentImageIndex]" 
                :alt="`平安义警大数据管理平台截图 ${currentImageIndex + 1}`"
                class="carousel-image"
              />
              <button @click="prevImage" class="carousel-btn prev-btn">‹</button>
              <button @click="nextImage" class="carousel-btn next-btn">›</button>
            </div>
            <div class="carousel-thumbnails">
              <div 
                v-for="(image, index) in carouselImages" 
                :key="index"
                @click="goToImage(index)"
                :class="['thumbnail', { active: index === currentImageIndex }]"
              >
                <img :src="image" :alt="`缩略图 ${index + 1}`" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>核心功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <h3>统一招募</h3>
            <div class="feature-content">
              <p>建立标准化的义警招募流程，统一招募标准和程序，确保义警队伍质量。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📏</div>
            <h3>统一标准</h3>
            <div class="feature-content">
              <p>制定统一的义警工作标准和规范，确保服务质量的一致性和专业性。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎓</div>
            <h3>统一培训</h3>
            <div class="feature-content">
              <p>提供标准化的培训体系，提升义警队伍的专业素养和服务能力。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏢</div>
            <h3>统一管理</h3>
            <div class="feature-content">
              <p>建立完善的管理体系，实现对义警队伍的统一调度和协调管理。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <h3>统一考核</h3>
            <div class="feature-content">
              <p>建立科学的考核评价体系，激励义警队伍积极参与社会治理工作。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">❤️</div>
            <h3>爱心激励</h3>
            <div class="feature-content">
              <p>建立爱心激励机制，通过多种方式激发义警队伍的服务热情和奉献精神。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>平台优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">📱</div>
            <h3>多端口支持</h3>
            <p>支持PC端、移动端、平板等多种终端设备，随时随地开展工作。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🖥️</div>
            <h3>跨屏幕适配</h3>
            <p>完美适配各种屏幕尺寸，提供一致的用户体验和操作界面。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🚀</div>
            <h3>移动化办公</h3>
            <p>支持移动化办公模式，提高工作效率和响应速度。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔗</div>
            <h3>互联互通</h3>
            <p>实现各级组织间的信息互联互通，打破信息孤岛。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <h3>精准管理</h3>
            <p>基于大数据分析，实现精准化、智能化的队伍管理。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🌐</div>
            <h3>开放民主</h3>
            <p>构建开放式、民主化的组织结构，提升管理透明度。</p>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="value-content">
          <p>平安义警大数据管理平台通过信息化手段，有效解决了传统义警管理中的难题，实现了义警队伍管理的数字化转型。平台构建了"义警+大数据"的创新工作体系，打通了社会化治理和人民服务的"最后一公里"，为构建共建共治共享的社会治理格局提供了强有力的技术支撑。通过统一的管理模式和爱心激励机制，不仅提升了义警队伍的专业化水平，更激发了广大义警的服务热情，为地方特色的市域社会治理探索出了一条实用可行的创新路径，为平安中国建设贡献了重要力量。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 项目介绍样式 */
.intro-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(40, 167, 69, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.intro-content {
  text-align: center;
}

.intro-title {
  margin-bottom: 2rem;
}

.intro-title h3 {
  color: #28a745;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.title-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-description {
  display: grid;
  gap: 1.5rem;
  text-align: left;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #28a745;
}

.item-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.description-item p {
  margin: 0;
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
}

/* 轮播图样式 */
.carousel-container {
  max-width: 100%;
  margin: 0 auto;
}

.carousel-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.carousel-main {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.prev-btn {
  left: 10px;
}

.next-btn {
  right: 10px;
}

.carousel-thumbnails {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
}

.thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 0.5rem;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.thumbnail:hover {
  border-color: #1693d2;
  transform: scale(1.05);
}

.thumbnail.active {
  border-color: #1693d2;
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 核心功能样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.feature-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 平台优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* 应用价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .intro-description {
    gap: 1rem;
  }

  .description-item {
    padding: 1rem;
  }

  .carousel-main {
    height: 250px;
  }

  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .thumbnail {
    width: 60px;
    height: 45px;
  }

  .carousel-thumbnails {
    gap: 0.3rem;
    max-height: 150px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }

  .intro-title h3 {
    font-size: 1.5rem;
  }
}

/* 滚动条样式 */
.carousel-thumbnails::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.carousel-thumbnails::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.carousel-thumbnails::-webkit-scrollbar-thumb {
  background: #1693d2;
  border-radius: 3px;
}

.carousel-thumbnails::-webkit-scrollbar-thumb:hover {
  background: #0d7ab8;
}
</style>
