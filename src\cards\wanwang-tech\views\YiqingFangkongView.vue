<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('YiqingFangkongView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>疫情防控实战平台</h1>
    </div>

    <div class="content">
      <!-- 项目介绍 -->
      <div class="section intro-section">
        
        <div class="intro-card">
          <div class="intro-visual">
            <div class="intro-image">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/YiQingFangKong.png" alt="疫情防控实战平台" />
            </div>
            
          </div>
          <div class="intro-content">
            <div class="intro-title">
              <h3>新冠肺炎疫情防控工作信息平台</h3>
              <div class="title-decoration"></div>
            </div>
            <div class="intro-description">
              <div class="description-item">
                <div class="item-icon">🔗</div>
                <p>新冠肺炎疫情防控工作信息平台是基于"移动互联网+大数据"技术，能够把"省漫游数据、落地检数据、报备数据等各渠道数据"进行导入整合，通过疫情防控工作信息平台快速层层分发，做到从卫建委到街镇、到社区、到网格员、到数据专班、到社区民警、到转运组、到疾控、转运司机、到隔离组等全部部门业务联动。</p>
              </div>
              <div class="description-item">
                <div class="item-icon">⚡</div>
                <p>工作流程全闭环、工作流程可追溯、工作流程扁平化，效率更高，责任更实。同时，这些信息可以实时共享，从发现到核查到管控到保障到定岗到定责有机整合起来，通过大数据一张网进行可视化展示。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>平台优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">🚀</div>
            <h3>全面升级效率</h3>
            <p>利用信息技术全面升级疫情应急处置效率和智能化分析水平，切实解决了一线疾控信息传递和信息共享的痛点问题。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <h3>提升流调效率</h3>
            <p>提升流调工作效率，助力疫情快速控制，助力流调、轨迹排查、隔离转运、疫点消杀等疫情防控工作有序协同开展。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <h3>快速决策反应</h3>
            <p>有效节约了科学决策时间、提升了快速反应能力，实现疫情防控工作的科学化、精准化管理。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔗</div>
            <h3>全流程闭环</h3>
            <p>工作流程全闭环、工作流程可追溯、工作流程扁平化，从发现到核查到管控到保障实现有机整合。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📊</div>
            <h3>大数据可视化</h3>
            <p>通过大数据一张网进行可视化展示，实现信息实时共享和智能化分析。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🤝</div>
            <h3>全部门联动</h3>
            <p>实现从卫建委到街镇、社区、网格员等全部部门业务联动，责任更实，效率更高。</p>
          </div>
        </div>
      </div>

      <!-- 总体技术设计 -->
      <div class="section tech-design-section">
        <div class="section-header">
          <h2>总体技术设计：业务框架</h2>
        </div>
        <div class="tech-design-content">
          <div class="tech-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yiqingfangkong121.png" alt="业务框架图" />
          </div>
          <div class="tech-description">
            <p>平台采用先进的技术架构，基于"移动互联网+大数据"技术构建，实现多源数据整合、智能分析处理和可视化展示的完整业务框架。</p>
          </div>
        </div>
      </div>

      <!-- 系统应用端 -->
      <div class="section app-section">
        <div class="section-header">
          <h2>系统应用端</h2>
        </div>
        <div class="app-grid">
          <div class="app-item">
            <div class="app-header">
              <div class="app-icon">📱</div>
              <h3>移动应用界面</h3>
            </div>
            <div class="app-content">
              <div class="app-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yiqingfangkong122.png" alt="系统应用端" />
              </div>
              <p>移动端应用界面，提供便捷的操作体验，支持各类疫情防控业务的移动化处理。</p>
            </div>
          </div>

          <div class="app-item">
            <div class="app-header">
              <div class="app-icon">📋</div>
              <h3>待办和上门管控功能</h3>
            </div>
            <div class="app-content">
              <div class="app-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yiqingfangkong123.png" alt="待办和上门管控功能界面" />
              </div>
              <p>待办事项管理和上门管控功能界面，实现任务分配、进度跟踪和现场管控的数字化管理。</p>
            </div>
          </div>

          <div class="app-item">
            <div class="app-header">
              <div class="app-icon">🧪</div>
              <h3>功能板块和核酸登记</h3>
            </div>
            <div class="app-content">
              <div class="app-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yiqingfangkong124.png" alt="功能板块和上门核酸登记板块" />
              </div>
              <p>功能板块和上门核酸登记板块，支持核酸检测信息的快速录入和管理。</p>
            </div>
          </div>

          <div class="app-item">
            <div class="app-header">
              <div class="app-icon">⚠️</div>
              <h3>风险人员报备和数据统计</h3>
            </div>
            <div class="app-content">
              <div class="app-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yiqingfangkong125.png" alt="风险人员报备和数据统计模块功能界面" />
              </div>
              <p>风险人员报备和数据统计模块功能界面，实现风险人员的精准识别和统计分析。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 大数据管控平台 -->
      <div class="section bigdata-section">
        <div class="section-header">
          <h2>大数据管控平台（指挥中心）</h2>
        </div>
        <div class="bigdata-card">
          <div class="bigdata-visual">
            <div class="bigdata-image">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/yiqingfangkong126.png" alt="大数据管控平台" />
            </div>
            <div class="bigdata-badge">
              <span class="badge-text">智能指挥</span>
            </div>
          </div>
          <div class="bigdata-content">
            <div class="bigdata-title">
              <h3>实时数据展示</h3>
              <div class="title-decoration"></div>
            </div>
            <div class="bigdata-intro">
              <p>通过大数据屏幕，实时展示以下数据：</p>
            </div>
            <div class="data-grid">
              <div class="data-item">
                <div class="data-icon">📊</div>
                <span>实时核查数据</span>
              </div>
              <div class="data-item">
                <div class="data-icon">⚠️</div>
                <span>风险人员分级分类管控</span>
              </div>
              <div class="data-item">
                <div class="data-icon">📍</div>
                <span>抵信州人员来源地区统计</span>
              </div>
              <div class="data-item">
                <div class="data-icon">🎯</div>
                <span>实时管控数据</span>
              </div>
              <div class="data-item">
                <div class="data-icon">👥</div>
                <span>在管人员数据来源</span>
              </div>
              <div class="data-item">
                <div class="data-icon">📋</div>
                <span>各街镇派单核查</span>
              </div>
              <div class="data-item">
                <div class="data-icon">📈</div>
                <span>分区域报表统计</span>
              </div>
              <div class="data-item">
                <div class="data-icon">🗺️</div>
                <span>实时地图</span>
              </div>
              <div class="data-item">
                <div class="data-icon">🏠</div>
                <span>集中隔离/居家隔离/监测</span>
              </div>
              <div class="data-item">
                <div class="data-icon">🏨</div>
                <span>酒店隔离点等全部数据展示</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>核心功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <h3>数据整合</h3>
            <div class="feature-content">
              <p>整合省漫游数据、落地检数据、报备数据等各渠道数据，实现多源数据的统一管理。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔄</div>
            <h3>快速分发</h3>
            <div class="feature-content">
              <p>通过平台快速层层分发信息，确保各级部门及时获取最新疫情防控信息。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🤝</div>
            <h3>业务联动</h3>
            <div class="feature-content">
              <p>实现全部门业务联动，从卫建委到基层网格员的全链条协同工作。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔍</div>
            <h3>流程追溯</h3>
            <div class="feature-content">
              <p>工作流程可追溯，确保每个环节都有据可查，责任明确。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📱</div>
            <h3>移动办公</h3>
            <div class="feature-content">
              <p>支持移动端操作，让一线工作人员随时随地处理疫情防控事务。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📈</div>
            <h3>智能分析</h3>
            <div class="feature-content">
              <p>基于大数据的智能分析，为疫情防控决策提供科学依据。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="value-content">
          <p>疫情防控实战平台通过信息技术的深度应用，实现了疫情防控工作的数字化转型。平台不仅提高了疫情应急处置的效率和智能化水平，更重要的是构建了一个全方位、多层次的疫情防控体系。通过数据整合、流程优化、智能分析等手段，平台有效解决了传统疫情防控工作中信息孤岛、响应滞后、协调困难等问题，为疫情防控工作提供了强有力的技术支撑，为保障人民群众生命健康安全发挥了重要作用。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 项目介绍样式 */
.intro-card {
  background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(22, 147, 210, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1693d2 0%, #0d7ab8 100%);
}

.intro-visual {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
}

.intro-image {
  width: 300px;
  height: 200px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(22, 147, 210, 0.2);
  position: relative;
}

.intro-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.intro-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #1693d2 0%, #0d7ab8 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
}

.intro-content {
  text-align: center;
}

.intro-title {
  margin-bottom: 2rem;
}

.intro-title h3 {
  color: #1693d2;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.title-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #1693d2 0%, #0d7ab8 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-description {
  display: grid;
  gap: 1.5rem;
  text-align: left;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #1693d2;
}

.item-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.description-item p {
  margin: 0;
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
}

/* 平台优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* 技术设计样式 */
.tech-design-content {
  text-align: center;
}

.tech-image {
  margin-bottom: 2rem;
}

.tech-image img {
  width: 100%;
  max-width: 800px;
  height: auto;
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tech-description p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 系统应用端样式 */
.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.app-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.app-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.app-header {
  background: linear-gradient(135deg, #1693d2 0%, #0d7ab8 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
}

.app-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.app-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.app-content {
  padding: 2rem;
}

.app-image {
  margin-bottom: 1rem;
}

.app-image img {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  display: block;
}

.app-content p {
  color: #333;
  line-height: 1.6;
  margin: 0;
  text-align: justify;
}

/* 大数据平台样式 */
.bigdata-card {
  background: linear-gradient(135deg, #fff8f0 0%, #fef3e8 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(255, 165, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.bigdata-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff9500 0%, #ff7b00 100%);
}

.bigdata-visual {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
}

.bigdata-image {
  width: 100%;
  max-width: 600px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(255, 149, 0, 0.2);
  position: relative;
}

.bigdata-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.bigdata-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
}

.bigdata-content {
  text-align: center;
}

.bigdata-title {
  margin-bottom: 1.5rem;
}

.bigdata-title h3 {
  color: #ff9500;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.bigdata-intro {
  margin-bottom: 2rem;
}

.bigdata-intro p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 1.5rem;
  background: white;
  border-radius: 0.8rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #ff9500;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.data-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.data-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.data-item span {
  color: #333;
  font-size: 0.95rem;
  font-weight: 500;
}

/* 核心功能样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.feature-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 应用价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .intro-image {
    width: 250px;
    height: 150px;
  }

  .intro-description {
    gap: 1rem;
  }

  .description-item {
    padding: 1rem;
  }

  .bigdata-card {
    padding: 1.5rem;
  }

  .data-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .data-item {
    padding: 0.8rem 1rem;
  }

  .app-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .app-header {
    padding: 1rem 1.5rem;
  }

  .app-content {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }
}
</style>
