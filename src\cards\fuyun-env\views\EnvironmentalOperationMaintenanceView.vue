<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}



// 运维服务内容
const serviceContents = reactive([
  {
    title: '设备巡检',
    description: '定期对环保设施进行全面巡检，检查设备运行状态、参数指标、磨损情况等，及时发现潜在问题。',
    icon: '🔍'
  },
  {
    title: '数据监测与分析',
    description: '实时监测设备运行数据，进行专业分析，监控设备性能和排放指标，为优化调整提供数据支撑。',
    icon: '📊'
  },
  {
    title: '故障处理与修复',
    description: '快速响应设备故障，进行专业诊断和修复，最大程度减少停机时间，确保设施连续稳定运行。',
    icon: '🔧'
  },
  {
    title: '优化运行方案',
    description: '根据实际运行情况和数据分析结果，持续优化运行参数和操作方案，提高运行效率和环保效果。',
    icon: '⚙️'
  }
])

// 服务优势
const serviceAdvantages = reactive([
  {
    title: '专业团队',
    description: '拥有经验丰富的环保运维工程师，提供专业的技术支持和服务。',
    icon: '👥'
  },
  {
    title: '24小时响应',
    description: '提供24小时应急响应服务，确保设备故障得到及时处理。',
    icon: '🕐'
  },
  {
    title: '预防性维护',
    description: '采用预防性维护策略，降低设备故障率，延长设备使用寿命。',
    icon: '🛡️'
  },
  {
    title: '成本控制',
    description: '通过专业运维管理，有效控制运营成本，提高经济效益。',
    icon: '💰'
  }
])

// 维护计划
const maintenancePlans = reactive([
  {
    type: '日常维护',
    frequency: '每日',
    items: ['设备运行状态检查', '参数记录与分析', '清洁保养', '异常情况处理'],
    color: '#10b981'
  },
  {
    type: '定期保养',
    frequency: '每周/每月',
    items: ['滤料更换检查', '风机润滑保养', '电气系统检测', '管道清理'],
    color: '#3b82f6'
  },
  {
    type: '大修维护',
    frequency: '每季度/每年',
    items: ['设备全面检修', '关键部件更换', '系统性能测试', '技术升级改造'],
    color: '#f59e0b'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>环保运营运维</h1>
    </div>

    <div class="content">
      <!-- 服务主图和基本信息 -->
      <div class="service-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/huanbaoyunwei.jpeg" alt="环保运营维护" />
        </div>
        <div class="hero-content">
          <h2>环保运营运维</h2>
          <p class="service-subtitle">专业的环保设备运营维护服务，确保设备长期稳定运行</p>
          <div class="service-intro">
            <p>我们提供全方位的环保设备运营维护服务，从日常巡检、数据监测到故障处理、优化改进，确保环保设施始终保持最佳运行状态。</p>
            <p>拥有专业的运维团队和完善的服务体系，为客户提供可靠、高效的环保设备运营保障。</p>
          </div>
        </div>
      </div>

      <!-- 服务内容 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🛠️</span>
          服务内容
        </h3>
        <div class="services-grid">
          <div
            v-for="(service, index) in serviceContents"
            :key="index"
            class="service-card"
          >
            <div class="service-icon">{{ service.icon }}</div>
            <h4>{{ service.title }}</h4>
            <p>{{ service.description }}</p>
          </div>
        </div>
      </div>

      <!-- 维护计划 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📅</span>
          维护计划
        </h3>
        <div class="plans-grid">
          <div
            v-for="(plan, index) in maintenancePlans"
            :key="index"
            class="plan-card"
            :style="{ borderColor: plan.color }"
          >
            <div class="plan-header" :style="{ backgroundColor: plan.color }">
              <h4>{{ plan.type }}</h4>
              <span class="frequency">{{ plan.frequency }}</span>
            </div>
            <div class="plan-content">
              <ul>
                <li v-for="(item, itemIndex) in plan.items" :key="itemIndex">
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          服务优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in serviceAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>


    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 服务展示区域 */
.service-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.service-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.service-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.service-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 服务内容网格 */
.services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.service-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.service-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.service-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.service-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
}

.service-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.9rem;
}

/* 维护计划 */
.plans-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.plan-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  border: 3px solid;
  transition: transform 0.2s ease;
}

.plan-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.plan-header {
  padding: 1rem 1.5rem;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.frequency {
  font-size: 0.9rem;
  opacity: 0.9;
}

.plan-content {
  padding: 1.5rem;
}

.plan-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.plan-content li {
  padding: 0.5rem 0;
  color: #4b5563;
  font-size: 0.9rem;
  position: relative;
  padding-left: 1.5rem;
}

.plan-content li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: bold;
}

/* 服务优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 联系我们部分 */
.contact-section {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #3b82f6;
}

.contact-content {
  text-align: center;
}

.contact-text {
  margin: 0 0 1.5rem 0;
  color: #4b5563;
  font-size: 1rem;
  line-height: 1.5;
}

.contact-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.contact-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.call-btn {
  background: #3b82f6;
  color: white;
}

.call-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.copy-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.copy-btn:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.phone-number {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .service-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .service-subtitle {
    font-size: 1.1rem;
  }

  .service-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .plans-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .contact-buttons {
    gap: 1.5rem;
  }

  .contact-btn {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
