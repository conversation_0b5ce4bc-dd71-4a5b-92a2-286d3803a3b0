<template>
  <div class="page">
    <!-- 顶部导航 -->
    <div class="header">
      <button @click="goBack" class="back-btn">‹ 返回</button>
      <h1>协会成员</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 欢迎页 -->
      <div class="section">
        <div class="welcome-card">
          <p>协会的成就与发展，源于每一位成员的专业智慧与不懈努力。他们是各自领域的精英，也是推动我们共同事业的核心力量。</p>
        </div>
      </div>

      <!-- 协会成员名单 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><UserFilled /></el-icon>
            协会成员名单
          </h2>

          <div class="members-list">
            <div
              v-for="(member, index) in memberImages"
              :key="index"
              class="member-item"
            >
              <div class="member-title">
                <h3>成员名单 {{ index + 1 }}</h3>
              </div>
              <div class="member-image-wrapper" @click="viewImage(member.url, index)">
                <img
                  :src="member.url"
                  :alt="`协会成员名单 ${index + 1}`"
                  class="member-full-image"
                  loading="lazy"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏图片查看 -->
    <div v-if="showImageViewer" class="fullscreen-viewer" @click="() => closeImageViewer()">
      <div class="viewer-header">
        <span class="image-title">成员名单 {{ currentImageIndex + 1 }}</span>
        <button class="close-btn desktop-only" @click="() => closeImageViewer()">✕</button>
      </div>
      <div class="fullscreen-image-container" @click.stop>
        <img
          ref="zoomableImage"
          :src="currentImageUrl"
          :alt="`协会成员名单 ${currentImageIndex + 1}`"
          class="fullscreen-image"
          :style="imageTransform"
          @click.stop
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @wheel="handleWheel"
        />
      </div>
      <div class="viewer-navigation" @click.stop>
        <button class="nav-btn" @click="prevImage" :disabled="currentImageIndex === 0">‹</button>
        <span class="image-counter">{{ currentImageIndex + 1 }} / {{ memberImages.length }}</span>
        <button class="nav-btn" @click="nextImage" :disabled="currentImageIndex === memberImages.length - 1">›</button>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar.vue'
import { UserFilled } from '@element-plus/icons-vue'

const router = useRouter()

// 成员图片数据
const memberImages = ref([
  { url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xiehuichengyuan1.png' },
  { url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xiehuichengyuan2.png' },
  { url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xiehuichengyuan3.png' },
  { url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xiehuichengyuan4.png' },
  { url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xiehuichengyuan5.png' },
  { url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xiehuichengyuan6.png' },
  { url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/xiehuichengyuan7.png' }
])

// 图片查看器相关
const showImageViewer = ref(false)
const currentImageUrl = ref('')
const currentImageIndex = ref(0)
const zoomableImage = ref<HTMLImageElement | null>(null)

// 缩放和拖拽相关
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)
const isDragging = ref(false)
const lastTouchDistance = ref(0)
const dragStart = ref({ x: 0, y: 0 })

// 滑动切换相关
const swipeStart = ref({ x: 0, y: 0, time: 0 })
const isSwipeGesture = ref(false)
const swipeThreshold = 50 // 滑动距离阈值
const swipeTimeThreshold = 300 // 滑动时间阈值（毫秒）

// 计算图片变换样式
const imageTransform = computed(() => {
  return {
    transform: `translate(${translateX.value}px, ${translateY.value}px) scale(${scale.value})`,
    transformOrigin: 'center center',
    transition: isDragging.value ? 'none' : 'transform 0.3s ease'
  }
})

const goBack = () => {
  router.push('/card/gesi-union')
}

const viewImage = (url: string, index: number) => {
  currentImageUrl.value = url
  currentImageIndex.value = index
  showImageViewer.value = true
  resetImageTransform()

  // 添加历史记录条目，用于处理返回键
  if (typeof window !== 'undefined') {
    window.history.pushState({ imageViewer: true }, '', window.location.href)
  }
}

const closeImageViewer = (fromBackButton = false) => {
  showImageViewer.value = false
  currentImageUrl.value = ''
  currentImageIndex.value = 0
  resetImageTransform()

  // 如果不是通过返回键关闭的，需要回退历史记录
  if (!fromBackButton && typeof window !== 'undefined' && window.history.state?.imageViewer) {
    window.history.back()
  }
}

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
    currentImageUrl.value = memberImages.value[currentImageIndex.value].url
    resetImageTransform()
  }
}

const nextImage = () => {
  if (currentImageIndex.value < memberImages.value.length - 1) {
    currentImageIndex.value++
    currentImageUrl.value = memberImages.value[currentImageIndex.value].url
    resetImageTransform()
  }
}

// 重置图片变换
const resetImageTransform = () => {
  scale.value = 1
  translateX.value = 0
  translateY.value = 0
  isDragging.value = false
}

// 获取两点间距离
const getDistance = (touch1: Touch, touch2: Touch) => {
  const dx = touch1.clientX - touch2.clientX
  const dy = touch1.clientY - touch2.clientY
  return Math.sqrt(dx * dx + dy * dy)
}

// 触摸开始
const handleTouchStart = (e: TouchEvent) => {
  e.preventDefault()

  if (e.touches.length === 1) {
    const touch = e.touches[0]

    // 记录滑动开始信息
    swipeStart.value = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    }
    isSwipeGesture.value = false

    // 如果图片已放大，准备拖拽
    if (scale.value > 1) {
      isDragging.value = true
      dragStart.value = {
        x: touch.clientX - translateX.value,
        y: touch.clientY - translateY.value
      }
    }
  } else if (e.touches.length === 2) {
    // 双指缩放 - 重置所有单指相关状态
    isDragging.value = false
    isSwipeGesture.value = false
    lastTouchDistance.value = getDistance(e.touches[0], e.touches[1])
  }
}

// 触摸移动
const handleTouchMove = (e: TouchEvent) => {
  e.preventDefault()

  if (e.touches.length === 2) {
    // 双指缩放 - 优先处理
    const currentDistance = getDistance(e.touches[0], e.touches[1])
    if (lastTouchDistance.value > 0) {
      const scaleChange = currentDistance / lastTouchDistance.value

      let newScale = scale.value * scaleChange
      newScale = Math.max(0.8, Math.min(5, newScale)) // 允许缩小到0.8倍

      scale.value = newScale
      lastTouchDistance.value = currentDistance

      // 如果缩放到1以下，重置位置
      if (newScale < 1) {
        translateX.value = 0
        translateY.value = 0
      }
    }
  } else if (e.touches.length === 1) {
    const touch = e.touches[0]
    const deltaX = touch.clientX - swipeStart.value.x
    const deltaY = touch.clientY - swipeStart.value.y

    // 如果是拖拽模式（图片已放大）
    if (isDragging.value && scale.value > 1) {
      translateX.value = touch.clientX - dragStart.value.x
      translateY.value = touch.clientY - dragStart.value.y
    }
    // 判断是否为滑动手势（水平移动大于垂直移动，且图片未放大）
    else if (scale.value <= 1 && Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
      isSwipeGesture.value = true
    }
  }
}

// 触摸结束
const handleTouchEnd = (e: TouchEvent) => {
  e.preventDefault()

  // 只有在单指操作且没有剩余触摸点时才处理滑动
  if (e.touches.length === 0) {
    // 处理滑动切换
    if (isSwipeGesture.value && scale.value <= 1) {
      const touch = e.changedTouches[0]
      const deltaX = touch.clientX - swipeStart.value.x
      const deltaY = touch.clientY - swipeStart.value.y
      const deltaTime = Date.now() - swipeStart.value.time

      // 检查是否满足滑动条件
      if (Math.abs(deltaX) > swipeThreshold &&
          Math.abs(deltaX) > Math.abs(deltaY) &&
          deltaTime < swipeTimeThreshold) {

        if (deltaX > 0) {
          // 右滑 - 上一张
          if (currentImageIndex.value > 0) {
            prevImage()
          }
        } else {
          // 左滑 - 下一张
          if (currentImageIndex.value < memberImages.value.length - 1) {
            nextImage()
          }
        }
      }
    }

    // 重置状态
    isDragging.value = false
    isSwipeGesture.value = false

    // 如果缩放小于1，平滑重置到1
    if (scale.value < 1) {
      scale.value = 1
      translateX.value = 0
      translateY.value = 0
    }
  }
}

// 鼠标按下
const handleMouseDown = (e: MouseEvent) => {
  if (scale.value > 1) {
    isDragging.value = true
    dragStart.value = {
      x: e.clientX - translateX.value,
      y: e.clientY - translateY.value
    }
  }
}

// 鼠标移动
const handleMouseMove = (e: MouseEvent) => {
  if (isDragging.value && scale.value > 1) {
    translateX.value = e.clientX - dragStart.value.x
    translateY.value = e.clientY - dragStart.value.y
  }
}

// 鼠标松开
const handleMouseUp = () => {
  isDragging.value = false
}

// 鼠标滚轮缩放
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()

  const scaleChange = e.deltaY > 0 ? 0.9 : 1.1
  let newScale = scale.value * scaleChange
  newScale = Math.max(0.8, Math.min(5, newScale))

  scale.value = newScale

  // 如果缩放到1以下，重置位置
  if (newScale < 1) {
    scale.value = 1
    translateX.value = 0
    translateY.value = 0
  }
}

// 处理浏览器返回键
const handlePopState = () => {
  if (showImageViewer.value) {
    // 如果图片查看器是打开的，通过返回键关闭它
    closeImageViewer(true)
  }
}

// 组件挂载时添加事件监听
onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('popstate', handlePopState)
  }
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('popstate', handlePopState)
  }
})
</script>

<style scoped>
.page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 100px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.2s;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  flex: 1;
  text-align: center;
  padding-right: 3rem; /* 平衡左侧按钮 */
}

.content {
  padding-top: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  margin-bottom: 1rem;
}

.card {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.welcome-card {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  text-align: center;
  border-radius: 0.75rem;
  padding: 1.5rem 1rem;
  box-shadow: 0 8px 30px rgba(196, 27, 33, 0.3);
}

.welcome-card h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  line-height: 1.3;
}

.welcome-card p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 400;
  line-height: 1.5;
}

.card h2 {
  color: #c41b21;
  margin: 0 0 0.75rem 0;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* 成员列表布局 */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

.member-item {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.member-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.member-title {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 0.75rem 1rem;
  text-align: center;
}

.member-title h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.member-image-wrapper {
  padding: 1rem;
  background: white;
  cursor: pointer;
}

.member-full-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 最后一个成员项额外底部间距 */
.member-item:last-child {
  margin-bottom: 2rem;
}

/* 全屏图片查看器样式 */
.fullscreen-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.viewer-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1001;
}

.image-title {
  font-size: 1.1rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 桌面端显示关闭按钮 */
.desktop-only {
  display: block;
}

.fullscreen-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  touch-action: none;
}

.fullscreen-image {
  max-width: 100vw;
  max-height: 100vh;
  width: auto;
  height: auto;
  object-fit: contain;
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
}

.fullscreen-image:active {
  cursor: grabbing;
}

.viewer-navigation {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1001;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 50px;
}

.nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.nav-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  border-color: rgba(255, 255, 255, 0.2);
}

.image-counter {
  color: white;
  font-size: 1rem;
  font-weight: 500;
}

/* 图片查看器样式 */
.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.image-viewer-content {
  background: white;
  border-radius: 1rem;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.image-viewer-header {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.image-viewer-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: auto;
}

.viewer-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
}

.image-navigation {
  background: #f8fafc;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #e2e8f0;
}

.nav-btn {
  background: #c41b21;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s;
}

.nav-btn:hover:not(:disabled) {
  background: #a01419;
  transform: translateY(-1px);
}

.nav-btn:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
}

.image-counter {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}




/* 移动端特殊样式 */
@media (max-width: 480px) {
  .content {
    padding-top: 4.5rem;
  }

  .page {
    padding-bottom: 120px;
  }

  .members-list {
    gap: 1rem;
  }

  .member-title {
    padding: 0.6rem 0.8rem;
  }

  .member-title h3 {
    font-size: 0.9rem;
  }

  .member-image-wrapper {
    padding: 0.8rem;
  }

  .member-item:last-child {
    margin-bottom: 3rem;
  }

  /* 移动端全屏查看器 */
  .viewer-header {
    padding: 0.75rem 1rem;
  }

  .image-title {
    font-size: 1rem;
  }

  .close-btn {
    font-size: 1.3rem;
    padding: 0.4rem;
  }

  /* 移动端隐藏关闭按钮 */
  .desktop-only {
    display: none;
  }

  .viewer-navigation {
    padding: 0.75rem 1rem;
  }

  .nav-btn {
    padding: 0.4rem 0.8rem;
    font-size: 1rem;
    min-width: 40px;
  }

  .image-counter {
    font-size: 0.9rem;
  }

  .mobile-swipe-hint {
    display: block;
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .welcome-card {
    padding: 2rem 1.5rem;
  }

  .welcome-card h1 {
    font-size: 1.9rem;
    margin: 0 0 0.75rem 0;
  }

  .welcome-card p {
    font-size: 1.1rem;
  }

  .card {
    padding: 1.5rem;
  }

  .card h2 {
    font-size: 1.3rem;
    margin: 0 0 1rem 0;
  }

  .section {
    margin-bottom: 1.75rem;
  }

  .members-list {
    gap: 2rem;
  }

  .member-title {
    padding: 1rem 1.5rem;
  }

  .member-title h3 {
    font-size: 1.2rem;
  }

  .member-image-wrapper {
    padding: 1.5rem;
  }

  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .members-list {
    gap: 2.5rem;
  }

  .member-title h3 {
    font-size: 1.3rem;
  }

  .member-image-wrapper {
    padding: 2rem;
  }

  .welcome-card h1 {
    font-size: 2.1rem;
  }

  .welcome-card p {
    font-size: 1.2rem;
  }

  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
</style>
