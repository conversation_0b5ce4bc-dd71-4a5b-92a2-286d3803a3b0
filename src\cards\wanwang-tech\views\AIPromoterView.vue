<script setup lang="ts">
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import {
  Reading,
  Timer,
  User,
  ArrowRight,
  Message,
  Collection,
  Phone,
  CopyDocument,
  Link
} from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  // 尝试使用浏览器的返回功能
  if (window.history.length > 1 && document.referrer) {
    // 检查来源页面是否是本站内的页面
    const referrerUrl = new URL(document.referrer)
    const currentUrl = new URL(window.location.href)

    if (referrerUrl.origin === currentUrl.origin && referrerUrl.pathname.includes('/card/wanwang-tech')) {
      router.go(-1)
      return
    }
  }

  // 如果没有合适的历史记录或来源不是本站，则跳转到主页
  router.push('/card/wanwang-tech')
}

const chatWithAI = () => {
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=rguahe68ussz1n813sbw9nqm', '_blank')
}

const copyPhone = async () => {
  try {
    await navigator.clipboard.writeText('18170312776')
    alert('电话号码已复制到剪贴板')
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = '18170312776'
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    alert('电话号码已复制到剪贴板')
  }
}

const callPhone = () => {
  window.location.href = 'tel:18170312776'
}

const copyEmail = async () => {
  try {
    await navigator.clipboard.writeText('<EMAIL>')
    alert('邮箱已复制到剪贴板')
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = '<EMAIL>'
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    alert('邮箱已复制到剪贴板')
  }
}

const sendEmail = () => {
  window.location.href = 'mailto:<EMAIL>'
}

const openWebsite = () => {
  window.open('https://www.jxwwkj.com/', '_blank')
}

// 添加打字机效果
const welcomeText = ref('')
const fullText = '您好，我是江西万网科技的AI宣传员"万小网"，很高兴为您服务。\n想了解我们的产品能力、成功案例，或是探讨项目合作？请直接输入您的问题，我会7x24小时为您解答。'
const typingSpeed = 50
let currentIndex = 0
let typingTimer: number | null = null

const typeText = () => {
  if (currentIndex < fullText.length) {
    welcomeText.value += fullText.charAt(currentIndex)
    currentIndex++
    typingTimer = setTimeout(typeText, typingSpeed) as unknown as number
  }
}

onMounted(() => {
  document.title = '江西万网科技AI名片 - AI宣传员'
  setTimeout(() => {
    typeText()
  }, 500)
})

onBeforeRouteLeave(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>AI宣传员</h1>
    </div>

    <div class="content">
      <div class="ai-promoter-container">
        <div class="ai-card">
          <div class="ai-image-container">
            <div class="ai-image"></div>
            <div class="image-overlay"></div>
          </div>
          <div class="ai-content">
            <h2>万小网 <span class="badge">AI 宣传员</span></h2>
            <div class="typing-container">
              <p class="welcome-text">
                <template v-if="welcomeText">
                  <span v-for="(line, index) in welcomeText.split('\n')" :key="index">
                    {{ line }}<br v-if="index < welcomeText.split('\n').length - 1">
                  </span>
                </template>
                <span class="cursor" v-if="welcomeText.length < fullText.length">|</span>
              </p>
            </div>
            <div class="action-container">
              <el-button type="primary" class="chat-btn" @click="chatWithAI">
                立即对话
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <div class="features-section">
          <h3>我的能力</h3>
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <h4>专业知识库</h4>
              <p>掌握万网科技全面信息，为您提供精准服务</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon speed-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <h4>7x24小时在线</h4>
              <p>随时解答您的问题，无需等待</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <h4>精准全面解答</h4>
              <p>为您提供详细、精准的业务相关信息</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon personalized-icon">
                <el-icon><User /></el-icon>
              </div>
              <h4>智能高效互动</h4>
              <p>根据您的需求提供定制化解答和服务</p>
            </div>
          </div>
        </div>

        <div class="contact-section">
          <h3>联系我们</h3>
          <div class="contact-simple">
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Phone /></el-icon>
                <span>电话</span>
              </div>
              <div class="contact-simple-value">
                <span class="phone-number">18170312776</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="copyPhone">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </button>
                  <button class="btn-simple" @click="callPhone">
                    <el-icon><Phone /></el-icon>
                    拨打
                  </button>
                </div>
              </div>
            </div>
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Message /></el-icon>
                <span>邮箱</span>
              </div>
              <div class="contact-simple-value">
                <span class="email-address"><EMAIL></span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="copyEmail">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </button>
                  <button class="btn-simple" @click="sendEmail">
                    <el-icon><Message /></el-icon>
                    发送
                  </button>
                </div>
              </div>
            </div>
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Link /></el-icon>
                <span>官网</span>
              </div>
              <div class="contact-simple-value">
                <span class="website-url">www.jxwwkj.com</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="openWebsite">
                    <el-icon><Link /></el-icon>
                    访问
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.ai-promoter-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.ai-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
}

.ai-image-container {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.ai-image {
  width: 100%;
  height: 100%;
  background: url('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/SZR.jpeg') center top/cover;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to top, white, transparent);
}

.ai-content {
  padding: 1.5rem;
}

.ai-content h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.badge {
  font-size: 0.8rem;
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-weight: 500;
}

.typing-container {
  margin-bottom: 1.5rem;
  min-height: 6rem;
}

.welcome-text {
  font-size: 1rem;
  line-height: 1.5;
  color: #555;
  margin: 0;
}

.cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: #1693d2;
  animation: blink 0.7s infinite;
  vertical-align: middle;
  margin-left: 2px;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.action-container {
  display: flex;
  justify-content: center;
}

.chat-btn {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 2rem;
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
  transition: all 0.3s ease;
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(22, 147, 210, 0.4);
}

.features-section, .contact-section {
  background: white;
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.features-section h3, .contact-section h3 {
  margin: 0 0 1.25rem 0;
  font-size: 1.2rem;
  color: #333;
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0.75rem;
  border-radius: 0.75rem;
  background-color: #f0f9ff;
  border: 1px solid #e0f2fe;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(22, 147, 210, 0.15);
}

.feature-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  color: white;
  font-size: 1.25rem;
}

.knowledge-icon {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
}

.speed-icon {
  background: linear-gradient(135deg, #1693d2, #60a5fa);
}

.personalized-icon {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
}

.feature-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.feature-item p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* 联系我们简洁布局 */
.contact-simple {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
  margin-top: 0.5rem;
}

.contact-simple-row {
  background: #f0f9ff;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e0f2fe;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 2.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.contact-simple-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1693d2;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 4rem;
}

.contact-simple-icon {
  font-size: 1rem;
  color: #1693d2;
}

.contact-simple-value {
  flex: 1;
  color: #555;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
  min-width: 0;
  flex-wrap: wrap;
}

.phone-number, .email-address, .website-url {
  font-weight: 500;
}

.contact-simple-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-simple {
  background: #1693d2;
  color: white;
  border: none;
  padding: 0.25rem 0.6rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.btn-simple:hover {
  background: #1472a8;
  transform: translateY(-1px);
}

/* 移动端优化 */
@media (max-width: 767px) {
  .contact-simple-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
  }

  .contact-simple-label {
    min-width: auto;
    width: 100%;
  }

  .contact-simple-value {
    width: 100%;
    justify-content: space-between;
    flex-wrap: nowrap;
    align-items: center;
  }

  .phone-number, .email-address, .website-url {
    flex: 1;
    text-align: left;
    word-break: break-all;
    margin-right: 0.5rem;
  }

  .contact-simple-actions {
    flex-shrink: 0;
    margin-left: auto;
  }
}

@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .ai-card {
    flex-direction: row;
    align-items: stretch;
  }

  .ai-image-container {
    width: 50%;
    height: auto;
  }

  .ai-content {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .image-overlay {
    display: none;
  }

  .ai-image {
    height: 100%;
  }

  .contact-simple-row {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .contact-simple-value {
    justify-content: flex-start;
    text-align: left;
    flex: 1;
    display: flex;
    align-items: center;
  }

  .phone-number, .email-address, .website-url {
    text-align: left;
    margin: 0;
    flex: none;
  }

  .contact-simple-actions {
    margin-left: auto;
    flex-shrink: 0;
  }

  .phone-number, .email-address, .website-url {
    text-align: right;
    margin-right: 0.75rem;
  }
}

@media (min-width: 992px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
