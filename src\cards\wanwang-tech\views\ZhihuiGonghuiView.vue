<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('ZhihuiGonghuiView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>智慧工会服务云平台</h1>
    </div>

    <div class="content">
      <!-- 产品概述 -->
      <div class="section overview-section">
        <div class="section-header">
          <h2>产品概述</h2>
        </div>
        <div class="overview-content">
          <div class="overview-image">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiGongHui.png" alt="智慧工会服务云平台" />
          </div>
          <div class="overview-text">
            <p>智慧工会是中国工会为适应互联网时代特点，加快改革创新而提出的鲜明而具体的发展目标，是工会组织通过运用互联网、大数据、云计算、人工智能等先进信息技术构建的工会智能化服务职工体系，突出工会干部的主观能动性和工会工作的精神文化层面，为职工群众提供普惠性、便捷性、精准性、常态化服务，形成线上线下相互融合的工会工作新格局。</p>
            <p>为适应新时期新形势下工会服务发展与创新的要求，努力扩大工会组织覆盖面，激发基层工会活力、完善工会信息化和数据库建设整合社会资源强化工会服务能力，江西万网科技有限公司推出"智慧工会服务云平台"为工会组织信息化、智慧化转型提供有力支持。</p>
            <p>江西万网智能工会系统通过智能的、社会化的职工服务，提升工会服务效能，加快向服务型、学习型、创新型 "新三型"工会组织发展目标转型，为职工提供更高效、更便捷、更贴心的服务，提升职工群众幸福指数，切实提升工会服务品质。</p>
          </div>
        </div>
      </div>

      <!-- 系统功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>系统功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <h3>会员服务</h3>
            <ul>
              <li>维权保障系统</li>
              <li>培训就业系统</li>
              <li>主题活动系统</li>
              <li>维权保障系统</li>
            </ul>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏛️</div>
            <h3>组织管理</h3>
            <ul>
              <li>业务管理系统</li>
              <li>权限管理系统</li>
              <li>绩效评定系统</li>
              <li>资金管理系统</li>
            </ul>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🤝</div>
            <h3>志愿者/社会组织管理</h3>
            <ul>
              <li>志愿者招募系统</li>
              <li>志愿者管理系统</li>
              <li>社会组织孵化系统</li>
              <li>公共项目承接系统</li>
            </ul>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <h3>辅助决策</h3>
            <ul>
              <li>业务检测管理</li>
              <li>数据分析系统</li>
              <li>数据交接系统</li>
              <li>图形统计系统</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 系统特色 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>系统特色</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">📈</div>
            <h3>提升服务水平</h3>
            <p>实现工会业务、管理、服务的数字化、信息化、网络化、实现各级工会组织工作协同，切实提高工会服务管理过程的质量、效率和效益，提高工会组织在职工中的吸引力、凝聚力和影响力，激发工会工作活力，促进企业和谐健康发展。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <h3>提升效率水平</h3>
            <p>依托工会组织管理和大数据平台对工会服务对象进行动态管理，提高数据查询系统便捷和准确性，减缓工作强度，提高工作效率。通过对服务对象的历史与当前数据、潜在线索和数据挖掘、各种信息关联性等大数据分析，为管理决策提供数据支撑。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🧠</div>
            <h3>形成智慧化管理理念</h3>
            <p>探索符合工会工作特色的顶层设计，形成符合工会工作的当前发展阶段、未来发展趋势的充分交互有机结合，促进工会服务管理服务模式转变提升。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔗</div>
            <h3>线上线下一体化运营</h3>
            <p>将线上服务终端与线下运营中心相结合，打造全国独一无二的线上线下的一体化运营体系。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🤝</div>
            <h3>工会深度整合社会资源</h3>
            <p>引入社会力量，整合各方资源，打造集惠民、维权和素质提升于一体的大平台，完善工会工作格局。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">💰</div>
            <h3>打造平台"造血机制"</h3>
            <p>坚持"政府主导，市场运营"，整合区域资源，打造自身盈利能力，实现平台的可持续发展。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📊</div>
            <h3>深耕平台"大数据"</h3>
            <p>构筑平台大数据库，进一步将服务衍伸到社会生活各个领域，挖掘更深层次的居民需求和服务模式。</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 产品概述样式 */
.overview-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.overview-image {
  width: 350px;
  height: 220px;
  flex-shrink: 0;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.overview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overview-text {
  flex: 1;
  color: #333;
  line-height: 1.8;
}

.overview-text p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  text-align: justify;
}

/* 功能特性样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.feature-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item li {
  padding: 0.5rem 0;
  font-size: 0.95rem;
  opacity: 0.9;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-item li:last-child {
  border-bottom: none;
}

/* 优势特色样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.advantage-item:hover {
  border-color: #1693d2;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1693d2;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #666;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .overview-content {
    flex-direction: column;
    text-align: center;
  }

  .overview-image {
    width: 150px;
    height: 150px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }
}
</style>
