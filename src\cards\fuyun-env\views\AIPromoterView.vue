<script setup lang="ts">
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import {
  ArrowLeft,
  Reading,
  Timer,
  User,
  ArrowRight,
  Message,
  Collection,
  Phone,
  Location
} from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/fuyun-env')
}

const chatWithAI = () => {
  // 这里可以替换为实际的AI对话链接
  window.open("https://ai.sdtaa.com:3105/chat/share?shareId=uq3wwdjn0jr60cmzd0wwlkwo")
}

const copyPhone = () => {
  navigator.clipboard.writeText('13793 8469690')
  alert('电话号码已复制到剪贴板')
}

const callPhone = () => {
  window.location.href = 'tel:13793 8469690'
}

const copyEmail = () => {
  navigator.clipboard.writeText('<EMAIL>')
  alert('邮箱已复制到剪贴板')
}


const sendEmail = () => {
  window.location.href = 'mailto:<EMAIL>'
}

const copyFax = () => {
  navigator.clipboard.writeText('13793 8469877')
  alert('传真已复制到剪贴板')
}
const callFax = () => {
  window.location.href = 'tel:13793 8469877'
}

const openWebsite = () => {
  window.open('http://www.jxfuyun.com', '_blank')
}

// 添加打字机效果
const welcomeText = ref('')
const fullText = '您好，我是福运环保的AI宣传员"小福"，很高兴为您服务。\n想了解我们的产品能力、成功案例，或是探讨项目合作？请直接输入您的问题，我会7x24小时为您解答。'
const typingSpeed = 50
let currentIndex = 0
let typingTimer: number | null = null

const typeText = () => {
  if (currentIndex < fullText.length) {
    welcomeText.value += fullText.charAt(currentIndex)
    currentIndex++
    typingTimer = setTimeout(typeText, typingSpeed) as unknown as number
  }
}

onMounted(() => {
  document.title = '福运环保AI名片 - AI宣传员'
  setTimeout(() => {
    typeText()
  }, 500)
})

onBeforeRouteLeave(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>AI宣传员：小福</h1>
    </div>

    <div class="content">
      <div class="ai-promoter-container">
        <div class="ai-card">
          <div class="ai-image-container">
            <div class="ai-image"></div>
          </div>
          <div class="ai-content">
            <h2>小福 <span class="badge">AI 宣传员</span></h2>
            <div class="typing-container">
              <p class="welcome-text">
                <template v-if="welcomeText">
                  <span v-for="(line, index) in welcomeText.split('\n')" :key="index">
                    {{ line }}<br v-if="index < welcomeText.split('\n').length - 1">
                  </span>
                </template>
                <span class="cursor" v-if="welcomeText.length < fullText.length">|</span>
              </p>
            </div>
            <div class="action-container">
              <el-button type="primary" class="chat-btn" @click="chatWithAI">
                立即对话
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <div class="features-section">
          <h3>我的能力</h3>
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <h4>环保专业知识库</h4>
              <p>掌握环保行业全面信息，为您提供专业服务</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon speed-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <h4>7x24小时在线</h4>
              <p>随时解答您的问题，无需等待</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <h4>精准全面解答</h4>
              <p>为您提供详细、精准的环保解决方案</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon personalized-icon">
                <el-icon><User /></el-icon>
              </div>
              <h4>智能高效互动</h4>
              <p>根据您的需求提供定制化解答和服务</p>
            </div>
          </div>
        </div>

        <div class="contact-section">
          <h3>联系我们</h3>
          <div class="contact-simple">
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Phone /></el-icon>
                <span>联系电话：</span>
              </div>
              <div class="contact-simple-value">
                <span class="phone-number">13793 8469690</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="copyPhone">复制</button>
                  <button class="btn-simple" @click="callPhone">拨打</button>
                </div>
              </div>
            </div> 
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Message /></el-icon>
                <span>传真：</span>
              </div>
              <div class="contact-simple-value">
                <span class="email-address">13793 8469877</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="copyFax">复制</button>
                  <button class="btn-simple" @click="callFax">拨打</button>
                </div>
              </div>
            </div>

            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Message /></el-icon>
                <span>电子邮箱：</span>
              </div>
              <div class="contact-simple-value">
                <span class="email-address"><EMAIL></span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="copyEmail">复制</button>
                  <button class="btn-simple" @click="sendEmail">发送</button>
                </div>
              </div>
            </div> 
            
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Location /></el-icon>
                <span>官方网站：</span>
              </div>
              <div class="contact-simple-value">
                <span class="website-url">http://www.jxfuyun.com</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="openWebsite">访问</button>
                </div>
              </div>
            </div>

               <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Message /></el-icon>
                <span>邮箱编码：</span>
              </div>
              <div class="contact-simple-value">
                <span class="email-address">334100</span>
              </div>
            </div>

              <div class="contact-simple-row">
              <div class="contact-simple-label">
                <el-icon class="contact-simple-icon"><Message /></el-icon>
                <span>地址：</span>
              </div>
              <div class="contact-simple-value">
                <span class="email-address">江西省上饶经济技术开发区江家大道51号</span>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 5rem;
  padding-bottom: 5rem; /* 为底部标签栏留出空间 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.ai-promoter-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.ai-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: row;
  width: 100%;
  min-height: 300px;
}

.ai-image-container {
  position: relative;
  height: 300px;
  overflow: hidden;
  width: 40%;
  flex-shrink: 0;
}

.ai-image {
  width: 100%;
  height: 100%;
  background: url('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/szr-pc.jpeg') center top/cover;
  background-size: 250%;
  background-position: center 5%;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to top, white, transparent);
}

.ai-content {
  padding: 1.5rem;
  width: 60%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.ai-content h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.badge {
  font-size: 0.8rem;
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-weight: 500;
}

.typing-container {
  margin-bottom: 1.5rem;
  min-height: 6rem;
}

.welcome-text {
  font-size: 1rem;
  line-height: 1.5;
  color: #555;
  margin: 0;
}

.cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: #1e3470;
  animation: blink 0.7s infinite;
  vertical-align: middle;
  margin-left: 2px;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.action-container {
  display: flex;
  justify-content: center;
}

.chat-btn {
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 2rem;
  box-shadow: 0 4px 15px rgba(30, 52, 112, 0.3);
  transition: all 0.3s ease;
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(30, 52, 112, 0.4);
}

/* 功能区域样式 */
.features-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.features-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  color: #1e3470;
  text-align: center;
  font-weight: 600;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  text-align: center;
  padding: 1.5rem;
  border-radius: 0.75rem;
  background: #f8fafc;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  background: white;
}

.feature-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  font-size: 1.5rem;
  color: white;
}

.knowledge-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.speed-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.personalized-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.feature-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #1e3470;
  font-weight: 600;
}

.feature-item p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 联系方式区域样式 */
.contact-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.contact-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  color: #1e3470;
  text-align: center;
  font-weight: 600;
}

.contact-simple {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-simple-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.contact-simple-row:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.contact-simple-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
  color: #374151;
  min-width: 120px;
}

.contact-simple-icon {
  font-size: 1.25rem;
  color: #1e3470;
}

.contact-simple-value {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  justify-content: space-between;
}

.phone-number,
.email-address,
.website-url {
  color: #1e3470;
  font-weight: 500;
  font-size: 0.95rem;
}

.contact-simple-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-simple {
  background: linear-gradient(135deg, #1e3470, #2d4a8a);
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-simple:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 52, 112, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .ai-card {
    flex-direction: column !important;
    min-height: auto;
  }

  .ai-image-container {
    width: 100% !important;
    height: 200px;
  }

  .ai-content {
    width: 100% !important;
  }

  .ai-card,
  .features-section,
  .contact-section {
    padding: 1.5rem;
  }

  .ai-image {
    background-size: 200%;
    background-position: center 10%;
  }

  .features-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .feature-item {
    padding: 0.75rem;
  }

  .feature-item h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
  }

  .feature-item p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .feature-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .contact-simple-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .contact-simple-value {
    width: 100%;
    justify-content: space-between;
  }

  .contact-simple-label {
    min-width: auto;
  }

  .ai-content h2 {
    font-size: 1.25rem;
  }

  .welcome-text {
    font-size: 0.95rem;
  }

  .chat-btn {
    padding: 0.6rem 1.25rem;
    font-size: 0.95rem;
  }
}
</style>
