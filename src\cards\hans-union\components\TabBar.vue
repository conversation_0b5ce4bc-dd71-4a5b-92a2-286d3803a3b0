<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  HomeFilled, 
  Box
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 汉氏联合底部导航配置
const tabs = ref([
  {
    id: 'home',
    name: '主页',
    icon: HomeFilled,
    route: '/card/hans-union'
  },
  {
    id: 'product-center',
    name: '产品中心',
    icon: Box,
    route: '/card/hans-union/product-center'
  }
])

// 获取当前路径
const currentPath = computed(() => route.path)

// 判断是否为当前激活的标签
const isActive = (tabRoute: string) => {
  return currentPath.value === tabRoute
}

// 导航到指定路由
const navigateTo = (tabRoute: string) => {
  router.push(tabRoute)
}
</script>

<template>
  <div class="tab-bar-container">
    <div class="tab-bar">
      <div 
        v-for="tab in tabs" 
        :key="tab.id" 
        class="tab-item"
        :class="{ active: isActive(tab.route) }"
        @click="navigateTo(tab.route)"
      >
        <el-icon class="tab-icon"><component :is="tab.icon" /></el-icon>
        <span class="tab-name">{{ tab.name }}</span>
      </div>
    </div>
    <div class="safe-area-bottom"></div>
  </div>
</template>

<style scoped>
/* 底部导航容器 */
.tab-bar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* 底部导航栏 */
.tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 3.5rem;
  padding: 0 1rem;
}

/* 导航项样式 */
.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #a0a0a0;
}

/* 激活状态的导航项 */
.tab-item.active {
  color: #0f9da8;
}

/* 导航图标样式 */
.tab-icon {
  font-size: 1.4rem;
  margin-bottom: 0.2rem;
  transition: transform 0.2s ease;
}

/* 导航文本样式 */
.tab-name {
  font-size: 0.7rem;
  font-weight: 500;
}

/* 安全区域底部适配 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom, 0);
  width: 100%;
}

/* 点击效果 */
.tab-item:active .tab-icon {
  transform: scale(0.95);
}

/* 悬停效果（桌面端） */
@media (hover: hover) {
  .tab-item:hover {
    color: #0f9da8;
  }
  
  .tab-item:hover .tab-icon {
    transform: scale(1.1);
  }
}

/* 桌面端响应式设计 */
@media (min-width: 768px) {
  .tab-bar {
    height: 4rem;
    padding: 0 2rem;
  }
  
  .tab-icon {
    font-size: 1.6rem;
  }
  
  .tab-name {
    font-size: 0.8rem;
  }
}
@media (max-width: 768px) {
  .tab-bar-container {
    display: none !important;
  }
}
</style>