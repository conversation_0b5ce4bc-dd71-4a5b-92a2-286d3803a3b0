<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术参数
const technicalSpecs = reactive([
  { 
    type: '圆笼型',
    topDiameter: '115',
    bottomDiameter: '115',
    length: '2000-6000',
    note: '多种长度可选'
  },
  { 
    type: '圆笼型',
    topDiameter: '125',
    bottomDiameter: '125',
    length: '2000-6000',
    note: '多种长度可选'
  },
  { 
    type: '圆笼型',
    topDiameter: '147',
    bottomDiameter: '147',
    length: '2000-6000',
    note: '多种长度可选'
  },
  { 
    type: '圆笼型',
    topDiameter: '195',
    bottomDiameter: '195',
    length: '2000-6000',
    note: '多种长度可选'
  },
  { 
    type: '扁袋型',
    topDiameter: '800',
    bottomDiameter: '800',
    length: '2000-6000',
    note: '扁袋专用'
  },
  { 
    type: '扁袋型',
    topDiameter: '900',
    bottomDiameter: '900',
    length: '2000-6000',
    note: '扁袋专用'
  },
  { 
    type: '信封型',
    topDiameter: '1500×750×25',
    bottomDiameter: '1500×750×25',
    length: '-',
    note: '长×宽×高'
  }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '全自动电焊机一次焊接成型',
    description: '采用先进的全自动电焊机设备，一次焊接成型，确保产品质量稳定，焊接点牢固可靠。'
  },
  {
    title: '焊接牢固、外表光洁挺直',
    description: '焊接工艺精湛，焊点牢固，外表光洁平整，骨架挺直不变形，确保滤袋正常工作。'
  },
  {
    title: '使滤袋不受损伤',
    description: '表面光滑无毛刺，边角圆润处理，有效保护滤袋不受损伤，延长滤袋使用寿命。'
  },
  {
    title: '应用轻巧，便于安装和维护',
    description: '结构设计合理，重量轻，安装简便，维护方便，降低操作难度和维护成本。'
  },
  {
    title: '多种表面处理工艺',
    description: '表面处理采用镀锌、喷塑或有机硅工艺处理，提高耐腐蚀性能和使用寿命。'
  }
])

// 表面处理工艺
const surfaceTreatments = reactive([
  {
    name: '镀锌处理',
    description: '热镀锌或电镀锌处理，提供优异的防腐蚀性能，适用于一般工况环境。',
    advantages: ['防腐蚀性能好', '成本经济', '工艺成熟', '适用范围广']
  },
  {
    name: '喷塑处理',
    description: '静电喷塑工艺，形成均匀的塑料涂层，具有良好的防腐和装饰效果。',
    advantages: ['防腐性能优异', '表面美观', '耐磨性好', '环保无污染']
  },
  {
    name: '有机硅处理',
    description: '有机硅涂层处理，具有优异的耐高温和耐化学腐蚀性能，适用于特殊工况。',
    advantages: ['耐高温性能好', '耐化学腐蚀', '表面光滑', '不粘附粉尘']
  }
])

// 应用范围
const applications = reactive([
  {
    title: '袋式除尘器',
    description: '各种袋式除尘器的核心支撑部件，确保滤袋正常工作。'
  },
  {
    title: '脉冲除尘器',
    description: '脉冲喷吹除尘器的专用骨架，承受脉冲清灰压力。'
  },
  {
    title: '反吹除尘器',
    description: '反吹式除尘器的支撑骨架，适应反吹清灰方式。'
  },
  {
    title: '机械振打除尘器',
    description: '机械振打除尘器的骨架，承受振打清灰冲击。'
  },
  {
    title: '工业除尘系统',
    description: '各种工业除尘系统的关键组件，保证除尘效果。'
  },
  {
    title: '环保设备',
    description: '环保除尘设备的重要配件，提高设备可靠性。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>除尘布袋骨架</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/FuYunHuanBao/chuchengujia.jpg"
        </div>
        <div class="hero-content">
          <h2>除尘布袋骨架</h2>
          <p class="product-subtitle">滤袋的"肋骨"，全自动电焊一次成型</p>
          <div class="product-intro">
            <p>除尘骨架是滤袋的"肋骨"，采用全自动电焊机一次焊接成型，具有焊接牢固、外表光洁挺直、使滤袋不受损伤的特点。产品应用轻巧，便于安装和维护。</p>
            <p>表面处理采用镀锌、喷塑或有机硅工艺处理，提高耐腐蚀性能和使用寿命。广泛应用于各种袋式除尘器，是除尘系统不可缺少的重要组件。</p>
          </div>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术参数
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>骨架类型</th>
                <th>上口径(mm)</th>
                <th>下口径(mm)</th>
                <th>长度(mm)</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.type + spec.topDiameter">
                <td>{{ spec.type }}</td>
                <td>{{ spec.topDiameter }}</td>
                <td>{{ spec.bottomDiameter }}</td>
                <td>{{ spec.length }}</td>
                <td>{{ spec.note }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 表面处理工艺 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎨</span>
          表面处理工艺
        </h3>
        <div class="treatments">
          <div
            v-for="(treatment, index) in surfaceTreatments"
            :key="index"
            class="treatment-card"
          >
            <h4>{{ treatment.name }}</h4>
            <p class="treatment-desc">{{ treatment.description }}</p>
            <div class="advantages">
              <div class="advantage-tag" v-for="advantage in treatment.advantages" :key="advantage">
                {{ advantage }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用范围 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用范围
        </h3>
        <div class="applications">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🔧</div>
            <div class="indicator-content">
              <h4>焊接工艺</h4>
              <div class="indicator-value">全自动电焊</div>
              <p>一次成型</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📏</div>
            <div class="indicator-content">
              <h4>规格范围</h4>
              <div class="indicator-value">115-1500mm</div>
              <p>多种规格</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📐</div>
            <div class="indicator-content">
              <h4>长度范围</h4>
              <div class="indicator-value">2000-6000mm</div>
              <p>可定制</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🛡️</div>
            <div class="indicator-content">
              <h4>表面处理</h4>
              <div class="indicator-value">三种工艺</div>
              <p>防腐耐用</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">⚖️</div>
            <div class="indicator-content">
              <h4>重量特性</h4>
              <div class="indicator-value">轻巧设计</div>
              <p>便于安装</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔩</div>
            <div class="indicator-content">
              <h4>结构强度</h4>
              <div class="indicator-value">焊接牢固</div>
              <p>挺直不变形</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 骨架类型详解 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          骨架类型详解
        </h3>
        <div class="cage-types">
          <div class="type-category">
            <h4>圆笼型骨架</h4>
            <div class="type-items">
              <div class="type-item">
                <div class="type-label">Φ115</div>
                <div class="type-info">
                  <div class="type-spec">直径: 115mm</div>
                  <div class="type-desc">适用于小直径圆形滤袋，常用于小型除尘器</div>
                </div>
              </div>
              <div class="type-item">
                <div class="type-label">Φ125</div>
                <div class="type-info">
                  <div class="type-spec">直径: 125mm</div>
                  <div class="type-desc">标准规格，应用最广泛的圆笼型骨架</div>
                </div>
              </div>
              <div class="type-item">
                <div class="type-label">Φ147</div>
                <div class="type-info">
                  <div class="type-spec">直径: 147mm</div>
                  <div class="type-desc">中等规格，适用于中型除尘系统</div>
                </div>
              </div>
              <div class="type-item">
                <div class="type-label">Φ195</div>
                <div class="type-info">
                  <div class="type-spec">直径: 195mm</div>
                  <div class="type-desc">大直径规格，适用于大型除尘器</div>
                </div>
              </div>
            </div>
          </div>
          <div class="type-category">
            <h4>扁袋型骨架</h4>
            <div class="type-items">
              <div class="type-item">
                <div class="type-label">800</div>
                <div class="type-info">
                  <div class="type-spec">宽度: 800mm</div>
                  <div class="type-desc">适用于扁袋式除尘器，过滤面积大</div>
                </div>
              </div>
              <div class="type-item">
                <div class="type-label">900</div>
                <div class="type-info">
                  <div class="type-spec">宽度: 900mm</div>
                  <div class="type-desc">大规格扁袋骨架，适用于大型扁袋除尘器</div>
                </div>
              </div>
            </div>
          </div>
          <div class="type-category">
            <h4>信封型骨架</h4>
            <div class="type-items">
              <div class="type-item">
                <div class="type-label">信封型</div>
                <div class="type-info">
                  <div class="type-spec">规格: 1500×750×25mm</div>
                  <div class="type-desc">特殊结构，适用于信封型滤袋除尘器</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🤖</div>
            <div class="advantage-content">
              <h4>全自动电焊一次成型</h4>
              <p>采用先进的全自动电焊机设备，一次焊接成型，确保产品质量稳定，焊接点牢固可靠，生产效率高，产品一致性好。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">💎</div>
            <div class="advantage-content">
              <h4>焊接牢固外表光洁</h4>
              <p>焊接工艺精湛，焊点牢固，外表光洁平整，骨架挺直不变形，表面光滑无毛刺，确保滤袋正常工作不受损伤。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🛡️</div>
            <div class="advantage-content">
              <h4>保护滤袋不受损伤</h4>
              <p>表面光滑无毛刺，边角圆润处理，有效保护滤袋不受损伤，延长滤袋使用寿命，降低更换频率和维护成本。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚖️</div>
            <div class="advantage-content">
              <h4>轻巧便于安装维护</h4>
              <p>结构设计合理，重量轻，安装简便，维护方便，降低操作难度和维护成本，提高工作效率，减少停机时间。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎨</div>
            <div class="advantage-content">
              <h4>多种表面处理工艺</h4>
              <p>提供镀锌、喷塑、有机硅三种表面处理工艺，满足不同工况环境需求，提高耐腐蚀性能和使用寿命。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔧</div>
            <div class="advantage-content">
              <h4>规格齐全可定制</h4>
              <p>提供圆笼型、扁袋型、信封型等多种类型，规格齐全，可根据客户需求定制特殊规格，满足不同除尘器的配套需求。</p>
            </div>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.5rem;
  min-width: 600px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.5rem 0.25rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.45rem;
  line-height: 1.1;
  border: 1px solid #2563eb;
}

.specs-table td {
  padding: 0.5rem 0.25rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.5rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.3;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 表面处理工艺 */
.treatments {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.treatment-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.treatment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.treatment-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
}

.treatment-desc {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

.advantages {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.advantage-tag {
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 应用范围 */
.applications {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.application-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 骨架类型详解 */
.cage-types {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.type-category {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border-left: 4px solid #3b82f6;
}

.type-category h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e3470;
}

.type-items {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
  transition: transform 0.2s ease;
}

.type-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.type-label {
  font-size: 1rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 0.25rem;
  padding: 0.5rem 0.75rem;
  min-width: 4rem;
  text-align: center;
  flex-shrink: 0;
}

.type-info {
  flex: 1;
}

.type-spec {
  font-size: 0.9rem;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.type-desc {
  font-size: 0.8rem;
  color: #4b5563;
  line-height: 1.3;
}

/* 产品优势 */
.advantages {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .specs-table {
    font-size: 0.6rem;
  }

  .specs-table th {
    padding: 0.75rem 0.5rem;
    font-size: 0.55rem;
  }

  .specs-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.6rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }

  .feature-card p {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .cage-types {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .type-items {
    grid-template-columns: repeat(2, 1fr);
  }

  .type-item {
    padding: 1.25rem;
  }

  .type-label {
    font-size: 1.1rem;
    padding: 0.6rem 0.9rem;
    min-width: 5rem;
  }

  .type-spec {
    font-size: 1rem;
  }

  .type-desc {
    font-size: 0.85rem;
  }

  .treatments {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .treatment-card {
    padding: 2rem;
  }

  .treatment-card h4 {
    font-size: 1.2rem;
  }

  .treatment-desc {
    font-size: 0.9rem;
  }

  .advantages {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .application-card {
    padding: 1.5rem;
  }

  .application-card h4 {
    font-size: 1.1rem;
  }

  .application-card p {
    font-size: 0.9rem;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.65rem;
  }

  .specs-table th {
    font-size: 0.6rem;
  }

  .specs-table td {
    font-size: 0.65rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .type-items {
    grid-template-columns: repeat(2, 1fr);
  }

  .advantages {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .type-items {
    grid-template-columns: 1fr;
  }

  .applications {
    grid-template-columns: 1fr;
  }

  .treatments {
    grid-template-columns: 1fr;
  }
}
</style>
