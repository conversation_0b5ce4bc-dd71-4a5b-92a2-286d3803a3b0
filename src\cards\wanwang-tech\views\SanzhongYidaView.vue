<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

// 系统截图数据
const systemImages = [
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanzhongyida21.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanzhongyida22.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanzhongyida23.jpg',
  'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/sanzhongyida24.jpg'
]

const currentImageIndex = ref(0)

const nextImage = () => {
  currentImageIndex.value = (currentImageIndex.value + 1) % systemImages.length
}

const prevImage = () => {
  currentImageIndex.value = currentImageIndex.value === 0 ? systemImages.length - 1 : currentImageIndex.value - 1
}

const goToImage = (index: number) => {
  currentImageIndex.value = index
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('SanzhongYidaView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>"三重一大"信息监管平台</h1>
    </div>

    <div class="content">
      <!-- 平台介绍 -->
      <div class="section intro-section">
        <div class="intro-card">
          <div class="intro-content">
            <div class="intro-title">
              <h3>"三重一大"信息监管平台</h3>
              <div class="title-decoration"></div>
            </div>
            <div class="intro-description">
              <div class="description-item">
                <div class="item-icon">🏛️</div>
                <p>"三重一大"信息监管平台是一个针对重大决策、重要干部任免、重大项目投资决策、大额资金使用等核心事项进行综合性管理的系统。它运用信息技术手段，实现了对"三重一大"事项的全过程、全方位、实时性的监管，旨在提高决策的科学性、规范性和透明度，降低决策风险，确保组织的健康稳定发展。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统截图展示 -->
      <div class="section gallery-section">
        <div class="section-header">
          <h2>系统功能展示</h2>
        </div>
        <div class="carousel-container">
          <div class="carousel-wrapper">
            <div class="carousel-main">
              <img 
                :src="systemImages[currentImageIndex]" 
                :alt="`三重一大信息监管平台截图 ${currentImageIndex + 1}`"
                class="carousel-image"
              />
              <button @click="prevImage" class="carousel-btn prev-btn">‹</button>
              <button @click="nextImage" class="carousel-btn next-btn">›</button>
            </div>
            <div class="carousel-thumbnails">
              <div 
                v-for="(image, index) in systemImages" 
                :key="index"
                @click="goToImage(index)"
                :class="['thumbnail', { active: index === currentImageIndex }]"
              >
                <img :src="image" :alt="`缩略图 ${index + 1}`" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心功能 -->
      <div class="section features-section">
        <div class="section-header">
          <h2>核心功能</h2>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">⚖️</div>
            <h3>重大决策管理</h3>
            <div class="feature-content">
              <p>对重大决策事项进行全流程管理，包括决策提议、讨论、表决、执行等各个环节的记录和监督。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <h3>重要干部任免</h3>
            <div class="feature-content">
              <p>规范干部任免程序，确保干部选拔任用的公开、公平、公正，提高干部队伍建设质量。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏗️</div>
            <h3>重大项目投资决策</h3>
            <div class="feature-content">
              <p>对重大项目投资进行科学论证和决策管理，确保投资决策的合理性和有效性。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">💰</div>
            <h3>大额资金使用</h3>
            <div class="feature-content">
              <p>严格管控大额资金使用，建立完善的审批流程和监督机制，防范财务风险。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <h3>决策分析统计</h3>
            <div class="feature-content">
              <p>提供决策数据分析和统计功能，为领导决策提供科学依据和参考。</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔍</div>
            <h3>监督检查</h3>
            <div class="feature-content">
              <p>建立健全监督检查机制，确保"三重一大"制度的有效执行和落实。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统特点 -->
      <div class="section characteristics-section">
        <div class="section-header">
          <h2>系统特点</h2>
        </div>
        <div class="characteristics-grid">
          <div class="characteristic-item">
            <div class="characteristic-header">
              <div class="characteristic-icon">🔄</div>
              <h3>全过程监管</h3>
            </div>
            <div class="characteristic-content">
              <p>从事项提出到决策执行的全过程进行跟踪监管，确保每个环节都有据可查。</p>
            </div>
          </div>
          <div class="characteristic-item">
            <div class="characteristic-header">
              <div class="characteristic-icon">🌐</div>
              <h3>全方位覆盖</h3>
            </div>
            <div class="characteristic-content">
              <p>涵盖"三重一大"所有事项类型，实现监管的全方位覆盖，不留死角。</p>
            </div>
          </div>
          <div class="characteristic-item">
            <div class="characteristic-header">
              <div class="characteristic-icon">⏰</div>
              <h3>实时性监控</h3>
            </div>
            <div class="characteristic-content">
              <p>提供实时监控功能，及时发现和处理异常情况，确保监管的时效性。</p>
            </div>
          </div>
          <div class="characteristic-item">
            <div class="characteristic-header">
              <div class="characteristic-icon">🔒</div>
              <h3>规范性管理</h3>
            </div>
            <div class="characteristic-content">
              <p>建立标准化的管理流程和制度，确保决策程序的规范性和合规性。</p>
            </div>
          </div>
          <div class="characteristic-item">
            <div class="characteristic-header">
              <div class="characteristic-icon">💡</div>
              <h3>科学性决策</h3>
            </div>
            <div class="characteristic-content">
              <p>运用科学的决策方法和工具，提高决策的科学性和准确性。</p>
            </div>
          </div>
          <div class="characteristic-item">
            <div class="characteristic-header">
              <div class="characteristic-icon">👁️</div>
              <h3>透明度提升</h3>
            </div>
            <div class="characteristic-content">
              <p>增强决策过程的透明度，接受监督，提高组织的公信力和权威性。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台优势 -->
      <div class="section advantages-section">
        <div class="section-header">
          <h2>平台优势</h2>
        </div>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">🛡️</div>
            <h3>风险防控</h3>
            <p>有效防范决策风险，降低因决策失误带来的损失和影响。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📈</div>
            <h3>效率提升</h3>
            <p>优化决策流程，提高决策效率，缩短决策周期。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔍</div>
            <h3>监督强化</h3>
            <p>强化内部监督机制，确保权力在阳光下运行。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">📋</div>
            <h3>制度完善</h3>
            <p>完善"三重一大"制度体系，规范决策行为。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">💻</div>
            <h3>信息化管理</h3>
            <p>运用现代信息技术，实现管理的数字化转型。</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <h3>精准监管</h3>
            <p>实现精准化监管，提高监管的针对性和有效性。</p>
          </div>
        </div>
      </div>

      <!-- 应用价值 -->
      <div class="section value-section">
        <div class="section-header">
          <h2>应用价值</h2>
        </div>
        <div class="value-content">
          <p>"三重一大"信息监管平台通过信息化手段，有效解决了传统"三重一大"制度执行中的痛点问题。平台实现了对重大决策、重要干部任免、重大项目投资决策、大额资金使用等核心事项的全过程、全方位、实时性监管，大大提升了决策的科学性、规范性和透明度。通过建立完善的监督机制和风险防控体系，有效降低了决策风险，确保了组织的健康稳定发展。平台的应用不仅提高了管理效率，更重要的是强化了权力监督，推进了党风廉政建设，为构建现代化治理体系提供了有力支撑。</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 平台介绍样式 */
.intro-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  border: 1px solid rgba(156, 39, 176, 0.1);
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #9c27b0 0%, #7b1fa2 100%);
}

.intro-content {
  text-align: center;
}

.intro-title {
  margin-bottom: 2rem;
}

.intro-title h3 {
  color: #7b1fa2;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.title-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #9c27b0 0%, #7b1fa2 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-description {
  display: grid;
  gap: 1.5rem;
  text-align: left;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #9c27b0;
}

.item-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.description-item p {
  margin: 0;
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
}

/* 轮播图样式 */
.carousel-container {
  max-width: 100%;
  margin: 0 auto;
}

.carousel-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.carousel-main {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.prev-btn {
  left: 10px;
}

.next-btn {
  right: 10px;
}

.carousel-thumbnails {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.thumbnail {
  width: 100px;
  height: 70px;
  border-radius: 0.5rem;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.thumbnail:hover {
  border-color: #1693d2;
  transform: scale(1.05);
}

.thumbnail.active {
  border-color: #1693d2;
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 核心功能样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.feature-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* 系统特点样式 */
.characteristics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.characteristic-item {
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.characteristic-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.characteristic-header {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
  color: white;
  padding: 1.2rem 1.5rem;
  display: flex;
  align-items: center;
}

.characteristic-icon {
  font-size: 1.3rem;
  margin-right: 0.8rem;
}

.characteristic-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.characteristic-content {
  padding: 1.5rem;
}

.characteristic-content p {
  color: #333;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
  text-align: justify;
}

/* 平台优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.advantage-item {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.advantage-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.advantage-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* 应用价值样式 */
.value-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .intro-description {
    gap: 1rem;
  }

  .description-item {
    padding: 1rem;
  }

  .carousel-main {
    height: 250px;
  }

  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .thumbnail {
    width: 70px;
    height: 50px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .characteristics-grid {
    grid-template-columns: 1fr;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .characteristic-header {
    padding: 1rem 1.2rem;
  }

  .characteristic-content {
    padding: 1.2rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }

  .intro-title h3 {
    font-size: 1.5rem;
  }
}
</style>
