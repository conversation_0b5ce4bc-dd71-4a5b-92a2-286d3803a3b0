# 多项目名片平台架构说明文档

## 项目概述

本平台是一个基于Vue 3和TypeScript开发的多项目名片展示平台，提供多卡片视图展示不同的项目和服务。该项目采用现代化的前端技术栈，包括Vue 3、Vue Router、Element Plus等，构建了一个灵活可扩展的项目展示系统。

## 技术栈

- **前端框架**：Vue 3 (^3.4.38)
- **类型系统**：TypeScript (^5.5.3)
- **路由管理**：Vue Router (^4.2.5)
- **UI组件库**：Element Plus (^2.4.4)
- **构建工具**：Vite (^5.4.2)
- **CSS预处理**：Sass (^1.69.5)
- **工具库**：@vueuse/core (^10.7.0)

## 项目结构

```
项目根目录/
├── node_modules/           # 项目依赖
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── assets/             # 全局资源文件
│   ├── cards/              # 项目卡片模块（各子项目）
│   │   ├── project-a/      # 项目A
│   │   │   ├── assets/     # 项目A资源
│   │   │   ├── components/ # 项目A组件
│   │   │   ├── views/      # 项目A视图
│   │   │   └── router.ts   # 项目A路由配置
│   │   └── project-b/      # 项目B（结构同上）
│   ├── router/             # 主路由配置
│   │   └── index.ts        # 主路由配置文件
│   ├── views/              # 主视图
│   │   └── ProjectSelectionView.vue # 项目选择视图
│   ├── App.vue             # 应用入口组件
│   ├── main.ts             # 应用入口文件
│   ├── style.css           # 全局样式
│   └── vite-env.d.ts       # Vite环境类型声明
├── index.html              # HTML模板
├── package.json            # 项目配置和依赖
├── tsconfig.json           # TypeScript配置
├── tsconfig.app.json       # 应用TypeScript配置
├── tsconfig.node.json      # Node TypeScript配置
└── vite.config.ts          # Vite配置
```

## 名片项目的独立性和组织结构

### 名片项目的独立性原则

平台采用了高度模块化的设计理念，每个名片项目都是完全独立的，拥有自己的资源、组件、路由和业务逻辑。这种独立性确保了：

1. **隔离性**：各个名片项目之间的代码互不影响，可以独立开发和维护
2. **可扩展性**：可以轻松添加新的名片项目，而不会影响现有项目
3. **并行开发**：不同团队可以同时开发不同的名片项目
4. **技术多样性**：各个名片项目可以根据需求使用不同的UI风格和组件库

### 名片项目的标准结构

每个名片项目应遵循以下标准结构，以确保一致性和可维护性：

```
src/cards/[项目名称]/
├── assets/             # 项目特有的资源文件
│   ├── icons/          # 图标资源
│   ├── images/         # 图片资源
│   └── styles/         # 样式文件
├── components/         # 项目特有的组件
│   ├── layout/         # 布局组件
│   ├── common/         # 通用组件
│   └── business/       # 业务组件
├── views/              # 页面视图
│   ├── HomeView.vue    # 首页
│   └── [其他页面].vue  # 其他功能页面
├── router.ts           # 项目路由配置
├── types/              # 类型定义
├── utils/              # 工具函数
└── vite-env.d.ts       # 环境类型声明
```

### 名片项目的文件放置规则

1. **资源文件**：
   - 项目特有的图片、图标应放在 `assets/` 目录下
   - 推荐使用云存储服务（如 `https://pic.sdtaa.com/`）存储大型或共享资源
   - 静态资源的路径应使用相对路径引用，如 `./assets/icons/icon.png`

2. **组件**：
   - 通用组件应放在 `components/common/` 目录下
   - 布局相关组件应放在 `components/layout/` 目录下
   - 业务组件应放在 `components/business/` 目录下
   - 组件命名应遵循 PascalCase 规范（如 `NavigationBar.vue`）

3. **视图**：
   - 所有页面视图应放在 `views/` 目录下
   - 视图命名应以功能命名，并以 View 结尾（如 `ProductsView.vue`）
   - 视图组件中应避免复杂的业务逻辑，业务逻辑应抽离到独立的组件或服务中

4. **路由**：
   - 每个名片项目必须有一个 `router.ts` 文件定义其路由
   - 路由配置应使用懒加载模式 `() => import('./views/XXXView.vue')`
   - 路由元信息应包括页面标题、favicon等

5. **类型定义**：
   - 项目特有的类型定义应放在 `types/` 目录下
   - 类型文件应以 `.d.ts` 或 `.ts` 为扩展名
   - 共享类型可以考虑放在项目根目录的 `src/types/` 下

6. **样式**：
   - 全局样式应放在项目根目录的 `src/style.css` 中
   - 项目特有的样式应放在 `assets/styles/` 目录下
   - 组件样式应优先使用 `<style scoped>` 局部样式

### 名片项目的集成方式

1. **路由集成**：
   在主路由配置文件 `src/router/index.ts` 中，通过以下方式集成各个名片项目的路由：

   ```typescript
   // 导入各个名片项目的路由
   import projectARoutes from '../cards/project-a/router'
   import projectBRoutes from '../cards/project-b/router'
   
   const router = createRouter({
     history: createWebHistory(),
     routes: [
       // 主页路由 - 项目选择页面
       {
         path: '/',
         name: 'projectSelection',
         component: () => import('../views/ProjectSelectionView.vue')
       },
       // 将项目A的路由添加到 /card/project-a 前缀下
       ...projectARoutes.map(route => ({
         ...route,
         path: `/card/project-a${route.path}`
       })),
       // 将项目B的路由添加到对应前缀下
       ...projectBRoutes.map(route => ({
         ...route,
         path: `/card/project-b${route.path}`
       }))
     ]
   })
   ```

2. **项目入口卡片**：
   在项目选择页面 `src/views/ProjectSelectionView.vue` 中添加对应的项目卡片：

   ```vue
   <div class="project-card" @click="navigateTo('/card/project-name')">
     <img src="项目logo路径" alt="项目名称" class="project-icon">
     <h2>项目名称</h2>
     <p>项目简介</p>
   </div>
   ```

## 核心功能模块

### 1. 项目选择模块 (`src/views/ProjectSelectionView.vue`)

该视图是应用的入口点，提供了项目选择界面，用户可以从这里选择要访问的名片项目。

### 2. 路由系统 (`src/router/`)

路由系统采用Vue Router实现，支持多项目的嵌套路由结构：
- 主路由配置在`src/router/index.ts`
- 各名片项目路由在各自的`router.ts`文件中配置
- 通过路径前缀（如`/card/project-name`）进行命名空间隔离

## 特点和优势

1. **模块化架构**：项目采用高度模块化的设计，便于维护和扩展
2. **多项目支持**：通过卡片和路由系统支持多个项目的展示和管理
3. **响应式设计**：适配不同设备屏幕，提供良好的用户体验
4. **云端资源**：使用云服务器存储静态资源，如logo、图片等
5. **类型安全**：使用TypeScript确保代码的类型安全

## 未来拓展方向

1. 添加更多项目卡片
2. 引入状态管理系统（如Pinia）处理复杂状态
3. 实现更多互动功能和动画效果
4. 优化性能和加载速度
5. 增强AI功能集成

## 开发指南

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

### 添加新名片项目的步骤

1. 在 `src/cards/` 目录下创建新项目文件夹，例如 `new-project/`
2. 按照标准结构创建项目目录：
   ```
   mkdir -p src/cards/new-project/{assets,components,views,types,utils}
   ```
3. 创建项目路由配置文件 `src/cards/new-project/router.ts`，示例内容：
   ```typescript
   import { RouteRecordRaw } from 'vue-router'
   
   const routes: RouteRecordRaw[] = [
     {
       path: '/',
       name: 'newProjectHome',
       component: () => import('./views/HomeView.vue'),
       meta: { 
         favicon: 'https://example.com/favicon.png',
         title: '新项目 - 首页'
       }
     },
     // 添加更多路由...
   ]
   
   export default routes
   ```
4. 在 `src/router/index.ts` 中引入并注册新项目路由：
   ```typescript
   import newProjectRoutes from '../cards/new-project/router'
   
   // 在routes数组中添加
   ...newProjectRoutes.map(route => ({
     ...route,
     path: `/card/new-project${route.path}`
   })),
   ```
5. 在 `src/views/ProjectSelectionView.vue` 中添加新项目卡片：
   ```html
   <div class="project-card" @click="navigateTo('/card/new-project')">
     <img src="项目logo路径" alt="新项目" class="project-icon">
     <h2>新项目</h2>
     <p>新项目简介</p>
   </div>
   ```
6. 创建项目首页视图 `src/cards/new-project/views/HomeView.vue`

## 部署说明

项目使用Vite构建工具，构建后的静态文件可部署在任何支持静态网站的服务器上。构建命令为`npm run build`，构建产物将输出到`dist`目录。 

## 公共组件库

### 图片查看器组件 (ImageViewer)

#### 组件概述

ImageViewer是一个可复用的图片查看组件，提供图片的全屏预览、缩放和移动功能。该组件被设计为可在整个项目中重复使用，避免在每个需要图片预览功能的页面中重复编写相似代码。

#### 组件位置

```
src/components/ImageViewer.vue
```

#### 功能特性

- **全屏预览**：点击图片后全屏显示
- **缩放功能**：支持放大和缩小图片
- **移动功能**：支持拖动图片查看不同区域
- **触摸支持**：完全支持移动设备的触摸手势
  - 单指拖动：移动图片
  - 双指缩放：放大/缩小图片
- **历史记录处理**：正确处理浏览器返回按钮，关闭预览而不是返回上一页
- **UI控制**：底部工具栏提供放大、缩小和关闭按钮

#### 组件实现

组件使用Vue 3的Composition API实现，主要包含以下部分：

1. **Props定义**：
   - `visible`：控制组件显示/隐藏的布尔值
   - `imageUrl`：要显示的图片URL

2. **事件**：
   - `update:visible`：用于支持v-model双向绑定
   - `close`：关闭事件

3. **交互处理**：
   - 触摸事件处理（开始、移动、结束）
   - 缩放控制
   - 浏览器历史状态管理

4. **样式设计**：
   - 固定定位的全屏覆盖层
   - 底部工具栏
   - 响应式设计适配不同设备

#### 使用方法

1. **导入组件**：

```javascript
import ImageViewer from '../../components/ImageViewer.vue'  // 根据实际路径调整
```

2. **添加状态变量**：

```javascript
// 图片查看器相关状态
const imageViewerVisible = ref(false)
const currentImage = ref('')
```

3. **创建打开图片查看器的方法**：

```javascript
// 打开图片查看器
const openImageViewer = (imageUrl: string) => {
  currentImage.value = imageUrl
  imageViewerVisible.value = true
}
```

4. **在模板中使用组件**：

```html
<!-- 为需要预览的图片添加点击事件 -->
<img src="图片路径" @click="openImageViewer('图片路径')" />

<!-- 在模板底部添加组件 -->
<ImageViewer 
  v-model:visible="imageViewerVisible" 
  :image-url="currentImage"
/>
```

#### 使用示例

以下是在页面中使用ImageViewer组件的完整示例：

```vue
<script setup lang="ts">
import { ref } from 'vue'
import ImageViewer from '../../components/ImageViewer.vue'

// 图片查看器相关状态
const imageViewerVisible = ref(false)
const currentImage = ref('')

// 打开图片查看器
const openImageViewer = (imageUrl: string) => {
  currentImage.value = imageUrl
  imageViewerVisible.value = true
}
</script>

<template>
  <div class="gallery">
    <img 
      src="https://example.com/image1.jpg" 
      @click="openImageViewer('https://example.com/image1.jpg')" 
      alt="示例图片1"
    />
    <img 
      src="https://example.com/image2.jpg" 
      @click="openImageViewer('https://example.com/image2.jpg')" 
      alt="示例图片2"
    />
  </div>
  
  <!-- 使用图片查看器组件 -->
  <ImageViewer 
    v-model:visible="imageViewerVisible" 
    :image-url="currentImage"
  />
</template>
```

#### 注意事项

1. 确保正确设置组件的导入路径，根据实际项目结构调整
2. 组件依赖Element Plus的图标组件，确保已正确安装和导入
3. 组件使用了Vue 3的Composition API特性，不兼容Vue 2
4. 在移动设备上测试触摸手势功能，确保良好的用户体验

#### 已应用该组件的页面

目前，ImageViewer组件已在以下页面中应用：

- `src/cards/houri-capital/views/CapitalView.vue`
- `src/cards/houri-capital/views/AIDrivenView.vue`

这些页面可作为组件使用的参考示例。 