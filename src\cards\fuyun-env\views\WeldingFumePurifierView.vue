<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 产品技术参数
const technicalSpecs = reactive([
  { model: 'FHY-1000', airflow: '1000', power: '0.75', voltage: '380/220', pressure: '0.5-0.6', area: '12', efficiency: '≥99.9', noise: '65', dimensions: '650×650×1300' },
  { model: 'FHY-1200', airflow: '1200', power: '1.5', voltage: '380/220', pressure: '0.5-0.6', area: '15', efficiency: '≥99.9', noise: '68', dimensions: '720×750×1450' },
  { model: 'FHY-1850', airflow: '1850', power: '2.2', voltage: '380', pressure: '0.5-0.6', area: '24', efficiency: '≥99.9', noise: '75', dimensions: '750×780×1600' },
  { model: 'FHY-2800', airflow: '2800', power: '3', voltage: '380', pressure: '0.5-0.6', area: '32', efficiency: '≥99.9', noise: '75', dimensions: '800×820×1680' }
])

// 性能特点
const productFeatures = reactive([
  {
    title: '灵活配置',
    description: '可根据用户工况，选择不同配置，如单级过滤、二级过滤、手动清灰、自动清灰等，是一种全新的烟尘净化设备。'
  },
  {
    title: '结构紧凑',
    description: '除尘器结构设计紧凑，体积小，现场摆置方便，配有四个脚轮(后两个脚轮配有脚刹)，移动灵活。'
  },
  {
    title: '自动清灰',
    description: 'PLC或单片机执行滤筒自动反吹清灰，自动化程度高。'
  },
  {
    title: '多臂配置',
    description: '不同要求，可配单臂，双臂。吸气臂可360°旋转，可空中上下、左右且自由悬停。'
  },
  {
    title: '柔性吸气臂',
    description: '可选配2m、3m或4m柔性吸气臂。吸气软管采用耐温、阻燃、耐磨复合材料，使用寿命长。'
  },
  {
    title: '高效过滤',
    description: '进口覆膜滤料或纳米级阻燃材料，过滤性能好，使用寿命长，使除尘效率达到99.9%以上，达到国家规定的室内排放标准。'
  },
  {
    title: '便捷维护',
    description: '过滤筒分为水平安装和竖立安装两种形式，操作维护方便，滤芯更换简单，即插即用。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>焊接烟尘净化器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/adc579eb-40a2-41a6-9179-e8306a50f14b.jpg" alt="焊接烟尘净化器" />
        </div>
        <div class="hero-content">
          <h2>焊接烟尘净化器</h2>
          <p class="product-subtitle">专业焊接烟尘净化设备，高效过滤焊接产生的有害烟雾和颗粒物</p>
          <div class="product-intro">
            <p>焊接及相关工艺过程会产生大量的有害物质，并以烟尘的形式存在于空气中，只有通过焊接烟尘净化器过滤后的清洁空气，并符合国家安全环境保护部门的排放标准，才能排放到车间或其它工作区域。</p>
            <p>设备通过吸气罩吸收的焊接烟尘，烟尘通过吸气臂进入过滤单元内部，首先撞击分流板，改变气流方向，使气流向上流动，这样可避免直接冲击滤芯，也得到了循环，大颗粒的粉尘在被过滤筒收集前先分离出来，细小颗粒经过有高效滤膜的过滤筒过滤分离，过滤后干净的空气通过消声后排入外界，完成过滤的全过程。带有高效滤膜的过滤筒分离的烟尘颗粒，在脉冲反吹作用下，粉尘落入粉尘容器，进行收集。</p>
          </div>
        </div>
      </div>

      <!-- 性能特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          性能特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术参数
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>处理风量<br/>(m³/h)</th>
                <th>电机功率<br/>(KW)</th>
                <th>电压<br/>(V)</th>
                <th>清尘气压<br/>(MPa)</th>
                <th>过滤面积<br/>(m²)</th>
                <th>过滤效率</th>
                <th>噪音<br/>dB(A)</th>
                <th>设备体积<br/>(L×W×H)mm</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.power }}</td>
                <td>{{ spec.voltage }}</td>
                <td>{{ spec.pressure }}</td>
                <td>{{ spec.area }}</td>
                <td>{{ spec.efficiency }}</td>
                <td>{{ spec.noise }}</td>
                <td class="dimensions-cell">{{ spec.dimensions }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🌪️</div>
            <div class="indicator-content">
              <h4>除尘效率</h4>
              <div class="indicator-value">99.9%+</div>
              <p>进口覆膜滤料</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">⚡</div>
            <div class="indicator-content">
              <h4>处理风量</h4>
              <div class="indicator-value">1000-2800</div>
              <p>m³/h大范围覆盖</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔧</div>
            <div class="indicator-content">
              <h4>清灰方式</h4>
              <div class="indicator-value">脉冲反吹</div>
              <p>自动清灰系统</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔄</div>
            <div class="indicator-content">
              <h4>吸气臂</h4>
              <div class="indicator-value">360°</div>
              <p>旋转自由悬停</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">📏</div>
            <div class="indicator-content">
              <h4>臂长选择</h4>
              <div class="indicator-value">2-4m</div>
              <p>柔性吸气臂</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔇</div>
            <div class="indicator-content">
              <h4>噪音控制</h4>
              <div class="indicator-value">65-75dB</div>
              <p>低噪音运行</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <div class="advantage-content">
              <h4>高效净化</h4>
              <p>采用多级过滤技术，能够有效去除焊接烟雾中的金属颗粒和有害物质，除尘效率达到99.9%以上，保护操作人员健康。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🚀</div>
            <div class="advantage-content">
              <h4>移动灵活</h4>
              <p>配有四个脚轮设计，后两个脚轮配有脚刹，移动灵活便捷，可根据工作需要随时调整位置，适应不同的作业环境。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🤖</div>
            <div class="advantage-content">
              <h4>智能控制</h4>
              <p>PLC或单片机执行滤筒自动反吹清灰，自动化程度高，可实现智能启停，减少人工干预，提高工作效率。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🛠️</div>
            <div class="advantage-content">
              <h4>维护简便</h4>
              <p>过滤筒分为水平安装和竖立安装两种形式，操作维护方便，滤芯更换简单，即插即用，大大降低维护成本。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications">
          <div class="application-item">
            <div class="app-icon">🔥</div>
            <h4>焊接车间</h4>
            <p>各类焊接作业产生的烟尘净化处理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🔧</div>
            <h4>维修作业</h4>
            <p>设备维修焊接过程中的烟尘控制</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🏭</div>
            <h4>小型加工厂</h4>
            <p>中小型金属加工企业的烟尘治理</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🔬</div>
            <h4>实验室焊接</h4>
            <p>科研院所实验室焊接烟尘净化</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🚢</div>
            <h4>造船行业</h4>
            <p>船舶制造焊接作业烟尘处理</p>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.3;
  font-size: 0.7rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.65rem;
  min-width: 1200px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.75rem 0.4rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.6rem;
  line-height: 1.2;
}

.specs-table td {
  padding: 0.75rem 0.4rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.65rem;
}

.dimensions-cell {
  font-size: 0.55rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

.specs-table tbody tr:last-child td {
  border-bottom: none;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 产品优势 */
.advantages {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 应用场景 */
.applications {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.application-item {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
}

.application-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .feature-card p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .specs-table {
    font-size: 0.75rem;
  }

  .specs-table th {
    padding: 1rem 0.6rem;
    font-size: 0.7rem;
  }

  .specs-table td {
    padding: 1rem 0.6rem;
    font-size: 0.75rem;
  }

  .dimensions-cell {
    font-size: 0.65rem;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .advantages {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .application-item {
    padding: 1.5rem;
  }

  .application-item h4 {
    font-size: 1.1rem;
  }

  .application-item p {
    font-size: 0.9rem;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .applications {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.8rem;
  }

  .specs-table th {
    font-size: 0.75rem;
  }

  .specs-table td {
    font-size: 0.8rem;
  }

  .dimensions-cell {
    font-size: 0.7rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }
}
</style>
