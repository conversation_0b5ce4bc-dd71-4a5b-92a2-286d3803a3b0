<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  // 返回到案例中心
  window.location.href = '/card/wanwang-tech/case-center'
}

// 复制网站链接
const copyWebsiteLink = () => {
  const websiteUrl = 'http://www.srlyjt.com/'
  navigator.clipboard.writeText(websiteUrl).then(() => {
    alert('网站链接已复制到剪贴板')
  }).catch(() => {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = websiteUrl
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    alert('网站链接已复制到剪贴板')
  })
}

// 访问网站
const visitWebsite = () => {
  window.open('http://www.srlyjt.com/', '_blank')
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

console.log('ShangRaoWenLvView 组件已加载')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>上饶文旅集团官网</h1>
    </div>

    <div class="content">
      <!-- 网站快照 -->
      <div class="section snapshot-section">
        <div class="snapshot-card">
          <div class="snapshot-image">
            <img 
              src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoWenLv.jpg" 
              alt="上饶文旅集团官网快照"
            />
          </div>
        </div>
      </div>

      <!-- 项目标签 -->
      <div class="section tags-section">
        <div class="tags-container">
          <div class="tag">文旅融合</div>
          <div class="tag">品牌官网</div>
          <div class="tag">大美上饶</div>
        </div>
      </div>

      <!-- 项目概述 -->
      <div class="section intro-section">
        <div class="section-header">
          <h2>项目概述</h2>
        </div>
        <div class="intro-content">
          <p>为上饶文旅集团量身定制的官方品牌门户网站。网站以"高铁枢纽，大美上饶"为主题，全面展示了集团在旅游景区开发、文化项目投资、酒店运营管理等方面的产业布局和辉煌成就。</p>
        </div>
      </div>

      <!-- 设计理念与技术实现 -->
      <div class="section design-section">
        <div class="section-header">
          <h2>设计理念与技术实现</h2>
        </div>
        <div class="design-content">
          <div class="design-item">
            <h3>设计理念</h3>
            <p>网站采用全屏视频和大幅高清图片作为背景，给用户带来身临其境的沉浸式视觉体验。设计上将上饶的山水之美与集团的现代发展理念相结合，极具感染力。</p>
          </div>
          <div class="design-item">
            <h3>技术实现</h3>
            <p>网站对视频和高清图片资源进行了极致的加载优化，确保在提供震撼视觉效果的同时，不牺牲访问速度。后台系统支持对旗下各个文旅项目的独立内容管理。</p>
          </div>
        </div>
      </div>

      <!-- 在线访问 -->
      <div class="section access-section">
        <div class="section-header">
          <h2>在线访问</h2>
        </div>
        <div class="access-content">
          <!-- 桌面端：单个访问按钮 -->
          <div v-if="!isMobile" class="desktop-buttons">
            <button @click="visitWebsite" class="visit-btn">
              点击访问网站
            </button>
          </div>
          
          <!-- 移动端：两个按钮 -->
          <div v-if="isMobile" class="mobile-buttons">
            <button @click="visitWebsite" class="visit-btn mobile-visit">
              访问网站
            </button>
            <button @click="copyWebsiteLink" class="copy-btn mobile-copy">
              复制网站链接
            </button>
            <div class="mobile-tip">
              <p>复制链接到浏览器打开，或直接访问网站</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  width: 100%;
}

.header {
  background: #1693d2;
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1693d2;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #1693d2;
}

/* 网站快照样式 */
.snapshot-card {
  text-align: center;
}

.snapshot-image {
  max-width: 100%;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.snapshot-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* 项目标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.tag {
  background: linear-gradient(135deg, #1693d2 0%, #0f7bb8 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-weight: 500;
  font-size: 0.9rem;
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(22, 147, 210, 0.4);
}

/* 项目概述样式 */
.intro-content p {
  color: #333;
  line-height: 1.8;
  font-size: 1.1rem;
  text-align: justify;
  margin: 0;
}

/* 设计理念与技术实现样式 */
.design-content {
  display: grid;
  gap: 2rem;
}

.design-item h3 {
  color: #1693d2;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  border-left: 4px solid #1693d2;
  padding-left: 1rem;
}

.design-item p {
  color: #333;
  line-height: 1.7;
  font-size: 1rem;
  text-align: justify;
  margin: 0;
}

/* 在线访问样式 */
.access-content {
  text-align: center;
}

.desktop-buttons {
  display: flex;
  justify-content: center;
}

.mobile-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.visit-btn {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  color: white;
  border: none;
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 3rem;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.visit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(76, 175, 80, 0.4);
}

.copy-btn {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  border: none;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 3rem;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.copy-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 152, 0, 0.4);
}

.mobile-visit {
  width: 100%;
  max-width: 280px;
}

.mobile-copy {
  width: 100%;
  max-width: 280px;
  background: linear-gradient(135deg, #1693d2 0%, #0f7bb8 100%);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.3);
}

.mobile-copy:hover {
  box-shadow: 0 12px 35px rgba(22, 147, 210, 0.4);
}

.mobile-tip {
  margin-top: 1rem;
}

.mobile-tip p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .header h1 {
    font-size: 1rem;
  }

  .tags-container {
    gap: 0.5rem;
  }

  .tag {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .design-content {
    gap: 1.5rem;
  }

  .design-item h3 {
    font-size: 1.1rem;
  }

  .visit-btn {
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }

  .copy-btn {
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }

  .mobile-buttons {
    gap: 0.8rem;
  }
}
</style>
